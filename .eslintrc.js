/* eslint-disable */
module.exports = {
  env: {
    'react-native/react-native': true,
    es2020: true
  },
  extends: ['plugin:react/recommended', 'eslint:recommended', 'plugin:react-hooks/recommended', 'prettier'],
  globals: {
    Atomics: 'readonly',
    SharedA<PERSON>y<PERSON>uffer: 'readonly',
    URL: 'readonly'
  },
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
      legacyDecorators: true
    },
    ecmaVersion: 11,
    sourceType: 'module'
  },
  plugins: ['react', 'react-native'],
  parser: '@babel/eslint-parser',
  settings: {
    react: {
      version: 'detect'
    }
  },
  rules: {
    'react-native/no-unused-styles': 2,
    'react-native/no-raw-text': 2,
    'react-native/no-single-element-style-arrays': 2,
    semi: ['error', 'always'],
    quotes: ['error', 'single', { avoidEscape: true }],
    'jsx-quotes': ['error', 'prefer-single'],
    'react/prop-types': [0],
    'semi-style': ['error', 'last'],
    'space-before-function-paren': [
      'error',
      {
        anonymous: 'always',
        named: 'never',
        asyncArrow: 'always'
      }
    ],
    'no-unused-vars': ['error', { args: 'none' }],
    'react/jsx-filename-extension': [1, { extensions: ['.js', '.jsx', '.tsx'] }],
    'new-cap': 'error',
    'no-var': 'error',
    curly: 'error'
  },
  overrides: [
    {
      files: ['*.ts', '*.tsx'],
      extends: ['plugin:@typescript-eslint/eslint-recommended', 'plugin:@typescript-eslint/recommended'],
      parser: '@typescript-eslint/parser',
      plugins: ['@typescript-eslint']
    }
  ],
  ignorePatterns: ['node_modules/*', 'build/*', 'dist/*', 'assets/*', 'android/*', 'ios/*', 'artifacts/*', 'coverage/*']
};
