{"main": "./src/App.tsx", "sideEffects": ["./src/App.tsx"], "license": "UNLICENSED", "scripts": {"start": "expo start", "fstart": "APP=fishMeet yarn start", "prebuild": "expo prebuild --clean", "fprebuild": "APP=fishMeet yarn prebuild", "android": "expo run:android", "fandroid": "APP=fishMeet yarn android", "ios": "expo run:ios", "fios": "APP=fishMeet yarn ios", "eject": "expo eject", "release": "eas build -p all --auto-submit", "preview": "yarn prebuild && eas build -p all --profile preview", "test": "NODE_OPTIONS=--experimental-vm-modules jest --watch --coverage=false --changedSince=origin/master", "test:debug": "NODE_OPTIONS=--experimental-vm-modules jest -o --watch --coverage=false", "test:final": "NODE_OPTIONS=--experimental-vm-modules jest", "e2ei": "npx detox test -c ios.sim.release --record-logs all", "e2ea": "npx detox test -c android.emu.release --record-logs all", "updateSnapshots": "jest -u --coverage=false", "format": "prettier --write . --ignore-path=.gitignore --cache --log-level=warn", "format:check": "prettier --check . --ignore-path=.gitignore --cache --log-level=warn", "tsc": "tsc --noEmit", "lint": "yarn tsc && eslint --fix . --cache --cache-location node_modules/eslint/.cache/", "lint:check": "yarn tsc && eslint . --cache --cache-location node_modules/eslint/.cache/", "pr-check": "node ./scripts/findMissingTranslations.js && yarn lint:check && yarn format:check && yarn test:final", "update": "yarn pr-check && eas update --branch production --auto", "fupdate": "yarn pr-check && eas update --branch fproduction --auto", "eas-build-pre-install": "corepack enable", "postinstall": "patch-package", "chinaBuild": "Country=China yarn prebuild && eas build -p android --profile china"}, "jest": {"preset": "jest-expo", "moduleNameMapper": {"lodash-es": "lodash"}, "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg|@rneui|lodash-es)"], "setupFiles": ["./jestSetupFile.js", "./node_modules/react-native-gesture-handler/jestSetup.js"], "testPathIgnorePatterns": ["<rootDir>/e2e"], "collectCoverage": true, "collectCoverageFrom": ["<rootDir>/src/**/*.{js,jsx,ts,tsx}"]}, "dependencies": {"@amplitude/react-native": "2.17.3", "@braintree/sanitize-url": "7.0.0", "@config-plugins/react-native-webrtc": "^10.0.0", "@expo/react-native-action-sheet": "^4.0.1", "@expo/vector-icons": "^14.0.3", "@giphy/react-native-sdk": "^3.3.1", "@jitsi/react-native-sdk": "https://github.com/gracetech-services/gt-jitsisdk/raw/d9c510ee1729e7e4b8876bd501d840fbd0ebf0bc/packages/jitsi-react-native-sdk-0.1.0.tgz", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-clipboard/clipboard": "1.15.0", "@react-native-community/datetimepicker": "8.2.0", "@react-native-community/netinfo": "11.4.1", "@react-native-community/slider": "4.5.5", "@react-native-google-signin/google-signin": "10.1.0", "@react-native-picker/picker": "2.9.0", "@react-native-seoul/masonry-list": "^1.4.2", "@react-native/metro-config": "0.76.5", "@react-navigation/bottom-tabs": "6.5.8", "@react-navigation/elements": "^1.3.31", "@react-navigation/native": "6.1.7", "@react-navigation/native-stack": "^6.9.12", "@rneui/base": "^4.0.0-rc.8", "@rneui/themed": "^4.0.0-rc.8", "@sentry/react-native": "~6.3.0", "@shopify/flash-list": "1.7.3", "Base64": "^1.2.0", "axios": "^1.7.8", "axios-retry": "^4.5.0", "chinese-to-pinyin": "^1.3.1", "create-react-class": "^15.7.0", "expo": "~52.0.36", "expo-asset": "~11.0.4", "expo-av": "~15.0.2", "expo-build-properties": "~0.13.2", "expo-camera": "~16.0.17", "expo-clipboard": "~7.0.1", "expo-config-plugin-ios-share-extension": "^0.0.4", "expo-constants": "~17.0.7", "expo-device": "~7.0.2", "expo-document-picker": "~13.0.3", "expo-file-system": "~18.0.11", "expo-font": "~13.0.4", "expo-image": "~2.0.6", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-linking": "~7.0.5", "expo-localization": "~16.0.1", "expo-media-library": "~17.0.6", "expo-notifications": "~0.29.13", "expo-print": "~14.0.3", "expo-screen-orientation": "~8.0.4", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-task-manager": "~12.0.5", "expo-updates": "~0.26.19", "expo-web-browser": "~14.0.2", "i18next": "23.11.5", "inversify": "^6.0.2", "link-preview-js": "^3.0.4", "lodash-es": "^4.17.21", "qrcode-base64": "^1.0.1", "react": "18.3.1", "react-native": "0.76.7", "react-native-background-timer": "2.4.1", "react-native-calendar-events": "2.2.0", "react-native-collapsible": "^1.6.0", "react-native-default-preference": "1.4.4", "react-native-device-info": "10.9.0", "react-native-flash-message": "^0.4.1", "react-native-gesture-handler": "2.20.2", "react-native-get-random-values": "1.11.0", "react-native-gifted-chat": "~2.4.0", "react-native-image-viewing": "^0.2.2", "react-native-immersive-mode": "2.0.1", "react-native-keep-awake": "4.0.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal": "^13.0.0", "react-native-modal-datetime-picker": "^14.0.1", "react-native-orientation-locker": "1.6.0", "react-native-pager-view": "6.5.1", "react-native-paper": "5.10.3", "react-native-parsed-text": "^0.0.22", "react-native-performance": "5.0.0", "react-native-radio-buttons-group": "^3.1.0", "react-native-reanimated": "~3.16.1", "react-native-receive-sharing-intent": "^2.0.0", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-sound": "0.11.2", "react-native-splash-screen": "3.3.0", "react-native-svg": "15.8.0", "react-native-url-polyfill": "2.0.0", "react-native-video": "6.8.2", "react-native-watch-connectivity": "1.1.0", "react-native-webrtc": "124.0.4", "react-native-webview": "13.12.5", "reflect-metadata": "^0.2.2", "socket.io-client": "^2.5.0", "text-encoding": "0.7.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/eslint-parser": "^7.21.3", "@babel/preset-typescript": "^7.16.5", "@babel/runtime": "^7.27.0", "@config-plugins/detox": "^9.0.0", "@expo/config-plugins": "~9.0.0", "@jest/globals": "^29.0.3", "@testing-library/react-native": "^12.9.0", "@types/babel__core": "^7", "@types/jest": "^29.5.12", "@types/lodash-es": "^4.17.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "babel-plugin-module-resolver": "^5.0.0", "babel-plugin-transform-typescript-metadata": "^0.3.2", "babel-preset-expo": "~12.0.0", "detox": "^20.19.3", "eslint": "^8.36.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-native": "^4.0.0", "expo-dev-client": "~5.0.12", "jest": "~29.7.0", "jest-expo": "~52.0.4", "patch-package": "^8.0.0", "prettier": "^3.3.3", "react-test-renderer": "18.3.1", "typescript": "~5.3.3", "typescript-eslint": "^7.7.1"}, "resolutions": {"@xmldom/xmldom": "0.8.7"}, "packageManager": "yarn@4.7.0", "expo": {"doctor": {"reactNativeDirectoryCheck": {"enabled": false}}}}