{"UserScreen": {"IConfirm": "I confirm", "DeleteAcctMsg": "You are deleting the following user account from iDigest system, by doing so you will permanently lose your user data and related information.", "DeleteAcct": "Delete Account"}, "HomeScreen": {"Create": "Help and Feedback", "MyStudyTitle": "My Study", "MyChannelsTitle": "My Channels", "More": "More", "LatestReleases": "Latest Releases", "MoreExciting": "More Exciting", "MyBible": "My Bible", "BibleBooks": "66 Books", "BibleCatalog": "Catalog & Search", "CurrentLocation": "Current Location", "DownloadingBible": "Downloading Bible data...", "DownloadFailed": "Download Failed", "DownloadFailedMsg": "Unable to download default Bible version ({value}), please check your network connection and try again.", "Error": "Error", "ErrorCheckingBible": "Error checking/downloading Bible"}, "HelpMeScreen": {"HowToGetHelp": "How to Contact Customer Support", "GetHelpInstruction": "For any questions about App features, feedback for improvement, or any violations from other users, please click \"Contact Support\" button below", "Contactsupport": "Contact Support", "HowToClassCreate": "How to Create a Class", "websiteTitle": "Class Development Website", "openOnComputer": ", please open it on your computer.", "videoDescription": "The following video demonstrates how to create and edit your own class on the computer's web page and make it visible to other users.", "copySuccess": "Copy successful! "}, "CreateAccountScreen": {"EmailTip": "(It is better to use a real email,\nin case you forget password,\nthe system can help you retrieve it through the email)", "NameTip": "(Choose your favorite name)"}, "LessonTableScreen": {"Download": "Download Class", "OnlineModeMessage": "The class is in online mode, content size: {value}", "CancelDownload": "Cancel download", "DownloadingMessage": "Downloading class content...", "DownloadingMediaMessage": "Downloading media resource '{value}'...", "DeleteDownload": "Delete local", "DownloadCompleteMessage": "The class is in offline mode, content size: {value}", "DeleteDialogMessage": "After the local copy is deleted, you will not be able to use the class without internet connection. Your original class notes and answers will not be impacted.", "DownloadDialogMessage": "After a local copy is downloaded, you can use the class without internet connection. Your original class notes and answers will not be impacted.", "DeleteDialogTitle": "Delete Local Copy?", "DownloadDialogTitle": "Download to Local?", "DownloadFailed": "Download Failed", "DownloadSuccess": "Download successful", "FileSizeProcess": "\nCurrent file: {value}"}, "LoginScreen": {"HeaderTitle": "Welcome to FishMeet!\nPlease log in or sign up.", "HeaderExplanation": "If you already have an iDigest account, you can use the same information to log in.", "ForgotPwd": "Forgot Password?", "CreateNew": "Create New", "OtherLogin": "Other ways to log in"}, "ForgotPasswordScreen": {"HeaderTitle": "Forgot your password?", "HeaderExplanation": "Enter your email below to recover your account."}, "DebugScreen": {"Title": "Debug"}, "UpdateNameScreen": {"Title": "Update Display Name"}, "SendFeedbackScreen": {"Submit": "Submit", "Title": "Contact Support", "ContactUs": "Please fill in the text box below, and click \"Submit\". iDigest customer support will create a new group in Fellowship and communicate with you privately", "Success": "Succeeded!\n\nThank you for contacting iDigest customer support! We will communicate with you in a new group in Fellowship"}, "NoteScreen": {"Title": "Notes"}, "GroupMemberSelectScreen": {"Title": "Select member"}, "MemberInviteScreen": {"Title": "Add members"}, "SelectGroupOrUserScreen": {"NoFriend": "No friend added yet"}, "GroupProgressListScreen": {"GroupNotTakenClass": "Your current group has not taken any class yet. Please go to the class screen to add a class to your group.", "AsMembersGoThroughDiscussion": "As your group members go through the discussion questions in the app, you will be able to see your group's progress here."}, "MyInfoScreen": {"Title": "Account number and QR Code", "Account": "Account number:"}, "MemberScreen": {"Title": "User info", "ID": "ID", "UserTag": "User tag", "BlockUser": "Block this user", "BlockUserText": "You will not have 1:1 communciation with this user, nor see the user info.", "NonSpaceUserTag": "The user tag must contain at least one non-space character", "SetTagSuccessed": "Set tag successed", "SetTagFailed": "Failed to set tag", "NoTag": "No tag", "SetBlockSuccessed": "Blocked user modified successfully", "SetBlockFailed": "Failed to modify the blocked user"}, "AcceptContactCardScreen": {"Title": "Contact card", "Connect": "Connect", "GreetingInput": "To add this user into your contact, please enter your greetings, and then click \"connect\""}, "CreateStudyGroupScreen": {"Title": "Create Group", "NoGroupName": "Missing group name!", "CreateGroupFailed": "Create group failed!", "AddMembers": "Add members"}, "GroupScreen": {"Approve": "Approve", "Reject": "Reject"}, "JoinStudyGroupScreen": {"PleaseInputCode": "Please input group invitation code", "InputCodeHint": "(8 digits, no space)"}, "GroupsScreen": {"HeaderTitle": "Thanks for joining us!\nLet's get started.", "HeaderExplanation": "If you already have an invitation link, please open it to join a group.\n\nOtherwise, create your own group and invite members below.", "HeaderExplanationContacts": "If you already have a friend's email or account ID, add a new friend below.\n\nOtherwise, navigate to the 'Groups' page to create or join a group.", "SetTopGroup": "Stick to top", "SetDownGroup": "Remove to top", "Mentioned": "You were mentioned", "DeleteGroupWarning": "You are about to delete the \"{0}\" group, all existing group members will no longer be able to use the group chat, nor will they be able to see the past chat messages, but the individual class answers and notes will be retained.", "DeleteOneOnOneGroupWarning": "You are about to delete 1-1 chat with \"{0}\", you both will lose all the chat history in the past, but you can still find each other in contact list, and start new 1-1 chat.", "DeleteGroup": "Delete group", "LeaveGroup": "Leave group", "NewGroupAndContact": "New group and contact", "CreateGroup": "Create group", "JoinGroup": "Join group", "JoinGroupInstruction": "Join Group\n(Through Group ID)", "ConnectPerson": "Connect a person\n(through email or\niDigest ID)", "ScanQRInstruction": "Scan QR\n(User or\nGroup QR)", "ScanQR": "Scan QR", "LeaveGroupWarning": "You are about to leave the \"{0}\" group. You will no longer be able to see your chat messages from the group, but your class answers and notes will be retained.", "AddFriendAction": "Please click the (+) button to add friends", "CreateGroupAction": "Please click (+) button on the top right to create groups", "DemoteToMember": "Demote to member", "PromoteToLeader": "Promote to leader", "RemoveUser": "Remove user", "Leaders": "Leaders", "Members": "Members", "ShowLess": "Show less...", "ShowMore": "Show more...", "LeaderTools": "Leader <PERSON><PERSON>", "GroupName": "Group name", "MsgSettings": "Message settings", "LocalMsgAutoDisappear": "Local message auto disappear: ", "MsgDisappearHint": "New messages sent to you in this group will disappear after below time. ", "TagMsg": "If you want to keep certain message visible, you can tag \"my favorite\" to the message.", "GroupMsgAutoDelete": "Group message auto delete: ", "MsgDeleteHint": "New messages sent to this group will be completely deleted after below time. ", "MuteNewMsg": "Mute new message notification: ", "MuteDescription": "This is a personal setting, and for this group only. Other members in this group are not affected.", "DeletedMsgHint": "No one in this group will be able to retrieve deleted messages. If you want to preserve a certain message for the group, you can tag “group favorite” to the message, or if you want to save this message for your own only, you can tag \"my favorite\" to the message.", "DeletedMsgHintOneOnOne": "No one in this group will be able to retrieve deleted messages. If you want to save this message for your own only, you can tag \"my favorite\" to the message.", "MsgDisappearWarning": "[WARNING]: All existing messages prior the time will disappear!", "InvitationLink": "Invitation link: ", "ShareGroup": "Share The Group", "ShareGroupHint": "Following information can be used to invite more people to join", "InvitationCode": "Invitation code: ", "CopyToClipboard": "Copied to clipboard", "InvitationFromIDigest": "Friendly Invitation from iDigest", "NotifyInvitation": "Hey, \"iDigest\" user {User}({Email}) invites you to use iDigest App to share the good messages with each other.\n\nTo accept this invitation, please first install iDigest APP, and then in \"fellowship > chat\" screen, click on uppper right (+), and then input the invitation code: {Code}\n\nAlternatively, you may use a browser to open: \n{Url}\n\nBest wishes,\niDigest", "OnlineMeeting": "Online meeting", "InviteToMeeting": "If you want to invite guests to the group meeting, you can click the button below, and copy the meeting URL and passcode into clipboard, then send to the guests you plan to invite. Note, the passcode setting here will not have impact to your group member joining the meeting from app", "CopyMeetingInfo": "Copy meeting info", "MeetingLink": "Meeting link:", "MeetingPwd": "Meeting password:", "Classes": "Classes", "InvitedToGroup": "You've been invited to this group, please respond", "Max120Reached": "End of the list: Only maximum 120 most recently used chat groups is displayed here. To retrieve other items in the entire history, please use search feature.", "WaitingForApproval": "Members waiting to be approved", "RequireLeaderApproval": "Require approval to join this group", "DisableMemberPost": "Disallow member post message", "DisableMemberPostTip": "(Only group leader can post message, regular member can only view leader's message)", "DisableMemberPostMsg": "Regular members are not allowed to post message to the group, only allowed to view messages from leaders.", "HiddenMembersMsg": "Group member info is hidden from regular members", "HiddenMembersTitle": "Hide group member info", "HiddenMembersTip": "(Regular Member can only see group leader info, but not other member info)"}, "ExportScreen": {"Title": "Export", "Lesson": "Lesson", "ContentExport": "The content of this lesson will be exported", "IncludeAnswers": "Include my answers", "Print": "Print", "ExportPDF": "Export PDF"}, "AttendanceHomeScreen": {"RemoveAttendanceRecord": "Remove this attendance record?"}, "AddFriendScreen": {"EmailNotUser": "{email} isn't a registered iDigest user, you invitation is sent in email.", "EnterFriendEmail": "Please enter your friend's email or account ID", "SelfIntro": "Self introduction and greetings"}, "AttendanceRecordScreen": {"AttendanceRecordExist": " attendance record exists, do you want to use this attendance record to overwrite the existing one?", "PickDate": "Pick a date"}, "SettingsScreen": {"Small": "Small", "Medium": "Medium", "Large": "Large", "ExtraLarge": "Extra Large", "AppUpToDate": "Your app is up to date!", "NoUpdate": "No update found", "Avatar": "Avatar", "SetAvatar": "Set Avatar", "MyAccountNoAndQR": "My account ID and QR code", "MyEmail": "My Email", "MyDisplayName": "My Display Name", "FontSize": "Font size", "SelectFontSize": "Please select font size", "DisplayLng": "Display Language", "ChooseLng": "Please choose a language", "DisappearMsgGroup": "Disappearing Messages in Groups", "MeetingTool": "Meeting tool", "Browser": "Browser", "FishMeet": "Fish Meet", "Jitsi": "<PERSON><PERSON><PERSON>", "AppVer": "AppVersion", "ClickToUpdateApp": "Click here to update app", "Donation": "Donation", "Logout": "Log out", "LoginInformation": "Login Information"}, "LoginInfoSettingScreen": {"EmailAndPassword": "Email and Password", "Settings": "Settings", "Link": "Link"}, "UpdatePasswordScreen": {"MyEmail": "My Email", "UpdatePassword": "Update Password", "NewPassword": "New Password", "ConfirmPassword": "Confirm Password", "Settings": "Settings"}, "SelectMeetingToolScreen": {"Browser": "Use browser. Please grant permission to audio and video", "FishMeet": "Use \"渔友会\" App", "DownloadFishMeet": "Download 渔友会", "Jitsi": "Use Jitsi App", "DownloadJitsi": "Download Jitsi"}, "AdvancedSettingsScreen": {"PushNotification": "Push notification", "UseJitsi": "Use Jitsi App for meeting", "InstallJitsi": "Install Jitsi App", "ResetAppData": "Reset app data", "Beta": "Experimental Features (not stable)", "FishMeetEnable": "Use FishMeet App for meeting", "FishMeetDownload": "Download and install FishMeet", "UseJitsiSDK": "Use Jitsi SDK", "Development": "Development", "LogToFile": "DebugLog (affect performance)", "OpenLogFile": "Open DebugLog", "ExperimentalChannelFeature": "Try Channel Feature", "ExperimentalBibleFeature": "Try Bible Feature"}, "SystemInfoScreen": {"Title": "System Info"}, "SelectScreen": {"ChangeLanguageHeaderTitle": "Change app language?", "ChangeLanguageHeaderExplanation": "Choose an available language setting from the list below."}, "ChatScreen": {"AllUsers": "all", "DisableMembersChat": "Regular members are not allowed to post message to the group.", "Save": "Save", "Share": "Share", "SaveSuccess": "Save successful", "SaveFailed": "Save failed"}, "PasswordDoneScreen": {"HeaderTitle": "Your password has been reset!", "HeaderExplanation": "Please check your email and follow the instructions to create a new password for your account."}, "MomentsScreen": {"ShowMore": "Show More", "ShowLess": "Show Less"}, "CreateMomentScreen": {"EditContent": "Content Editing", "PublishScope": "Distribution target", "InvalidUrl": "Invalid URL", "InputTextContent": "Input text content", "ImageOrVideo": "Select pictures or videos", "FileSizeExceeded": "File size exceeds the limit（5MB）", "UploadVideo": "Upload Video", "UploadImage": "Upload Image", "AgreeTerms": "Agreement", "SelectType": "Source Type", "PersonalDevotion": "Personal devotion and testimony", "SharePersonalContent": "Pictures, short videos, and text", "ExternalResourceLink": "External web resources", "ShareExternalResource": "Content from other websites", "NoRestriction": "No limit, everyone can see", "SpecificGroup": "Specific groups, only specified group members can see", "SelectGroup": "Select Group"}, "ChannelDetailScreen": {"ChannelPrefix": "Channel: ", "Subscribe": "Subscribe", "Unsubscribe": "Unsubscribe", "DateFormat": "MMMM DD, YYYY"}, "BibleBooksScreen": {"Title": "Bible 66 Books Catalog and Search", "OldTestament": "Old Testament", "NewTestament": "New Testament", "All": "All"}, "BibleSearchScreen": {"Title": "Bible Search", "SearchHistory": "Search History", "SelectTranslation": "Select Translation", "Cancel": "Cancel", "Searching": "Searching...", "NoResults": "No results found", "LoadMore": "Load more..."}, "BibleSettingScreen": {"Title": "Bible Settings", "Version": "Versions", "Download": "Download", "DownloadSuccess": "Download Successful", "DownloadSuccessMsg": "{value} Bible version has been downloaded successfully.", "DownloadFailed": "Download Failed", "DownloadFailedMsg": "Unable to download {value} Bible version. Please check your network connection and try again.", "DownloadError": "Download Error", "DownloadErrorMsg": "An error occurred during download: {value}", "DownloadRequired": "Download Required", "DownloadRequiredMsg": "To use the {value} version, you need to download the data first. Would you like to download it now?", "Cancel": "Cancel", "DataIncomplete": "Data Incomplete", "DataIncompleteMsg": "The data for {value} version is incomplete and needs to be repaired. Would you like to repair it now?", "Repair": "Repair", "RepairSuccess": "Repair Successful", "RepairSuccessMsg": "The {value} version data has been successfully repaired.", "RepairFailed": "Repair Failed", "RepairFailedMsg": "Unable to repair {value} version data. Please check your network connection and try again."}}