# Locale

Locale basically use i18next internationalization-framework (https://www.i18next.com/)

## Folder layout

```
root
|- src
|  |- locale
|  |  |- TopScreens
|  |  |  |- eng, cht, chs ...
|  |  |- common
|  |  |  |- eng, cht, chs ...
|  |  |- screens
|  |  |  |- eng, cht, chs ...
|  |  |
|  |  |- i18n2_chs.json
|  |  |- i18n2_cht.json
|  |  |- i18n2_eng.json
```

## Guildline for placing locale strings

- App/Homepage UI strings should go to TopScreens
- Screen/View/UI strings should go to Screens
- common strings like Yes/No/Ok etc will go to common
- All the rest strings will go to Translation(i18n2\_\*)
