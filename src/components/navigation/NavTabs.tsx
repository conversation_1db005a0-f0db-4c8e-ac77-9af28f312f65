import React from 'react';
import { View, ViewStyle } from 'react-native';
import { NavChip } from '@/components/navigation/NavChip';

interface INavTabs {
  current: number;
  onChange: (current: number) => void;
  options: { title: string; showRedDot?: boolean }[];
  itemWidth?: number;
  style?: ViewStyle;
}

const NavTabs: React.FC<INavTabs> = ({ current, options, itemWidth = 100, onChange, style }) => {
  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        height: 100,
        justifyContent: 'center',
        gap: 8,
        ...(style || {})
      }}>
      {options.map((item, index) => (
        <NavChip
          key={`nav_${index}`}
          showRedDot={item.showRedDot}
          page={current}
          pageIndex={index}
          title={item.title}
          width={itemWidth}
          onPress={() => {
            onChange(index);
          }}
        />
      ))}
    </View>
  );
};

export default NavTabs;
