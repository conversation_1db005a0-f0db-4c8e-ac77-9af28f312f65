import { Button, RedDot } from '@/components';

import { Colors } from '@/styles/colors';
import React from 'react';
import { View } from 'react-native';
import { getFontSize } from '@/utils/getFontSize';

export const NavChip = ({ page, pageIndex, width, title, onPress, showRedDot }) => (
  <View>
    <Button
      style={{
        padding: 3,
        borderWidth: page === pageIndex ? 2 : 1,
        borderColor: page === pageIndex ? 'white' : '#AAAAAA',
        backgroundColor: page === pageIndex ? 'black' : Colors.buttonBackground,
        height: 32,
        justifyContent: 'center'
      }}
      textStyle={{
        color: page === pageIndex ? 'white' : 'black',
        fontWeight: page === pageIndex ? 'bold' : 'normal',
        lineHeight: getFontSize().mediumFontSize + 4
      }}
      width={width}
      title={title}
      onPress={() => {
        if (page !== pageIndex) {
          onPress();
        }
      }}
    />
    {showRedDot ? <RedDot right={3} top={8} /> : null}
  </View>
);
