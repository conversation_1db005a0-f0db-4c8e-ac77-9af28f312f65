/* eslint-disable react-native/no-unused-styles */

import { StyleSheet, TouchableOpacity, TouchableOpacityProps } from 'react-native';

import React from 'react';
import { getFmNavBarButtonStyles } from '@/fishMeet/styles/components/navigationButtonsStyles';
import { getImage } from '@/components/Images';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { useNavigation } from '@react-navigation/native';

export type NavBarButtonProps = {
  image?: string;
  onPress?: NonNullable<TouchableOpacityProps['onPress']>;
  right?: boolean;
  backgroundColor?: string;
  disabled?: boolean;
  title?: React.ReactNode;
};

export function NavBarButton(props: NavBarButtonProps) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const navigation = useNavigation<any>();

  const { image = 'back', onPress = () => navigation.pop(), right, backgroundColor, disabled, title } = props;

  const styles = getNavBarButtonStyles(right, backgroundColor, disabled);
  const fmStyles = getFmNavBarButtonStyles(right, backgroundColor, disabled);

  return (
    <TouchableOpacity
      disabled={disabled}
      style={isFishMeet ? fmStyles.buttonOpacity : styles.buttonOpacity}
      activeOpacity={1}
      onPress={(event) => {
        onPress(event);
      }}>
      {title ? title : getImage(image)}
    </TouchableOpacity>
  );
}

export function BackButton(props: NavBarButtonProps) {
  return <NavBarButton {...props} image='back' />;
}

export function OkButton(props: NavBarButtonProps) {
  return <NavBarButton {...props} image='ok' />;
}

function getNavBarButtonStyles(
  right: NavBarButtonProps['right'],
  backgroundColor: NavBarButtonProps['backgroundColor'],
  disabled: NavBarButtonProps['disabled']
) {
  return right
    ? StyleSheet.create({
        buttonOpacity: { right: -7, backgroundColor: backgroundColor ?? 'white', opacity: disabled ? 0.3 : 1 }
      })
    : StyleSheet.create({
        buttonOpacity: { left: -7, backgroundColor: backgroundColor ?? 'white', opacity: disabled ? 0.3 : 1 }
      });
}
