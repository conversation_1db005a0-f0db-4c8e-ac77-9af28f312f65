import * as React from 'react';

import { Text, View } from 'react-native';

import { FmText } from '@/fishMeet/components/FmText';
import { isFishMeet } from '@/utils/deviceOrAppType';

export class NavBarTitle extends React.Component {
  render() {
    const { title, subTitle } = this.props;
    const boldTitle = typeof this.props.boldTitle === 'boolean' ? this.props.boldTitle : false;
    const boldSubTitle = typeof this.props.boldSubTitle === 'boolean' ? this.props.boldSubTitle : true;
    const titleSize = typeof this.props.titleSize === 'number' ? this.props.titleSize : 14;
    const subTitleSize = typeof this.props.subTitleSize === 'number' ? this.props.subTitleSize : 16;
    const subTitleColor = typeof this.props.subTitleColor === 'string' ? this.props.subTitleColor : 'black';
    return (
      <View style={{ alignItems: 'center', justifyContent: 'center' }}>
        {isFishMeet ? (
          <FmText
            style={{ fontSize: titleSize, fontWeight: boldTitle ? 'bold' : 'normal', ...this.props.titleStyle }}
            numberOfLines={1}>
            {title || ''}
          </FmText>
        ) : (
          <Text
            style={{ fontSize: titleSize, fontWeight: boldTitle ? 'bold' : 'normal', ...this.props.titleStyle }}
            numberOfLines={1}>
            {title || ''}
          </Text>
        )}

        {/* fm: not used by fishMeet */}
        {subTitle ? (
          <Text
            style={{
              color: subTitleColor,
              fontSize: subTitleSize,
              fontWeight: boldSubTitle ? 'bold' : 'normal',
              paddingTop: 3
            }}
            numberOfLines={1}>
            {subTitle}
          </Text>
        ) : null}
      </View>
    );
  }
}
