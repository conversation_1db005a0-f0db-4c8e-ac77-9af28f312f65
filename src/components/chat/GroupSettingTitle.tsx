import { Text } from 'react-native';
import { getCurrentUser } from '@/utils/user';
import React, { useMemo } from 'react';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { FmText, type FmTextStyle } from '@/fishMeet/components/FmText';

interface GroupSettingTitleProps {
  title: string;
  warn?: string;
  tip?: string;
  style?: React.StyleHTMLAttributes<Text> | FmTextStyle;
}

const GroupSettingTitle: React.FC<GroupSettingTitleProps> = ({ title, warn, tip, style }) => {
  const smallFontSize: number = useMemo(() => getCurrentUser().getSmallFontSize(), []);
  const largeFontSize: number = useMemo(() => getCurrentUser().getLargeFontSize(), []);

  return isFishMeet ? (
    <FmText style={style as FmTextStyle}>
      <FmText style={{ fontSize: largeFontSize, fontWeight: 'bold' }}>{title}</FmText>
      {!!warn && <FmText style={{ fontSize: largeFontSize, color: 'red' }}>{warn}</FmText>}
      {!!tip && <FmText style={{ fontSize: largeFontSize }}>{tip}</FmText>}
    </FmText>
  ) : (
    <Text style={style}>
      <Text style={{ fontSize: smallFontSize, fontWeight: 'bold' }}>{title}</Text>
      {!!warn && <Text style={{ fontSize: smallFontSize, color: 'red' }}>{warn}</Text>}
      {!!tip && <Text style={{ fontSize: smallFontSize }}>{tip}</Text>}
    </Text>
  );
};

export default GroupSettingTitle;
