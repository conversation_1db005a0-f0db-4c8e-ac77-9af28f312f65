import * as React from 'react';

import { Dimensions, Text, TouchableOpacity, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import { getCurrentUser } from '@/utils/user';

export class ListItem extends React.Component {
  static contextType = AppContext;

  render() {
    const { onPress, textLeft, textRight, selected, index, background } = this.props;
    const fontSize = getCurrentUser().getSmallFontSize();

    const backgroundColor = background || Colors.lightBlue;
    const color = selected ? 'white' : Colors.darkBlue;
    const content = (
      <View
        style={{
          marginHorizontal: 4,
          backgroundColor,
          borderColor: Colors.darkBlue,
          borderWidth: 1,
          borderRadius: 25,
          height: 50,
          alignItems: 'center',
          flexDirection: 'row'
        }}>
        {selected ? (
          <View
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              borderRadius: 25,
              backgroundColor: '#00000080'
            }}
          />
        ) : null}
        {index !== undefined ? (
          <View
            style={{
              left: 5,
              borderRadius: 20,
              width: 40,
              height: 40,
              borderWidth: 2,
              borderColor: Colors.darkBlue,
              backgroundColor: Colors.lightBlue,
              alignItems: 'center',
              justifyContent: 'center'
            }}>
            <Text style={{ fontSize, color: Colors.darkBlue }}>{index}</Text>
          </View>
        ) : null}
        <View
          style={{
            width:
              Dimensions.get('window').width -
              this.context.insets.left -
              this.context.insets.right -
              80 -
              (index ? 30 : 0),
            marginLeft: index === undefined ? 20 : 8
          }}>
          <Text style={{ fontSize, color }} numberOfLines={1}>
            {textLeft}
          </Text>
        </View>
        <View style={{ position: 'absolute', right: 10 }}>
          <Text style={{ fontSize, color, textAlign: 'right' }}>{textRight}</Text>
        </View>
      </View>
    );

    if (!onPress) {
      return content;
    }

    return (
      <TouchableOpacity activeOpacity={1} onPress={onPress}>
        {content}
      </TouchableOpacity>
    );
  }
}
