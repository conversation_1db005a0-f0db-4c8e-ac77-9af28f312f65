import { i18n2 } from '@/utils/i18n2';
import { showMessage as showMessageOrg, type MessageOptions } from 'react-native-flash-message';

export function showMessage(data: MessageOptions) {
  showMessageOrg({
    duration: 5000,
    ...data
  });
}

export function showServerErrMessage() {
  showMessage({
    message: i18n2.t('Errors.Error'),
    description: i18n2.t('Errors.ServerErr'),
    type: 'danger'
  });
}
