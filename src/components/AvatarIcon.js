import * as React from 'react';

import { Text, TouchableOpacity, View } from 'react-native';

import { Colors } from '@/styles/colors';
import { Entypo } from '@expo/vector-icons';

export class AvatarIcon extends React.Component {
  /*
  Usage:
    avatar used in groupscreen and chatscreen, to show the icon of user with his name

  Variables:
    isAdd: whether the avataricon is add sign "+" or a name
    size: size of the icon
    fontSize: font size of name
    name: full username
  */

  render() {
    const { isAdd, isAnonymous, size, onPress, onLongPress } = this.props;
    const content = (
      <View
        style={{
          width: size,
          height: size,
          borderRadius: size / 2,
          alignItems: 'center',
          justifyContent: 'center',
          borderColor: this.props.borderColor || Colors.darkBlue,
          backgroundColor: this.props.backgroundColor || Colors.lightBlue,
          borderWidth: 0.5
        }}>
        {isAdd ? <Entypo name='plus' color={Colors.darkBlue} size={35} /> : null}
        {isAnonymous ? <Entypo name='mask' color={Colors.darkBlue} size={35} /> : null}
        {!isAdd && !isAnonymous ? (
          <Text
            style={{
              fontSize: this.props.fontSize,
              fontWeight: this.props.fontWeight || 'normal',
              textDecorationLine: this.props.textDecorationLine || 'none',
              fontStyle: this.props.fontStyle || 'normal',
              color: this.props.textColor || Colors.darkBlue,
              textAlign: 'center'
            }}
            numberOfLines={1}>
            {this.props.name}
          </Text>
        ) : null}
      </View>
    );

    if (onPress || onLongPress) {
      return (
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            if (onPress) {
              onPress();
            }
          }}
          onLongPress={() => {
            if (onLongPress) {
              onLongPress();
            }
          }}>
          {content}
        </TouchableOpacity>
      );
    } else {
      return content;
    }
  }
}
