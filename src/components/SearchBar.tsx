import {
  type NativeSyntheticEvent,
  StyleSheet,
  TextInput,
  type TextInputSubmitEditingEventData,
  TouchableOpacity,
  View,
  type ViewStyle
} from 'react-native';

import { Colors } from '@/styles/colors';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { getImage } from '@/components/Images';
import { i18n2 } from '@/utils/i18n2';

interface SearchBarProps {
  onFocus?: () => void;
  onBlur?: () => void;
  onChangeText?: (text: string) => void;
  onSubmitEditing?: (evt: NativeSyntheticEvent<TextInputSubmitEditingEventData>) => void;
  onClose?: () => void;
  isCloseVisible?: boolean;
  autoFocus?: boolean;
  value?: string | undefined;
  enablesReturnKeyAutomatically?: boolean;
  style?: ViewStyle;
}

const SearchBar: React.FC<SearchBarProps> = ({
  onFocus,
  onBlur,
  onChangeText,
  onClose,
  onSubmitEditing,
  isCloseVisible = true,
  autoFocus = true,
  value,
  enablesReturnKeyAutomatically,
  style = {}
}) => {
  return (
    <View style={{ ...style, ...styles.container }}>
      <View style={styles.innerContainer}>
        {getImage('search')}
        <TextInput
          style={[styles.input, { fontSize: getCurrentUser().getSmallFontSize() }]}
          placeholder={i18n2.t('EnterSearch')}
          autoFocus={autoFocus}
          onChangeText={onChangeText}
          onFocus={onFocus}
          onBlur={onBlur}
          onSubmitEditing={onSubmitEditing}
          value={value}
          enablesReturnKeyAutomatically={enablesReturnKeyAutomatically}
          underlineColorAndroid='transparent'
        />
      </View>
      {isCloseVisible && (
        <TouchableOpacity style={{ paddingLeft: 10 }} onPress={onClose}>
          {getImage('close', { width: 28, height: 28 })}
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 10,
    flexDirection: 'row',
    alignItems: 'center'
  },
  input: {
    paddingHorizontal: 10,
    flex: 1,
    paddingVertical: 6
  },
  innerContainer: {
    flex: 1.0,
    borderWidth: 0.5,
    borderRadius: 7,
    flexDirection: 'row',
    borderColor: Colors.darkBlue,
    backgroundColor: Colors.lightBlue,
    alignItems: 'center',
    paddingHorizontal: 10
  }
});

export default SearchBar;
