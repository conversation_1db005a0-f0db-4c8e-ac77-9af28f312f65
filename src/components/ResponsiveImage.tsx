import React, { useState } from 'react';
import { Image, useWindowDimensions, StyleSheet, ImageProps } from 'react-native';

interface IResponsiveImage extends Omit<ImageProps, 'onLoad' | 'resizeMode'> {
  padding?: number;
}

const ResponsiveImage: React.FC<IResponsiveImage> = ({ padding = 16, source, style, ...rest }) => {
  const [imageHeight, setImageHeight] = useState<number>(100);
  const { width: screenWidth } = useWindowDimensions(); // 使用 useWindowDimensions hook 获取屏幕宽度

  const imageWidth = screenWidth - 2 * padding;

  return (
    <Image
      source={source}
      style={[styles.image, { width: imageWidth, height: imageHeight }, style]}
      onLoad={(event) => {
        const { width, height } = event.nativeEvent.source;
        const ratio = width / height;
        const calculatedHeight = imageWidth / ratio;
        console.log('calculatedHeight', calculatedHeight);
        setImageHeight(calculatedHeight);
      }}
      resizeMode='contain'
      {...rest}
    />
  );
};

const styles = StyleSheet.create({
  image: {
    resizeMode: 'contain' // 保持图片长宽比
  }
});

export default ResponsiveImage;
