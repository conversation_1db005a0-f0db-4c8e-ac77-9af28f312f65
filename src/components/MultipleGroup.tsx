import React, { useMemo } from 'react';
import PopupSelect, { useMultipleSelect } from '@/components/Select/PopupSelect';
import { getGroupIcon } from '@/components/Images';
import { IGroupItemProps, useGroups } from '@/hooks/useGroups';
import { i18n2 } from '@/utils/i18n2';

interface MultipleGroupProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (value: IGroupItemProps[]) => void;
  defaultSelect?: IGroupItemProps[] | undefined;
}

const MultipleGroup: React.FC<MultipleGroupProps> = ({ visible, onCancel, onConfirm, defaultSelect }) => {
  const { groups } = useGroups();
  const { selected, onSelect, checkIsCurrent } = useMultipleSelect<IGroupItemProps>({
    visible,
    keyString: 'groupId',
    defaultSelect
  });
  const handleOk = () => {
    onConfirm(selected);
  };
  const filterGroups = useMemo(() => {
    return [...selected, ...groups.filter((item) => !selected?.find((s) => s.groupId === item.groupId))];
  }, [groups, selected]);
  return (
    <PopupSelect<IGroupItemProps>
      title={i18n2.t('CreateMomentScreen.SelectGroup')}
      visible={visible}
      onCancel={onCancel}
      onConfirm={handleOk}
      onSelect={onSelect}
      multiple
      choices={filterGroups}
      isCurrent={checkIsCurrent}
      keyString='groupId'
      itemIcon={(choice) => getGroupIcon(choice)}
      getDisplayName={(choice) => choice.name}
    />
  );
};

export default MultipleGroup;
