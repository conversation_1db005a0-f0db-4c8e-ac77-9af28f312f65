import * as Notifications from 'expo-notifications';

import {
  Animated,
  DeviceEventEmitter,
  PanResponder,
  PanResponderInstance,
  StyleSheet,
  TouchableOpacity,
  View,
  useWindowDimensions
} from 'react-native';
import React, { useContext, useEffect, useRef, useState, useCallback } from 'react';
import { AppContext } from '@/context/AppContext';
import { JitsiMeeting } from '@jitsi/react-native-sdk';
import { getImage } from '@/components/Images';
import { getMapLanguage } from '@/utils/meeting';
import { i18n2 } from '@/utils/i18n2';
import { useEventListener } from 'expo';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { getCurrentUser } from '@/utils/user';
interface Position {
  x: number;
  y: number;
}
interface UserProfile {
  displayName: string;
}

const FloatingJitsiMeeting = () => {
  const fullWidth = useWindowDimensions().width;
  const fullHeight = useWindowDimensions().height;
  const smallWidth = fullWidth * (3 / 7);
  const smallHeight = smallWidth * (185 / 206);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const jitsiMeetingRef = useRef<any>(null);
  const pan = useRef<Position>({ x: 0, y: 0 }).current;
  const [position, setPosition] = useState<Position>({ x: 0, y: fullHeight * 0.18 });
  const [isFullScreen, setIsFullScreen] = useState<boolean>(true);
  const [isShowBack, setIsShowBack] = useState<boolean>(false);
  const [meetingRoom, setMeetingRoom] = useState<string | null>(null);
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const autoHideTimeout = useRef<NodeJS.Timeout | null>(null);
  const context = useContext(AppContext) as {
    userProfile: UserProfile;
  };
  const boundedPosition = {
    x: Math.max(0, Math.min(position.x, fullWidth - smallWidth)),
    y: Math.max(0, Math.min(position.y, fullHeight - smallHeight))
  };
  const recordIsFullRef = useRef(false);
  const [preMeetingData, setPreMeetingData] = useState<unknown>();

  // animation
  const fadeIn = useCallback(() => {
    opacityAnim.setValue(0);
    Animated.timing(opacityAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true
    }).start();
  }, [opacityAnim]);

  const fadeOut = useCallback(() => {
    opacityAnim.setValue(1);
    Animated.timing(opacityAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true
    }).start();
  }, [opacityAnim]);

  const panResponder: PanResponderInstance = PanResponder.create({
    onStartShouldSetPanResponder: () => !isFullScreen,
    onMoveShouldSetPanResponder: (_e, gestureState) => {
      if (isFullScreen) {
        return false;
      }
      const moveThreshold = 5;
      return Math.abs(gestureState.dx) > moveThreshold || Math.abs(gestureState.dy) > moveThreshold;
    },
    onPanResponderMove: (_e, gestureState) => {
      setPosition((prevPosition) => ({
        x: prevPosition.x + gestureState.dx,
        y: prevPosition.y + gestureState.dy
      }));
    },
    onPanResponderRelease: (_e, gestureState) => {
      pan.x += gestureState.dx;
      pan.y += gestureState.dy;
    }
  });

  useEventListener(globalThis.eventEmitter, 'meetingRoomValue', async (value: string) => {
    const res = await globalThis.dsObject.getPreMeetingData();
    if (res && res.status === 200) {
      setPreMeetingData(res.body);
    }
    setMeetingRoom(value);
  });

  useEffect(() => {
    if (isShowBack) {
      fadeIn();
      autoHideTimeout.current = setTimeout(() => {
        setIsShowBack(false); // after 3 seconds hidden
      }, 3000);
    } else {
      fadeOut();
    }
    return () => {
      if (autoHideTimeout.current) {
        clearTimeout(autoHideTimeout.current);
      }
    };
  }, [fadeIn, fadeOut, isShowBack]);

  useEffect(() => {
    const eventListener = DeviceEventEmitter.addListener('PiPStatusChanged', (event) => {
      const { isInPiPMode, isAppInBackground } = event;
      if (isInPiPMode) {
        recordIsFullRef.current = isFullScreen ? true : false;
        setIsFullScreen(true);
      } else {
        setIsFullScreen(recordIsFullRef.current);
        if (isAppInBackground) {
          Notifications.scheduleNotificationAsync({
            content: {
              title: i18n2.t('MeetingPipNotificationMessage')
            },
            trigger: null
          });
        }
      }
    });
    return () => {
      eventListener.remove();
    };
  }, [isFullScreen]);

  const jitsiEventListeners = {
    onReadyToClose: () => {
      if (jitsiMeetingRef.current) {
        jitsiMeetingRef.current.close();
        setMeetingRoom(null);
      }
    },
    onEnterFloatMeetingInApp: () => {
      setIsFullScreen(false);
    },
    onUploadPreJsonData: async (meetingData: unknown, callback: (result: boolean) => void) => {
      try {
        const res = await globalThis.dsObject.setPreMeetingData(meetingData);
        if (res && res.status === 200) {
          callback(true);
        } else {
          callback(false);
        }
      } catch (error) {
        console.log('error', error);
        callback(false);
      }
    }
  };

  if (!meetingRoom) {
    return null;
  }

  return (
    <View
      style={[
        styles.floatingWindow,
        {
          left: isFullScreen ? 0 : boundedPosition.x,
          top: isFullScreen ? 0 : boundedPosition.y,
          width: isFullScreen ? '100%' : smallWidth,
          height: isFullScreen ? '100%' : smallHeight,
          borderWidth: isFullScreen ? 0 : 2,
          borderRadius: isFullScreen ? 0 : 10
        }
      ]}
      {...panResponder.panHandlers}>
      <View style={{ pointerEvents: isFullScreen ? 'auto' : 'none' }}>
        <JitsiMeeting
          ref={jitsiMeetingRef}
          room={meetingRoom}
          config={{
            defaultLanguage: getMapLanguage(),
            isFishMeet: isFishMeet,
            email: getCurrentUser().getLoginId(),
            preMeetingData: preMeetingData,
            participantsPane: !isFishMeet && { hideMoreActionsButton: true, hideModeratorSettingsTab: true }
          }}
          flags={{
            'invite.enabled': false,
            'bg-video-active.enabled': true,
            'pip.enabled': true
          }}
          style={styles.video}
          userInfo={{
            displayName: context?.userProfile?.displayName || '',
            avatarURL: '',
            email: ''
          }}
          eventListeners={jitsiEventListeners}
        />
      </View>
      {!isFullScreen && isShowBack && (
        <Animated.View style={[styles.floatingBackArrow, { opacity: opacityAnim }]}>
          <TouchableOpacity
            style={styles.floatingBackArrow}
            onPress={() => {
              setIsFullScreen(true);
              setIsShowBack(false);
              if (autoHideTimeout.current) {
                clearTimeout(autoHideTimeout.current);
              }
            }}>
            {getImage('floatMeetingBack', { width: 60, height: 60 })}
          </TouchableOpacity>
        </Animated.View>
      )}
      {!isFullScreen && !isShowBack && (
        <TouchableOpacity
          style={styles.fullScreenTouch}
          onPress={() => {
            setIsShowBack(true);
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  floatingWindow: {
    position: 'absolute',
    backgroundColor: 'black',
    overflow: 'hidden',
    borderColor: '#000000'
  },
  video: {
    width: '100%',
    height: '100%'
  },
  fullScreenTouch: {
    position: 'absolute',
    top: 10,
    left: 10,
    right: 10,
    bottom: 10,
    backgroundColor: 'transparent',
    zIndex: 10
  },
  floatingBackArrow: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)'
  }
});

export default FloatingJitsiMeeting;
