import * as React from 'react';

import { ActivityIndicator, View } from 'react-native';

import { Colors } from '@/styles/colors';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { isFishMeet } from '@/utils/deviceOrAppType';

type LoadingIndicatorProps = {
  backgroundColor?: string;
  indicatorColor?: string;
};

export function LoadingIndicator(props: LoadingIndicatorProps) {
  const backgroundColor = props.backgroundColor ?? (isFishMeet ? undefined : '#FFFFFF90');
  const indicatorColor = props.indicatorColor ?? (isFishMeet ? FmColors.accent : Colors.darkBlue);

  return (
    <View
      style={{
        position: 'absolute',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor,
        alignContent: 'center',
        justifyContent: 'center'
      }}>
      <ActivityIndicator size='large' color={indicatorColor} />
    </View>
  );
}
