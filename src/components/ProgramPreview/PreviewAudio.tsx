import React, { useContext, useState } from 'react';
import { Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { AppContext } from '@/context/AppContext';
import { i18n2 } from '@/utils/i18n2';

interface IPreviewAudio {
  value: string;
}

const PreviewAudio: React.FC<IPreviewAudio> = ({ value }) => {
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const context = useContext(AppContext) as any;
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={async () => {
        setIsPlaying(true);
        await context.sound.loadAsync(value);
        await context.sound.playAsync();
        setIsPlaying(false);
        context.setAudioPlayerMinimized(false);
      }}
      activeOpacity={0.8}>
      <Ionicons name={isPlaying ? 'cloud-download' : 'play-circle'} size={32} color='#4A90E2' style={styles.icon} />
      <Text style={styles.text}>{isPlaying ? i18n2.t('Common.LoadingAudio') : i18n2.t('Common.PlayAudio')}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F4F8',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 24,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 3,
    alignSelf: 'flex-start'
  },
  icon: {
    marginRight: 8
  },
  text: {
    fontSize: 16,
    color: '#333'
  }
});

export default PreviewAudio;
