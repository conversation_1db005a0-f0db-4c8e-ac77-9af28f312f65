import React from 'react';
import RenderHTML from 'react-native-render-html';
import { useWindowDimensions } from 'react-native';
import { getFontSize } from '@/utils/getFontSize';

interface IPreviewText {
  value?: string;
}

const processHTML = (html: string) => {
  return (
    html
      // RenderHTML can't handle <font color></font> correctly so replace <font color="..."> with <span style="color:...;">
      .replace(/<font\s+color="([^"]+)">/gi, '<span style="color:$1;">')
      // Replace </font> with </span>
      .replace(/<\/font>/gi, '</span>')
      .replace(/<br>/gi, '<div></div>')
  );
};

const PreviewText: React.FC<IPreviewText> = ({ value }) => {
  const { width } = useWindowDimensions();
  const processedHtml = value ? processHTML(value) : '';

  return (
    <RenderHTML
      baseStyle={{
        lineHeight: getFontSize().mediumFontSize + 6,
        minHeight: getFontSize().mediumFontSize + 6,
        fontSize: getFontSize().mediumFontSize
      }}
      tagsStyles={{
        h1: {
          lineHeight: getFontSize().mediumFontSize + 16,
          margin: 0
        },
        h2: {
          lineHeight: getFontSize().mediumFontSize + 16,
          paddingTop: 0,
          paddingBottom: 0,
          margin: 0
        },
        ul: {
          margin: 0
        },
        li: {
          margin: 0
        },
        div: {
          minHeight: getFontSize().mediumFontSize + 6
        }
      }}
      contentWidth={width}
      source={{ html: processedHtml }}
    />
  );
};

export default PreviewText;
