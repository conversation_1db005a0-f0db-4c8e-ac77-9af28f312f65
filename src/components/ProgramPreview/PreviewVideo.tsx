import React, { useEffect, useRef, useState } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { ResizeMode, Video, Audio, InterruptionModeIOS, InterruptionModeAndroid } from 'expo-av';

interface IPreviewVideo {
  value?: string;
}

const PreviewVideo: React.FC<IPreviewVideo> = ({ value }) => {
  const videoRef = useRef<Video>(null);
  const [loading, setLoading] = useState(!!value);

  useEffect(() => {
    (async () => {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        playsInSilentModeIOS: true,
        interruptionModeIOS: InterruptionModeIOS.DoNotMix,
        shouldDuckAndroid: true,
        interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
        playThroughEarpieceAndroid: false
      });
    })();
  }, []);

  return value ? (
    <View style={styles.container}>
      <Video
        ref={videoRef}
        style={styles.video}
        source={{ uri: value }}
        rate={1.0}
        volume={1.0}
        isMuted={false}
        resizeMode={ResizeMode.CONTAIN}
        shouldPlay={false}
        isLooping
        useNativeControls
        onLoad={(status) => {
          setLoading(!status.isLoaded);
        }}
        onError={(e) => {
          console.warn('Video load error:', e);
          setLoading(false);
        }}
      />
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color='#fff' />
        </View>
      )}
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 240,
    backgroundColor: '#000'
  },
  video: {
    width: '100%',
    height: '100%'
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 10,
    justifyContent: 'center',
    alignItems: 'center'
  }
});

export default PreviewVideo;
