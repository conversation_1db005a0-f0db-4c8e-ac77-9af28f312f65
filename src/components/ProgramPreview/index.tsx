import React from 'react';
import PreviewText from './PreviewText';
import PreviewLink from './PreviewLink';
import PreviewImage from './PreviewImage';
import PreviewAudio from './PreviewAudio';
import PreviewVideo from './PreviewVideo';
import { View } from 'react-native';
import { IContentItem, FileType } from '@/utils/channel/IChannel';

interface IPreview {
  data?: IContentItem[];
  getPreviewUrl?: (v: string) => string;
}

const Preview: React.FC<IPreview> = ({ data, getPreviewUrl }) => {
  return (
    <View style={{ gap: 16 }}>
      {data?.map((item, index) => {
        const key = `preview_${index};`;
        switch (item.type) {
          case FileType.Text:
            return <PreviewText value={item.value as string} key={key} />;
          case FileType.ExternalLink: {
            const { title, url } = typeof item.value === 'string' ? { url: '', title: '' } : item.value;
            return <PreviewLink title={title} url={url} key={key} />;
          }
          case FileType.Video:
            return (
              <PreviewVideo
                value={
                  getPreviewUrl && typeof item.value === 'string' ? getPreviewUrl(item.value) : (item.value as string)
                }
                key={key}
              />
            );
          case FileType.Image:
            return (
              <PreviewImage
                value={
                  getPreviewUrl && typeof item.value === 'string' ? getPreviewUrl(item.value) : (item.value as string)
                }
                key={key}
              />
            );
          case FileType.Audio:
            return (
              <PreviewAudio
                value={
                  getPreviewUrl && typeof item.value === 'string' ? getPreviewUrl(item.value) : (item.value as string)
                }
                key={key}
              />
            );
          default:
            return null;
        }
      })}
    </View>
  );
};

export default Preview;
