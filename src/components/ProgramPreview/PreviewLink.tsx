import React, { useContext } from 'react';
import { View } from 'react-native';
import { Button } from '@/components';
import { openUrl } from '@/utils/helper';
import { useNavigation } from '@react-navigation/native';
import { AppContext } from '@/context/AppContext';

interface IPreviewLink {
  title?: string;
  url?: string;
}

const PreviewLink: React.FC<IPreviewLink> = ({ title, url }) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const navigation = useNavigation<any>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const context = useContext<any>(AppContext);
  return (
    <View>
      <Button
        title={title || ''}
        onPress={() => {
          url && openUrl(/^https?:\/\//.test(url) ? url : `https://${url}`, navigation, context);
        }}></Button>
    </View>
  );
};

export default PreviewLink;
