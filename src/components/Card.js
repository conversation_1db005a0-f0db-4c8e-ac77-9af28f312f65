import { Text, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import React from 'react';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { FmText } from '@/fishMeet/components/FmText';

export const Card = (props) => {
  const TitleComponent = isFishMeet ? FmText : Text;

  return (
    <AppContext.Consumer>
      {(context) => (
        <View
          style={{
            flex: 1,
            borderWidth: 2,
            margin: 10,
            padding: 10,
            borderColor: isFishMeet ? FmColors.mediumGray100 : Colors.lightBlue,
            borderRadius: isFishMeet ? 30 : 0,
            alignItems: 'center',
            backgroundColor: isFishMeet ? FmColors.lightBackground : 'white'
          }}>
          <TitleComponent
            style={{
              fontSize: props.fontSize || props.titleStyle?.fontSize || 20,
              color: isFishMeet ? FmColors.darkGray : '#202020',
              marginBottom: props.titleStyle?.marginBottom ?? 10,
              alignSelf: props.titleStyle?.alignSelf,
              paddingHorizontal: props.titleStyle?.paddingHorizontal,
              textTransform: isFishMeet ? 'uppercase' : 'none'
            }}>
            {props.title}
          </TitleComponent>
          {props.children}
        </View>
      )}
    </AppContext.Consumer>
  );
};
