import * as React from 'react';

import { FlashList, FlashListProps } from '@shopify/flash-list';
import {
  KeyboardAwareFlatList,
  KeyboardAwareFlatListProps,
  KeyboardAwareScrollView,
  KeyboardAwareScrollViewProps
} from 'react-native-keyboard-aware-scroll-view';

export function KeyboardView(props: KeyboardAwareScrollViewProps) {
  return (
    <KeyboardAwareScrollView keyboardShouldPersistTaps='handled' showsVerticalScrollIndicator={false} {...props}>
      {props.children}
    </KeyboardAwareScrollView>
  );
}

export function KeyboardFlatList<T>(props: KeyboardAwareFlatListProps<T>) {
  return (
    <KeyboardAwareFlatList keyboardShouldPersistTaps={'handled'} showsVerticalScrollIndicator={false} {...props}>
      {props.children}
    </KeyboardAwareFlatList>
  );
}

export function KeyboardFlashList<T>(props: FlashListProps<T>) {
  return (
    <KeyboardView>
      <FlashList keyboardShouldPersistTaps={'handled'} showsVerticalScrollIndicator={false} {...props}>
        {props.children}
      </FlashList>
    </KeyboardView>
  );
}
