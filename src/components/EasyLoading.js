import { ActivityIndicator, Dimensions, Text, View } from 'react-native';

import React from 'react';
import { SafeAreaInsetsContext } from 'react-native-safe-area-context';

let waitTimer = -1;
let refCount = 0;

const waitTimeout = 5000;

export class EasyLoading {
  static bind(loading) {
    this.control = loading;
  }

  static show() {
    if (refCount === 0) {
      if (waitTimer === -1) {
        waitTimer = setTimeout(() => {
          waitTimer = -1;
          if (this.control && this.control.setState) {
            this.control.setState({ isShown: true });
          }
        }, waitTimeout);
      }
    }

    refCount++;
  }

  static dismiss() {
    if (refCount === 0) {
      console.error('wrong ref-count!');
      return;
    }

    refCount--;

    if (refCount === 0) {
      if (waitTimer === -1) {
        if (this.control && this.control.setState) {
          this.control.setState({ isShown: false });
        }
      } else {
        clearTimeout(waitTimer);
        waitTimer = -1;
      }
    }
  }
}

EasyLoading.control = {};

export class Loading extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      isShown: false
    };

    EasyLoading.bind(this);
  }

  componentWillUnmount() {
    EasyLoading.bind(null);
  }

  render() {
    if (!this.state.isShown) {
      return null;
    }

    const screenWidth = Dimensions.get('window').width;
    const screenHeight = Dimensions.get('window').height;
    const loadingText = this.props.loadingText ? this.props.loadingText : 'Loading...';
    return (
      <SafeAreaInsetsContext.Consumer>
        {(insets) => (
          <View
            style={{
              position: 'absolute',
              width: 100,
              height: 100,
              backgroundColor: '#0008',
              alignItems: 'center',
              marginLeft: screenWidth / 2 - 50,
              marginTop: screenHeight / 2 - 50,
              borderRadius: 10
            }}>
            <ActivityIndicator
              style={{ marginTop: 20 }}
              animating={true}
              color={this.props.color || 'white'}
              size={'small'}
            />
            <Text style={{ marginTop: 10, color: '#FFF' }}>{loadingText}</Text>
          </View>
        )}
      </SafeAreaInsetsContext.Consumer>
    );
  }
}
