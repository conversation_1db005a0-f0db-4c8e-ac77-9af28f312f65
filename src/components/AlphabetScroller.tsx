/* eslint-disable react-native/no-raw-text */

import { Text, View, useWindowDimensions } from 'react-native';

import { Colors } from '@/styles/colors';
import { FM_ROUNDED_CORNER_RADIUS } from '@/fishMeet/styles/fmStyles';
import { FlashList } from '@shopify/flash-list';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { FmText } from '@/fishMeet/components/FmText';
import { Pressable } from 'react-native-gesture-handler';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { useHeaderHeight } from '@react-navigation/elements';

export const ALPHABET_SCROLLER_WIDTH = 18;

const headerLetters = '\u2026ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');

let prevIdx: number | null = null;

type AlphabetScrollerProps<T> = {
  flashListRef: React.RefObject<FlashList<T>>;
};
export function AlphabetScroller<T>({ flashListRef }: AlphabetScrollerProps<T>) {
  const { height: windowHeight } = useWindowDimensions();
  const headerHeight = useHeaderHeight();

  const height = Math.max(500, windowHeight / 1.8);
  const top = (windowHeight - height) / 2 - headerHeight;
  const width = ALPHABET_SCROLLER_WIDTH;
  const scrollerPageYTop = top + headerHeight;
  const scrollerPageYBottom = scrollerPageYTop + height;
  const letterHeight = height / headerLetters.length;

  return (
    <View
      style={{
        position: 'absolute',
        backgroundColor: isFishMeet ? FmColors.darkBackground : Colors.lightBlue,
        top,
        right: 0,
        height,
        width,
        borderRadius: isFishMeet ? FM_ROUNDED_CORNER_RADIUS : 24
      }}>
      <View
        style={{
          flex: 1,
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'space-evenly'
        }}>
        {headerLetters.map((char) => (
          <Pressable
            style={{ flex: 1, alignItems: 'center', width }}
            key={char}
            onTouchMove={(event) => {
              if (event.nativeEvent.pageY < scrollerPageYTop || event.nativeEvent.pageY > scrollerPageYBottom) {
                return;
              }

              const idx = Math.floor((event.nativeEvent.pageY - scrollerPageYTop) / letterHeight);
              if (idx === prevIdx) {
                return;
              }

              flashListRef.current?.scrollToItem({ animated: true, item: headerLetters[idx] });
              prevIdx = idx;
            }}
            onTouchStart={() => {
              flashListRef.current?.scrollToItem({ item: char });
            }}>
            {isFishMeet ? (
              <FmText style={{ fontSize: getCurrentUser().getSmallMinusFontSize() }}>{char}</FmText>
            ) : (
              <Text
                style={{
                  fontSize: getCurrentUser().getSmallMinusFontSize(),
                  lineHeight: getCurrentUser().getSmallMinusFontSize()
                }}>
                {char}
              </Text>
            )}
          </Pressable>
        ))}
      </View>
    </View>
  );
}
