import { useState } from 'react';
import { type Id, type InviteUser } from '@/components/MemberInvite/MemberInvite';

export function useMemberInviteState() {
  const [iDigestUserEnteredMembers, iDigestSetUserEnteredMembers] = useState('');
  const [fmUserEnteredMembers, setFmUserEnteredMembers] = useState<Id[]>([]);
  const [inviteUsers, setInviteUsers] = useState<InviteUser[]>([]);

  return {
    iDigestUserEnteredMembers,
    iDigestSetUserEnteredMembers,
    fmUserEnteredMembers,
    setFmUserEnteredMembers,
    inviteUsers,
    setInviteUsers
  };
}
