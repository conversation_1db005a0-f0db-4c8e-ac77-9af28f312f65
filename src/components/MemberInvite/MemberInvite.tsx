/* eslint-disable react-native/no-unused-styles */

import { BorderlessButton, Pressable, RectButton } from 'react-native-gesture-handler';
import { InputFieldBox, inputFieldLabelText } from '@/fishMeet/components/InputField';
import { Keyboard, StyleSheet, Text, TextInput, View, useWindowDimensions } from 'react-native';
import React, { useContext, useMemo, useState } from 'react';
import { fmAddMember, fmSelectMember, iDigestSelectMember } from '@/components/MemberInvite/handleMember';
import {
  fmUserEnteredMemberChipStyles,
  getFmMemberInviteStyles
} from '@/fishMeet/styles/components/memberInviteStyles';

import { AppContext } from '@/context/AppContext';
import { Avatar2 } from '@/components/Avatar2';
import { AvatarAndNameListItem } from '@/fishMeet/components/AvatarAndNameListItem';
import { CheckBox } from '@rneui/themed';
import { Colors } from '@/styles/colors';
import { FmCloseIcon } from '@/fishMeet/components/FmIcons';
import { FmStyles } from '@/fishMeet/styles/fmStyles';
import { FmText } from '@/fishMeet/components/FmText';
import { KeyboardFlashList } from '@/components';
import SearchBar from '@/components/SearchBar';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';

export type InviteUser = {
  id: Id;
  userId: number;
  text: string;
  isSelected: boolean;
  displayName: string;
};

export type Id = string;

type MemberInviteProps =
  | {
      isFishMeet: true;
      header?: React.ReactElement;
      inviteUsers: InviteUser[];
      userEnteredMembers: Id[];
      setInviteUsers: (user: InviteUser[]) => void;
      setUserEnteredMembers: (members: Id[]) => void;
    }
  | {
      isFishMeet: false;
      header?: React.ReactElement;
      inviteUsers: InviteUser[];
      userEnteredMembers: string;
      setInviteUsers: (user: InviteUser[]) => void;
      setUserEnteredMembers: (members: string) => void;
    };

type AppContextType = {
  insets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
};

export function MemberInvite({
  isFishMeet,
  header,
  inviteUsers,
  userEnteredMembers,
  setInviteUsers,
  setUserEnteredMembers
}: MemberInviteProps) {
  const { insets } = useContext(AppContext) as AppContextType;

  const [isSearching, setIsSearching] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [memberInputFieldExpanded, setMemberInputFieldExpanded] = useState(false);

  const { width } = useWindowDimensions();
  const windowWidth = width - insets.left - insets.right;

  const styles = getMemberInviteStyles(windowWidth);
  const fmStyles = getFmMemberInviteStyles(getCurrentUser().getSmallFontSize());

  function iDigestRenderHeader() {
    return !isFishMeet ? (
      <View style={styles.headerContainer}>
        {header}
        <Text style={styles.multiInputLabel}>{i18n2.t('EnterEmails')}</Text>
        <TextInput
          style={styles.multiInput}
          autoCapitalize='none'
          multiline={true}
          value={userEnteredMembers}
          onChangeText={(text) => {
            setUserEnteredMembers(text);
          }}
        />
        {inviteUsers.length > 0 && <Text style={styles.memberSelect}>{i18n2.t('SelectFromList')}</Text>}
        {inviteUsers.length > 10 ? (
          <SearchBar
            style={styles.memberSearchBar}
            autoFocus={false}
            value={searchText}
            isCloseVisible={isSearching}
            onFocus={() => setIsSearching(true)}
            onClose={() => {
              Keyboard.dismiss();
              setIsSearching(false);
              setSearchText('');
            }}
            onChangeText={(text) => setSearchText(text)}
          />
        ) : null}
      </View>
    ) : null;
  }

  function fmRenderHeader(selectedInviteUsers: InviteUser[]) {
    return isFishMeet ? (
      <View style={fmStyles.headerContainer}>
        {header}

        {/* Add members input field label */}
        {!!header && <FmText style={inputFieldLabelText}>{i18n2.t('CreateStudyGroupScreen.AddMembers')}</FmText>}

        {/* Selected users avatar list */}
        {selectedInviteUsers.length > 0 && (
          <View style={fmStyles.selectedInviteUsersListContainer}>
            {selectedInviteUsers.map((user) => (
              <View style={fmStyles.selectedInviteUsersOpacityContainer} key={user.userId}>
                <Pressable
                  style={fmStyles.selectedInviteUsersOpacity}
                  onPress={() => {
                    fmSelectMember(user, inviteUsers, setInviteUsers);
                  }}>
                  <FmCloseIcon style={fmStyles.selectedInviteUsersCloseIcon} enabled={false} />
                  <Avatar2 userId={user.userId} size={50} />
                </Pressable>
                <FmText style={fmStyles.selectedInviteUsersText} numberOfLines={1}>
                  {user.displayName}
                </FmText>
              </View>
            ))}
          </View>
        )}

        {/* Entered users chip list */}
        {userEnteredMembers.length > 0 && (
          <View style={fmStyles.fmUserEnteredMemberChipListContainer}>
            {userEnteredMembers.map((id) => (
              <FmUserEnteredMemberChip
                key={id}
                userEnteredMembers={userEnteredMembers}
                setUserEnteredMembers={setUserEnteredMembers}
                id={id}
              />
            ))}
          </View>
        )}

        {/* Member input field and expand button */}
        <InputFieldBox
          placeholder={memberInputFieldExpanded ? i18n2.t('EnterEmails') : i18n2.t('Common.Email')}
          expanded={memberInputFieldExpanded}
          value={searchText} // Changing placeholder will clear entered text, so the value must be explicitly set
          returnKeyType='go'
          onChangeText={(text) => {
            setSearchText(text);
          }}
          onSubmitEditing={() => {
            fmMemberInputFieldOnSubmitEditing();
          }}
          showClearButton={true}
          onClearButton={() => {
            setSearchText('');
            Keyboard.dismiss();
          }}
          showAddButton={true}
          onAddButton={() => {
            fmMemberInputFieldOnSubmitEditing();
          }}
        />
        <BorderlessButton borderless={false} onPress={() => setMemberInputFieldExpanded(!memberInputFieldExpanded)}>
          <FmText style={fmStyles.expandOpacityText}>
            {/* eslint-disable-next-line react-native/no-raw-text */}
            {memberInputFieldExpanded ? '- ' : '+ '}
            <FmText style={{ ...fmStyles.expandOpacityText, textDecorationLine: 'underline' }}>
              {memberInputFieldExpanded ? i18n2.t('Common.Cancel') : i18n2.t('MultiAddMembers')}
            </FmText>
          </FmText>
        </BorderlessButton>
        <View style={FmStyles.sectionSpacer} />
      </View>
    ) : null;
  }

  function fmMemberInputFieldOnSubmitEditing() {
    return isFishMeet
      ? fmAddMember(searchText, userEnteredMembers, setUserEnteredMembers, inviteUsers, setInviteUsers, setSearchText)
      : null;
  }

  const selectedInviteUsers = useMemo(() => inviteUsers.filter((it) => it.isSelected), [inviteUsers]);
  const showUsers = useMemo(
    () =>
      searchText.trim()
        ? inviteUsers.filter((it) => it.text.toLowerCase().includes(searchText.trim().toLowerCase()))
        : inviteUsers,
    [inviteUsers, searchText]
  );

  return (
    <KeyboardFlashList
      contentContainerStyle={isFishMeet ? fmStyles.flashListContentContainer : {}}
      ListHeaderComponent={isFishMeet ? fmRenderHeader(selectedInviteUsers) : iDigestRenderHeader()}
      data={showUsers}
      extraData={isFishMeet ? null : userEnteredMembers}
      renderItem={({ item }) =>
        isFishMeet ? (
          <AvatarAndNameListItem
            userId={item.userId}
            text={item.text}
            onPress={() => {
              fmSelectMember(item, inviteUsers, setInviteUsers);
            }}
            showCheckBox={true}
            checkBoxToggled={item.isSelected}
          />
        ) : (
          <CheckBox
            textStyle={styles.checkBoxText}
            checkedColor={Colors.darkBlue}
            title={item.text}
            checked={item.isSelected}
            onPress={() => {
              iDigestSelectMember(item, inviteUsers, userEnteredMembers, setInviteUsers, setUserEnteredMembers);
            }}
          />
        )
      }
      estimatedItemSize={isFishMeet ? 61 : 56}
    />
  );
}

type FmUserEnteredMemberChipProps = {
  userEnteredMembers: Id[];
  setUserEnteredMembers: (members: Id[]) => void;
  id: string;
};

function FmUserEnteredMemberChip({ userEnteredMembers, setUserEnteredMembers, id }: FmUserEnteredMemberChipProps) {
  return (
    <RectButton
      style={fmUserEnteredMemberChipStyles.chipOpacity}
      onPress={() => {
        setUserEnteredMembers(userEnteredMembers.filter((it) => it !== id));
      }}>
      <FmText style={fmUserEnteredMemberChipStyles.text}>{id}</FmText>
      <FmCloseIcon style={fmUserEnteredMemberChipStyles.closeIcon} enabled={false} />
    </RectButton>
  );
}

function getMemberInviteStyles(windowWidth: number) {
  return StyleSheet.create({
    headerContainer: {
      flex: 1,
      alignItems: 'center'
    },
    multiInputLabel: {
      width: windowWidth - 50,
      fontSize: getCurrentUser().getSmallFontSize(),
      color: Colors.darkBlue,
      textAlign: 'center',
      marginVertical: 10
    },
    multiInput: {
      borderColor: '#cccccc',
      width: windowWidth - 50,
      fontSize: getCurrentUser().getSmallFontSize(),
      marginHorizontal: 7,
      paddingHorizontal: 7,
      borderWidth: 1,
      textAlignVertical: 'top',
      height: 90
    },
    memberSelect: {
      fontSize: getCurrentUser().getSmallFontSize(),
      color: Colors.darkBlue,
      textAlign: 'center',
      marginTop: 10
    },
    memberSearchBar: {
      marginTop: 10
    },
    checkBoxText: {
      fontSize: getCurrentUser().getSmallFontSize()
    }
  });
}
