import { type Id, type InviteUser } from '@/components/MemberInvite/MemberInvite';
import { isEmailOrUserUniqueIdValid } from '@/utils/helper';
import { i18n2 } from '@/utils/i18n2';
import { showMessage } from '@/components/MessageBox';

export function iDigestSelectMember(
  user: InviteUser,
  inviteUsers: InviteUser[],
  userEnteredMembers: string,
  setInviteUsers: (users: InviteUser[]) => void,
  setUserEnteredMembers: (members: string) => void
) {
  inviteUsers = inviteUsers.map((it) => {
    if (it.id === user.id) {
      return { ...it, isSelected: !it.isSelected };
    } else {
      return it;
    }
  });

  // merge selected members and user entered members
  const selectedUsers = inviteUsers.filter((it) => it.isSelected).map((it2) => it2.id);
  const unselectedUsers = inviteUsers.filter((it) => !it.isSelected).map((it2) => it2.id);
  const enteredMembers = userEnteredMembers.split(',').filter((it) => it.trim() !== '');

  // add selected user from selectedUsers
  for (const user of selectedUsers) {
    if (!enteredMembers.includes(user)) {
      enteredMembers.push(user);
    }
  }

  // remove unselected users from unselectedUsers
  for (const user of unselectedUsers) {
    const pos = enteredMembers.indexOf(user);
    if (pos !== -1) {
      enteredMembers.splice(pos, 1);
    }
  }

  setInviteUsers(inviteUsers);
  setUserEnteredMembers(enteredMembers.join(','));
}

export function fmSelectMember(
  user: InviteUser,
  inviteUsers: InviteUser[],
  setInviteUsers: (users: InviteUser[]) => void
) {
  inviteUsers = inviteUsers.map((it) => {
    if (it.id === user.id) {
      return { ...it, isSelected: !it.isSelected };
    } else {
      return it;
    }
  });

  setInviteUsers(inviteUsers);
}

export function fmAddMember(
  searchText: string,
  userEnteredMembers: string[],
  setUserEnteredMembers: (members: Id[]) => void,
  inviteUsers: InviteUser[],
  setInviteUsers: (user: InviteUser[]) => void,
  setSearchText: (text: string) => void
) {
  const membersToAdd = searchText
    .replaceAll('\n', ',')
    .split(',')
    .map((item) => item.trim())
    .filter((item) => item.length > 0 && !userEnteredMembers.includes(item));

  for (const member of membersToAdd) {
    if (!isEmailOrUserUniqueIdValid(member)) {
      showMessage({ message: i18n2.t('Common.InvalidMemberInvite').replace('{member}', member), type: 'info' });
      return;
    }

    const foundUserIdx = inviteUsers.findIndex((user) => user.id === member);
    if (foundUserIdx === -1) {
      // Add user to userEnteredMembers iff user is not in list
      userEnteredMembers.push(member);
      continue;
    }
    if (!inviteUsers[foundUserIdx]!.isSelected) {
      // If user is not selected (checkbox is not ticked), set isSelected to true (checkbox is ticked)
      inviteUsers = inviteUsers.map((it, i) => {
        if (i === foundUserIdx) {
          return { ...it, isSelected: true };
        } else {
          return it;
        }
      });
    }
  }

  setInviteUsers(inviteUsers);
  setUserEnteredMembers(userEnteredMembers);
  setSearchText('');
}
