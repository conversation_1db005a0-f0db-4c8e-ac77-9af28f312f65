import { Image, Modal, StyleSheet, TouchableOpacity, View } from 'react-native';

import { Icon } from '@rneui/themed';
import React from 'react';
import { Video } from 'expo-av';

const MediaModal = ({ visible, onClose, media }) => {
  return (
    <Modal transparent={true} animationType='fade' visible={visible} onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Icon name='close' type='material' color='#fff' size={28} />
        </TouchableOpacity>
        <View style={styles.modalContent}>
          {/* Close Button with Icon */}

          {/* Render Image or Video */}
          {media && media.type === 'image' && (
            <Image source={{ uri: media.value }} style={styles.media} resizeMode='contain' />
          )}
          {media && media.type === 'video' && (
            <Video
              source={{ uri: media.value }}
              style={styles.media}
              resizeMode='contain'
              shouldPlay
              isLooping
              useNativeControls
            />
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalContent: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center'
  },
  closeButton: {
    position: 'absolute',
    right: 10,
    top: 40,
    padding: 10,
    zIndex: 99
  },
  media: {
    width: '100%',
    height: '70%'
  }
});

export default MediaModal;
