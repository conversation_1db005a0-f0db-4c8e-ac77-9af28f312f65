/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useRef } from 'react';
import ImageView from 'react-native-image-viewing';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Animated,
  ActivityIndicator,
  Platform,
  ActionSheetIOS,
  SafeAreaView,
  Dimensions
} from 'react-native';
import { saveImageToGallery, shareMedia } from '@/utils/media';
import { Colors } from '@/styles/colors';
import { i18n2 } from '@/utils/i18n2';
import { getFilename } from 'expo-asset/build/AssetUris';

type ImageType = { uri: string; attachment?: string };

interface ImagePreviewProps {
  images: ImageType[];
  imageIndex: number;
  ableShare?: boolean;
  ableSave?: boolean;
  onRequestClose: () => void;
}

const ImagePreview: React.FC<ImagePreviewProps> = ({ imageIndex, images, ableShare, ableSave, onRequestClose }) => {
  const [showMessage, setShowMessage] = useState<string>('');
  const [fadeAnim] = useState(new Animated.Value(0));
  const [loadingSave, setLoadingSave] = useState<boolean>(false);
  const [loadingShare, setLoadingShare] = useState<boolean>(false);
  const [showFooter, setShowFooter] = useState<boolean>(false);

  const slideAnim = useRef(new Animated.Value(100)).current;
  // show & hide android action sheet
  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: showFooter ? 0 : 100,
      duration: 100,
      useNativeDriver: true
    }).start();
  }, [showFooter, slideAnim]);

  useEffect(() => {
    if (showMessage) {
      // show toast
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }).start(() => {
        // hide toast
        setTimeout(() => {
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true
          }).start(() => setShowMessage(''));
        }, 1500);
      });
    }
  }, [showMessage, fadeAnim]);

  const save = async (image: ImageType) => {
    const { uri, attachment } = image || {};
    if (uri) {
      setLoadingSave(true);
      const resp = await saveImageToGallery(uri, attachment || getFilename(uri));
      setLoadingSave(false);
      setShowFooter(false);
      setTimeout(() => {
        setShowMessage(resp ? i18n2.t('ChatScreen.SaveSuccess') : i18n2.t('ChatScreen.SaveFailed'));
      }, 0);
    }
  };

  const share = async (image: ImageType) => {
    const { uri } = image || {};
    if (uri) {
      setLoadingShare(true);
      await shareMedia(uri);
      setLoadingShare(false);
      setShowFooter(false);
    }
  };
  const handleLongPress = (image: ImageType) => {
    const { uri } = image;
    if (!uri) {
      return;
    }
    // android only
    if (Platform.OS !== 'ios') {
      setShowFooter(!!(ableSave || ableShare));
      return;
    }
    if (ableSave || ableShare) {
      const options = [
        ...(ableSave ? [i18n2.t('ChatScreen.Save')] : []),
        ...(ableShare ? [i18n2.t('ChatScreen.Share')] : []),
        i18n2.t('Common.Cancel')
      ];
      const cancelButtonIndex = options.length - 1;

      ActionSheetIOS.showActionSheetWithOptions(
        {
          options,
          cancelButtonIndex
        },
        async (buttonIndex) => {
          if (ableSave && buttonIndex === 0) {
            await save(image);
          } else if (ableShare && (ableSave ? buttonIndex === 1 : buttonIndex === 0)) {
            share(image);
          }
        }
      );
    }
  };
  const HeaderComponent: any = showMessage
    ? () => (
        <Animated.View style={[styles.toastContainer, { paddingTop: 60, opacity: fadeAnim }]}>
          <View style={styles.toastContent}>
            <Text style={styles.toastText}>{showMessage}</Text>
          </View>
        </Animated.View>
      )
    : undefined;

  return (
    <ImageView
      visible={imageIndex > -1}
      imageIndex={imageIndex}
      images={images}
      onRequestClose={onRequestClose}
      onLongPress={(image) => {
        handleLongPress(image as ImageType);
      }}
      HeaderComponent={HeaderComponent}
      FooterComponent={({ imageIndex: index }) =>
        showFooter ? (
          <Animated.View style={[styles.footerContainer, { transform: [{ translateY: slideAnim }] }]}>
            <SafeAreaView>
              {ableSave && (
                <TouchableOpacity
                  style={styles.downloadButton}
                  onPress={() => {
                    const current = images[index];
                    current && save(current);
                  }}>
                  <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
                    {loadingSave ? (
                      <ActivityIndicator size='small' color={Colors.darkBlue} />
                    ) : (
                      <Text style={styles.downloadText}>{i18n2.t('ChatScreen.Save')}</Text>
                    )}
                  </View>
                </TouchableOpacity>
              )}
              {ableShare && (
                <TouchableOpacity
                  style={styles.downloadButton}
                  onPress={() => {
                    const current = images[index];
                    current && share(current);
                  }}>
                  {loadingShare ? (
                    <ActivityIndicator size='small' color={Colors.darkBlue} />
                  ) : (
                    <Text style={styles.downloadText}>{i18n2.t('ChatScreen.Share')}</Text>
                  )}
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={styles.downloadButton}
                onPress={() => {
                  setShowFooter(false);
                }}>
                <Text style={styles.downloadText}>{i18n2.t('Common.Cancel')}</Text>
              </TouchableOpacity>
            </SafeAreaView>
          </Animated.View>
        ) : null
      }
    />
  );
};

const styles = StyleSheet.create({
  footerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16
  },
  downloadButton: {
    backgroundColor: Colors.buttonBackground,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
    marginVertical: 4,
    borderRadius: 4,
    height: 48,
    width: Dimensions.get('window').width - 16
  },
  downloadText: {
    color: Colors.answerText,
    fontSize: 18,
    lineHeight: 20
  },
  toastContainer: {
    paddingTop: 60,
    alignItems: 'center'
  },
  toastContent: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    height: 40
  },
  toastText: {
    color: Colors.answerText,
    fontSize: 14
  }
});

export default ImagePreview;
