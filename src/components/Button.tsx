/* eslint-disable react-native/no-unused-styles */

import * as React from 'react';

import { Dimensions, StyleSheet, Text, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import { FmText, FmTextStyle } from '@/fishMeet/components/FmText';

import { Colors } from '@/styles/colors';
import { RectButton } from 'react-native-gesture-handler';
import { getCurrentUser } from '@/utils/user';
import { getFmButtonStyles } from '@/fishMeet/styles/components/buttonStyles';
import { isFishMeet } from '@/utils/deviceOrAppType';

export type ButtonProps = {
  title: string;
  onPress: () => void;
  disabled?: boolean;
  width?: number;
  testID?: string;
  style?: ViewStyle;
  textStyle?: TextStyle | FmTextStyle;
};

export function Button({ title, onPress, disabled, width, testID = '', style, textStyle }: ButtonProps) {
  const styles = getButtonStyles(width ?? Dimensions.get('window').width / 2, style, textStyle);
  const fmStyles = getFmButtonStyles(width ?? Dimensions.get('window').width / 1.5, style, textStyle as FmTextStyle);

  const disabledButtonText = isFishMeet ? (
    <FmText numberOfLines={1} style={fmStyles.text}>
      {title}
    </FmText>
  ) : (
    <Text numberOfLines={1} style={styles.disabledText}>
      {title}
    </Text>
  );
  const disabledButton = (
    <View testID={testID} style={isFishMeet ? fmStyles.disabledButton : styles.disabledButton}>
      <View style={isFishMeet ? fmStyles.textContainer : undefined}>{disabledButtonText}</View>
    </View>
  );

  const enabledButtonText = isFishMeet ? (
    <FmText numberOfLines={1} style={fmStyles.text}>
      {title}
    </FmText>
  ) : (
    <Text numberOfLines={1} style={styles.text}>
      {title}
    </Text>
  );
  const enabledButton = isFishMeet ? (
    <RectButton
      testID={testID}
      style={fmStyles.rectButton}
      onPress={() => {
        onPress();
      }}>
      <View style={fmStyles.textContainer}>{enabledButtonText}</View>
    </RectButton>
  ) : (
    <TouchableOpacity
      testID={testID}
      activeOpacity={1}
      style={styles.buttonOpacity}
      onPress={() => {
        onPress();
      }}>
      <View>{enabledButtonText}</View>
    </TouchableOpacity>
  );

  return disabled ? disabledButton : enabledButton;
}

const getButtonStyles = (
  width: ButtonProps['width'],
  style: ButtonProps['style'],
  textStyle: ButtonProps['textStyle']
) =>
  StyleSheet.create({
    disabledButton: {
      padding: 10,
      borderRadius: 24,
      backgroundColor: Colors.buttonBackground,
      marginVertical: 10,
      borderColor: Colors.disabled,
      borderWidth: 1,
      width,
      ...style
    },
    disabledText: {
      color: Colors.disabled,
      textAlign: 'center',
      fontSize: getCurrentUser().getMediumFontSize(),
      ...textStyle
    },
    buttonOpacity: {
      padding: 10,
      borderRadius: 24,
      backgroundColor: Colors.buttonBackground,
      marginVertical: 10,
      borderColor: Colors.buttonBorder,
      borderWidth: 1,
      width,
      ...style
    },
    text: {
      color: Colors.buttonText,
      textAlign: 'center',
      fontSize: getCurrentUser().getMediumFontSize(),
      ...textStyle
    }
  });
