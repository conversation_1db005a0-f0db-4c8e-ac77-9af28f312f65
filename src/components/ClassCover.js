import * as React from 'react';

import { Text, TouchableOpacity, View } from 'react-native';

import { Image } from 'expo-image';
import { RedDot } from '@/components';
import { getClassCoverUrl } from '@/utils/helper';
import { getCurrentUser } from '@/utils/user';
import { useState } from 'react';

function getHashCode(value) {
  let hash = 0;
  if (!value) {
    return hash;
  }

  for (let i = 0; i < value.length; i++) {
    const chr = value.charCodeAt(i);
    hash = (hash << 5) - hash + chr;
    hash |= 0; // Convert to 32bit integer
  }

  return hash;
}

export function ClassCover({
  margin,
  size,
  classId,
  fontSize = getCurrentUser().getLargeFontSize(),
  onPress,
  text,
  unreadCount = 0
}) {
  const imageColors = [
    '1abc9c',
    '16a085',
    'f1c40f',
    'f39c12',
    '2ecc71',
    '27ae60',
    'e67e22',
    'd35400',
    '3498db',
    '2980b9',
    'e74c3c',
    'c0392b',
    '9b59b6',
    '8e44ad',
    'ecf0f1',
    'bdc3c7',
    '95a5a6',
    '7f8c8d'
  ];

  const backgroundColor = `#${
    imageColors[((getHashCode(classId + text) % imageColors.length) + imageColors.length) % imageColors.length]
  }77`;

  const [imageLoadError, setImageLoadError] = useState(false);

  return (
    <TouchableOpacity activeOpacity={1} onPress={onPress}>
      <View
        style={{
          marginLeft: margin,
          marginBottom: margin,
          width: size,
          height: size,
          borderWidth: 0.5,
          borderColor: 'gray',
          backgroundColor: 'white'
        }}>
        {imageLoadError ? (
          <View
            style={{
              width: size - 1,
              height: size - 1,
              padding: 10,
              position: 'absolute',
              backgroundColor,
              alignItems: 'center',
              justifyContent: 'center'
            }}>
            <Text
              style={{
                fontSize,
                textAlign: 'center'
              }}>
              {text}
            </Text>
          </View>
        ) : (
          <Image
            source={getClassCoverUrl(classId)}
            transition={100}
            cachePolicy={'memory-disk'}
            style={{
              width: size - 1,
              height: size - 1,
              position: 'absolute'
            }}
            contentFit={'fill'}
            onError={() => {
              setImageLoadError(true);
            }}
          />
        )}
        <RedDot count={unreadCount} />
      </View>
    </TouchableOpacity>
  );
}
