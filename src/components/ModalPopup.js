/* eslint-disable react-native/no-unused-styles */

import { BorderlessButton, GestureHandlerRootView } from 'react-native-gesture-handler';
import { StyleSheet, Text, TouchableOpacity, View, useWindowDimensions } from 'react-native';

import { Colors } from '@/styles/colors';
import { FmText } from '@/fishMeet/components/FmText';
import Modal from 'react-native-modal';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { getFmModalPopupStyles } from '@/fishMeet/styles/components/modalPopupStyles';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';

export const ModalPopup = (props) => {
  const { insets, visible, setNotVisible, title, children, items, iconsPerRow, showCancel, showDivider, hideOnPress } =
    props;

  let keyIndex = 0;

  const CONTENT_MARGIN_HORIZONTAL = 5;
  const CONTENT_PADDING = 5;
  const BORDER_WIDTH = 1;

  const { width } = useWindowDimensions();

  const windowWidth = width - insets.left - insets.right;

  const styles = getModalPopupStyles(
    windowWidth - 5,
    BORDER_WIDTH,
    windowWidth - 2 * CONTENT_MARGIN_HORIZONTAL,
    CONTENT_MARGIN_HORIZONTAL,
    title ? 0 : 24,
    CONTENT_PADDING,
    showDivider,
    items.length,
    iconsPerRow,
    (windowWidth - (CONTENT_PADDING + CONTENT_MARGIN_HORIZONTAL + BORDER_WIDTH) * 2) / iconsPerRow
  );
  const fmStyles = getFmModalPopupStyles(
    windowWidth,
    windowWidth - 2 * CONTENT_MARGIN_HORIZONTAL,
    CONTENT_MARGIN_HORIZONTAL,
    title ? 0 : 30,
    CONTENT_PADDING,
    showDivider,
    items.length,
    iconsPerRow,
    (windowWidth - (CONTENT_PADDING + CONTENT_MARGIN_HORIZONTAL) * 2) / iconsPerRow
  );

  return (
    <Modal
      style={{
        position: 'absolute',
        bottom: insets.bottom,
        margin: 0
      }}
      isVisible={visible}
      backdropColor={isFishMeet ? fmStyles.backdrop.color : styles.backdrop.color}
      backdropOpacity={isFishMeet ? fmStyles.backdrop.opacity : styles.backdrop.opacity}
      onBackdropPress={setNotVisible}>
      <GestureHandlerRootView>
        <View style={isFishMeet ? fmStyles.sheetContainer : styles.sheetContainer}>
          {/* render header */}
          {title ? (
            isFishMeet ? (
              <FmText style={fmStyles.headerText}>{title}</FmText>
            ) : (
              <Text style={styles.headerText}>{title}</Text>
            )
          ) : null}

          {/* render content */}
          <View style={isFishMeet ? fmStyles.contentContainer : styles.contentContainer}>
            {items.map((item) => (
              <View style={isFishMeet ? fmStyles.contentItem : styles.contentItem} key={keyIndex++}>
                {isFishMeet ? (
                  <BorderlessButton
                    onPress={() => {
                      item.onPress();
                      if (hideOnPress) {
                        setNotVisible();
                      }
                    }}>
                    {item.renderIcon()}
                    <FmText style={fmStyles.contentItemText}>
                      {item.text}
                      {item.subText && <FmText style={styles.contentItemSubText}>{item.subText}</FmText>}
                    </FmText>
                  </BorderlessButton>
                ) : (
                  <TouchableOpacity
                    activeOpacity={1}
                    onPress={() => {
                      item.onPress();
                      if (hideOnPress) {
                        setNotVisible();
                      }
                    }}>
                    {item.renderIcon()}
                    <Text style={styles.contentItemText}>
                      {item.text}
                      {item.subText && <Text style={styles.contentItemSubText}>{item.subText}</Text>}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </View>

          {/* render children */}
          {children}

          {/* render footer - fm: not used by fishMeet */}
          {showCancel ? (
            <View style={{ width: windowWidth, borderRadius: 24 }}>
              <TouchableOpacity activeOpacity={1} style={styles.footerOpacity} onPress={setNotVisible}>
                <Text style={styles.footerText}>{i18n2.t('Common.Cancel')}</Text>
              </TouchableOpacity>
            </View>
          ) : null}
        </View>
      </GestureHandlerRootView>
    </Modal>
  );
};

const getModalPopupStyles = (
  sheetContainerWidth,
  sheetContainerBorderWidth,
  contentContainerWidth,
  contentContainerMarginHorizontal,
  contentContainerBorderRadius,
  contentContainerPadding,
  showDivider,
  itemsLength,
  iconsPerRow,
  contentItemWidth
) =>
  StyleSheet.create({
    backdrop: {
      color: 'black',
      opacity: 0.7
    },
    sheetContainer: {
      width: sheetContainerWidth,
      backgroundColor: 'white',
      alignItems: 'center',
      marginHorizontal: 3,
      borderColor: Colors.lightBlue,
      borderWidth: sheetContainerBorderWidth,
      borderTopWidth: 0,
      borderRadius: 24
    },
    headerText: {
      fontSize: getCurrentUser().getMediumFontSize(),
      marginVertical: 12
    },
    contentContainer: {
      width: contentContainerWidth,
      marginHorizontal: contentContainerMarginHorizontal,
      borderTopLeftRadius: contentContainerBorderRadius,
      borderTopRightRadius: contentContainerBorderRadius,
      backgroundColor: 'white',
      padding: contentContainerPadding,
      borderColor: Colors.lightBlue,
      borderBottomWidth: showDivider ? 1 : 0,
      flexDirection: 'row',
      justifyContent: itemsLength <= iconsPerRow ? 'center' : 'flex-start',
      flexWrap: 'wrap'
    },
    contentItem: {
      width: contentItemWidth,
      marginVertical: 5
    },
    contentItemText: {
      fontSize: 12,
      paddingTop: 5,
      textAlign: 'center',
      height: 54
    },
    contentItemSubText: {
      fontSize: 10,
      paddingTop: 2,
      textAlign: 'center'
    },
    footerOpacity: {
      marginVertical: 12
    },
    footerText: {
      fontSize: getCurrentUser().getMediumFontSize(),
      textAlign: 'center'
    }
  });
