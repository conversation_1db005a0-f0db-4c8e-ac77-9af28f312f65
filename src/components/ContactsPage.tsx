/* eslint-disable react-native/no-unused-styles */

import { Keyboard, Platform, StyleSheet, Text, TouchableOpacity, View, useWindowDimensions } from 'react-native';
import React, { useContext, useMemo, useRef } from 'react';

import { AppContext } from '@/context/AppContext';
import { Avatar2 } from '@/components/Avatar2';
import { AvatarAndNameListItem } from '@/fishMeet/components/AvatarAndNameListItem';
import { FmText } from '@/fishMeet/components/FmText';
import { InputField } from '@/fishMeet/components/InputField';
import SearchBar from '@/components/SearchBar';
import { getCurrentUser } from '@/utils/user';
import { getFmContactsPageStyles } from '@/fishMeet/styles/components/contactsPageStyles';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { useNavigation } from '@react-navigation/native';
import { FlashList, type ListRenderItemInfo } from '@shopify/flash-list';
import { ALPHABET_SCROLLER_WIDTH, AlphabetScroller } from '@/components/AlphabetScroller';

type Friend = {
  key: string;
  userId: number;
  uniqueId: string;
  value: string;
};

type ContactsPageProps = {
  searchText: string;
  isSearching: boolean;
  handleSearchTextFocus: () => void;
  handleSearchTextClose: () => void;
  handleSearchTextChange: (text: string) => void;
  renderNoFriends: () => React.Component;
  friendItemOnPress?: (member: Friend) => void;
};

type AppContextType = {
  friends: Friend[];
  insets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  userTags: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  blocks: any;
  blockedBy: number[];
};

export function ContactsPage({
  searchText,
  isSearching,
  handleSearchTextFocus,
  handleSearchTextClose,
  handleSearchTextChange,
  renderNoFriends,
  friendItemOnPress
}: ContactsPageProps) {
  const { friends, insets, userTags, blocks, blockedBy } = useContext(AppContext) as AppContextType;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const navigation = useNavigation<any>();
  const flashListRef = useRef<FlashList<string | Friend>>(null);
  const windowDimensions = useWindowDimensions();

  const [friendsData, stickyHeaderIndices] = useMemo(() => {
    const searchTextLowerCase = searchText.trim().toLowerCase();
    const data = searchText.trim()
      ? friends.filter(
          (it) =>
            it.value.toLowerCase().includes(searchTextLowerCase) ||
            it.key.includes(searchTextLowerCase) ||
            (userTags[it.userId] ?? '').toLowerCase().includes(searchTextLowerCase)
        )
      : friends;

    if (data === null) {
      return [data, []];
    }

    // Create header letter for each unique first letter of name
    let hasEllipsis = false;
    const friendsData = data.flatMap((friend, i) => {
      const prevFirstLetter = data[i - 1]?.value.charAt(0);
      const currFirstLetter = friend.value.charAt(0);

      if (prevFirstLetter !== currFirstLetter) {
        const headerLetter = currFirstLetter.toUpperCase();
        // Any header letter that is not in A-Z should be ellipsis
        if (headerLetter.match('[A-Z]') === null) {
          // Only create one ellipsis
          if (!hasEllipsis) {
            hasEllipsis = true;
            return ['\u2026', friend];
          } else {
            return friend;
          }
        } else {
          return [headerLetter, friend];
        }
      } else {
        return friend;
      }
    });

    const stickyHeaderIndices = friendsData
      .map((item, index) => (typeof item === 'string' ? index : null))
      .filter((item) => item !== null) as number[];

    return [friendsData, stickyHeaderIndices];
  }, [friends, searchText, userTags]);

  const styles = getContactsPageStyles(insets);
  const fmStyles = getFmContactsPageStyles(insets);

  if (!Array.isArray(friends) || friends.length === 0) {
    return renderNoFriends();
  }

  return (
    <View style={isFishMeet ? fmStyles.pageContainer : styles.pageContainer}>
      <View style={isFishMeet ? {} : styles.searchBarContainer}>
        {isFishMeet ? (
          <InputField
            value={searchText}
            placeholder={i18n2.t('EnterSearch')}
            showClearButton={true}
            onClearButton={handleSearchTextClose}
            onChangeText={handleSearchTextChange}
            onSubmitEditing={() => Keyboard.dismiss()}
          />
        ) : (
          <SearchBar
            autoFocus={false}
            value={searchText}
            isCloseVisible={isSearching}
            onFocus={handleSearchTextFocus}
            onClose={handleSearchTextClose}
            onChangeText={handleSearchTextChange}
          />
        )}
      </View>
      <FlashList
        keyboardShouldPersistTaps='handled'
        keyboardDismissMode={Platform.OS === 'ios' ? 'on-drag' : 'none'}
        showsVerticalScrollIndicator={false}
        ref={flashListRef}
        data={friendsData}
        stickyHeaderIndices={stickyHeaderIndices}
        renderItem={(item) => {
          if (typeof item.item === 'string') {
            return isFishMeet ? (
              <View style={fmStyles.sectionHeaderContainer}>
                <FmText style={fmStyles.sectionHeaderText}>{item.item}</FmText>
              </View>
            ) : (
              <View style={styles.sectionHeaderContainer}>
                <Text style={styles.sectionHeaderText}>{item.item}</Text>
              </View>
            );
          } else {
            return renderFriendItem(
              item as ListRenderItemInfo<Friend>,
              userTags,
              blocks,
              blockedBy,
              friendItemOnPress,
              navigation
            );
          }
        }}
        getItemType={(item) => {
          return typeof item === 'string' ? 'sectionHeader' : 'row';
        }}
        estimatedItemSize={60}
        estimatedListSize={{ width: windowDimensions.width, height: windowDimensions.height }}
      />
      <AlphabetScroller flashListRef={flashListRef} />
    </View>
  );
}

function renderFriendItem(
  { item }: ListRenderItemInfo<Friend>,
  userTags: AppContextType['userTags'],
  blocks: AppContextType['blocks'],
  blockedBy: AppContextType['blockedBy'],
  friendItemOnPress: ContactsPageProps['friendItemOnPress'],
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  navigation: any
) {
  const fontSize = getCurrentUser().getMediumFontSize();
  const name = item.value.substring(1);
  const userTag = userTags[item.userId] || '';
  const isUserBlocked = blocks[item.userId] || false;
  const isCurrentUserBlocking = blockedBy.includes(item.userId);
  const blockingColor = isUserBlocked || isCurrentUserBlocking ? { color: '#707070' } : {};
  const text = `${name} (${item.key}) ${userTag ? `(${userTag})` : ''}`;
  const friendItemStyles = getFriendItemStyles(blockingColor, fontSize);

  return isFishMeet ? (
    <AvatarAndNameListItem
      userId={item.userId}
      text={text}
      onPress={() => {
        if (friendItemOnPress) {
          friendItemOnPress(item);
        } else {
          goToMemberScreen(item, name, userTag, isUserBlocked, isCurrentUserBlocking, navigation);
        }
      }}
    />
  ) : (
    <TouchableOpacity
      onPress={() => {
        goToMemberScreen(item, name, userTag, isUserBlocked, isCurrentUserBlocking, navigation);
      }}>
      <View style={friendItemStyles.avatarItemContainer}>
        <Avatar2
          userId={item.userId}
          onPress={() => {
            goToMemberScreen(item, name, userTag, isUserBlocked, isCurrentUserBlocking, navigation);
          }}
        />
        <Text style={friendItemStyles.avatarItemText} numberOfLines={1}>
          {text}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

function goToMemberScreen(
  item: Friend,
  name: string,
  userTag: string,
  isUserBlocked: boolean,
  isCurrentUserBlocking: boolean,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  navigation: any
) {
  navigation.navigate('Member', {
    id: item.userId,
    uniqueId: item.uniqueId,
    name,
    email: item.key,
    userTag,
    isUserBlocked,
    isCurrentUserBlocking
  });
}

function getContactsPageStyles(insets: AppContextType['insets']) {
  return StyleSheet.create({
    pageContainer: {
      flex: 1,
      paddingLeft: insets.left,
      paddingRight: insets.right
    },
    searchBarContainer: {
      marginTop: 10
    },
    noFriendsContainer: {
      flex: 1,
      paddingLeft: insets.left,
      paddingRight: insets.right,
      alignContent: 'center',
      justifyContent: 'center'
    },
    sectionHeaderContainer: {
      flex: 1,
      backgroundColor: 'white'
    },
    sectionHeaderText: {
      fontSize: getCurrentUser().getLargeFontSize(),
      fontWeight: 'bold',
      paddingTop: 5,
      marginLeft: 10
    }
  });
}

function getFriendItemStyles(blockingColor: { color?: string }, fontSize: number) {
  return StyleSheet.create({
    avatarItemContainer: {
      height: 60,
      flexDirection: 'row',
      alignItems: 'center',
      paddingLeft: 7,
      marginRight: ALPHABET_SCROLLER_WIDTH
    },
    avatarItemText: {
      ...blockingColor,
      fontSize,
      marginLeft: 7,
      marginRight: ALPHABET_SCROLLER_WIDTH
    }
  });
}
