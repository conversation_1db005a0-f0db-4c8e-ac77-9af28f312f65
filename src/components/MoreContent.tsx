import React, { useState, useCallback } from 'react';
import {
  Text,
  TouchableOpacity,
  View,
  TextLayoutEventData,
  NativeSyntheticEvent,
  StyleProp,
  ViewStyle,
  TextStyle
} from 'react-native';
import { Colors } from '@/styles/colors';
import { i18n2 } from '@/utils/i18n2';
import { getFontSize } from '@/utils/getFontSize';

interface MoreContentProps {
  description: string;
  lineCount?: number;
  ableShowMore?: boolean;
  style?: StyleProp<ViewStyle> | undefined;
  buttonStyle?: StyleProp<ViewStyle> | undefined;
  textStyle?: StyleProp<TextStyle> | undefined;
  buttonTextStyle?: StyleProp<TextStyle> | undefined;
  showMoreText?: string;
  showLessText?: string;
}

const MoreContent: React.FC<MoreContentProps> = ({
  description,
  lineCount = 5,
  ableShowMore = true,
  style,
  textStyle,
  buttonStyle,
  buttonTextStyle,
  showMoreText = i18n2.t('MomentsScreen.ShowMore'),
  showLessText = i18n2.t('MomentsScreen.ShowLess')
}) => {
  const [showMore, setShowMore] = useState<boolean>(false);
  const [isOverflow, setIsOverflow] = useState<boolean>(false);

  const { mediumFontSize: fontSize } = getFontSize();

  const handleTextLayout = useCallback(
    (event: NativeSyntheticEvent<TextLayoutEventData>) => {
      const { lines } = event.nativeEvent;
      if (lines && lines.length >= lineCount) {
        setIsOverflow(true);
      }
    },
    [lineCount]
  );

  return (
    <View style={style}>
      <Text
        style={[{ fontSize, color: Colors.text }, textStyle]}
        numberOfLines={showMore ? undefined : lineCount}
        onTextLayout={handleTextLayout}>
        {description.trim()}
      </Text>
      {ableShowMore && isOverflow && (
        <TouchableOpacity
          style={buttonStyle}
          onPress={() => {
            setShowMore((pre) => !pre);
          }}>
          <Text style={[{ color: Colors.tintColor, lineHeight: fontSize + 4 }, buttonTextStyle]}>
            {showMore ? showLessText : showMoreText}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default MoreContent;
