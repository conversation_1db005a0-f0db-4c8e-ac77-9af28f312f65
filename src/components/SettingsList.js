// https://raw.githubusercontent.com/evetstech/react-native-settings-list/master/index.js

import { ScrollView, StyleSheet, Switch, Text, TextInput, TouchableHighlight, View } from 'react-native';

import { FM_SCREEN_PADDING_HORIZONTAL } from '@/fishMeet/styles/fmStyles';
import { Image } from 'expo-image';
import React from 'react';
import createReactClass from 'create-react-class';
import { isFishMeet } from '@/utils/deviceOrAppType';

const ARROW_ICON = require('@/assets/images/icon-arrow-settings.png');

class SettingsList extends React.Component {
  static defaultProps = {
    backgroundColor: 'white',
    borderColor: 'black',
    defaultItemSize: 50,
    underlayColor: 'transparent',
    defaultTitleStyle: { fontSize: 16 },
    itemMarginTop: 0
  };

  _getGroups() {
    let groupNumber = -1;
    let headers = [];
    let itemGroup = [];
    let result = [];
    React.Children.forEach(this.props.children, (child) => {
      // Allow for null, optional fields
      if (!child || !child.type) {
        return;
      }

      if (child.type.displayName === 'Header') {
        if (groupNumber != -1) {
          result[groupNumber] = { items: itemGroup, header: headers[groupNumber] };
          itemGroup = [];
        }
        groupNumber++;
        headers[groupNumber] = child.props;
      } else if (child.type.displayName === 'Item') {
        if (groupNumber == -1) {
          groupNumber++;
        }
        itemGroup.push(child.props);
      } else {
        if (groupNumber == -1) {
          groupNumber++;
        }
        itemGroup.push(child);
      }
    });
    result[groupNumber] = { items: itemGroup, header: headers[groupNumber] };
    return result;
  }

  render() {
    return (
      <ScrollView {...this.props.scrollViewProps}>
        {this._getGroups().map((group, index) => {
          return this._groupView(group, index);
        })}
      </ScrollView>
    );
  }

  _groupView(group, index) {
    const borderStyle = isFishMeet
      ? { borderTopWidth: 0, borderBottomWidth: 0, borderColor: this.props.borderColor }
      : { borderTopWidth: 1, borderBottomWidth: 1, borderColor: this.props.borderColor };

    if (group.header) {
      return (
        <View key={'group_' + index}>
          <Text
            style={[{ margin: 5 }, group.header.headerStyle]}
            numberOfLines={group.header.headerNumberOfLines}
            ellipsizeMode='tail'
            ref={group.header.headerRef}>
            {group.header.headerText}
          </Text>
          <View style={borderStyle}>
            {group.items.map((item, index) => {
              return this._itemView(item, index, group.items.length);
            })}
          </View>
        </View>
      );
    } else {
      let items;
      if (group.items.length > 0) {
        items = (
          <View style={borderStyle}>
            {group.items.map((item, index) => {
              return this._itemView(item, index, group.items.length);
            })}
          </View>
        );
      }

      return <View key={'group_' + index}>{items}</View>;
    }
  }

  _itemEditableBlock(item, index, position) {
    return [
      <Text
        key={'itemTitle_' + index}
        style={[
          item.titleStyle ? item.titleStyle : this.props.defaultTitleStyle,
          position === 'Bottom' ? null : styles.titleText
        ]}>
        {item.title}
      </Text>,
      item.isEditable ? (
        <TextInput
          key={item.id}
          style={item.editableTextStyle ? item.editableTextStyle : styles.editableText}
          placeholder={item.placeholder}
          onChangeText={(text) => item.onTextChange(text)}
          value={item.value}
        />
      ) : null
    ];
  }

  _itemTitleBlock(item, index, position) {
    return [
      <Text
        key={'itemTitle_' + index}
        style={[
          item.titleStyle ? item.titleStyle : this.props.defaultTitleStyle,
          position === 'Bottom' ? null : styles.titleText
        ]}>
        {item.title}
      </Text>,
      item.titleInfo ? (
        <Text
          key={'itemTitleInfo_' + index}
          style={[
            item.rightSideStyle ? item.rightSideStyle : position === 'Bottom' ? null : styles.rightSide,
            { color: '#B1B1B1' },
            item.titleInfoStyle
          ]}>
          {item.titleInfo}
        </Text>
      ) : null
    ];
  }

  _itemView(item, index, max) {
    let border;

    if (item.type && item.type.displayName) {
      return item;
    }

    if (item.borderHide) {
      switch (item.borderHide) {
        case 'Top':
          border = { borderBottomWidth: 1, borderColor: this.props.borderColor };
          break;
        case 'Bottom':
          border = { borderTopWidth: 1, borderColor: this.props.borderColor };
          break;
      }
    } else {
      border = index === max - 1 ? { borderWidth: 0 } : { borderBottomWidth: 1, borderColor: this.props.borderColor };
    }

    let titleInfoPosition = item.titleInfoPosition ? item.titleInfoPosition : this.props.defaultTitleInfoPosition;

    return (
      <TouchableHighlight
        accessible={false}
        key={'item_' + index}
        underlayColor={item.underlayColor ? item.underlayColor : this.props.underlayColor}
        onPress={item.onPress}
        onLongPress={item.onLongPress}
        ref={item.itemRef}>
        <View
          style={
            item.itemBoxStyle
              ? item.itemBoxStyle
              : [
                  styles.itemBox,
                  {
                    backgroundColor: item.backgroundColor ? item.backgroundColor : this.props.backgroundColor,
                    marginTop: index ? this.props.itemMarginTop : 0
                  }
                ]
          }>
          {item.icon}
          {item.isAuth ? (
            <View style={item.titleBoxStyle ? item.titleBoxStyle : [styles.titleBox, border]}>
              <View style={{ paddingLeft: 5, flexDirection: 'column', flex: 1 }}>
                <View style={{ borderBottomWidth: 1, borderColor: this.props.borderColor }}>
                  <TextInput
                    onSubmitEditing={() => this.passwordInputBlock.focus()}
                    style={{ flex: 1, height: 30, borderBottomWidth: 1 }}
                    placeholder='username'
                    {...item.authPropsUser}
                  />
                </View>
                <View>
                  <TextInput
                    ref={(input) => (this.passwordInputBlock = input)}
                    style={{ flex: 1, height: 30 }}
                    placeholder='password'
                    secureTextEntry={true}
                    returnKeyType={'go'}
                    {...item.authPropsPW}
                    onSubmitEditing={() => item.onPress()}
                  />
                </View>
              </View>
            </View>
          ) : (
            <View
              style={
                item.titleBoxStyle
                  ? item.titleBoxStyle
                  : [
                      styles.titleBox,
                      border,
                      { minHeight: item.itemWidth ? item.itemWidth : this.props.defaultItemSize }
                    ]
              }>
              {titleInfoPosition === 'Bottom' ? (
                <View style={{ flexDirection: 'column', flex: 1, justifyContent: 'center' }}>
                  {item.isEditable
                    ? this._itemEditableBlock(item, index, 'Bottom')
                    : this._itemTitleBlock(item, index, 'Bottom')}
                </View>
              ) : item.isEditable ? (
                this._itemEditableBlock(item, index)
              ) : (
                this._itemTitleBlock(item, index)
              )}

              {item.rightSideContent ? item.rightSideContent : null}
              {item.hasSwitch ? (
                <Switch
                  {...item.switchProps}
                  style={styles.rightSide}
                  onValueChange={(value) => item.switchOnValueChange(value)}
                  value={item.switchState}
                />
              ) : null}
              {this.itemArrowIcon(item)}
            </View>
          )}
        </View>
      </TouchableHighlight>
    );
  }

  itemArrowIcon(item) {
    if (item.arrowIcon) {
      return item.arrowIcon;
    }

    if (item.hasNavArrow) {
      return (
        <Image
          style={[styles.rightSide, styles.arrowStyle, item.arrowStyle]}
          contentFit={'contain'}
          source={ARROW_ICON}
        />
      );
    }

    return null;
  }
}
export default SettingsList;
module.exports = SettingsList;

const styles = StyleSheet.create({
  itemBox: {
    flex: 1,
    justifyContent: 'center',
    flexDirection: 'row'
  },
  titleBox: {
    flex: 1,
    ...(isFishMeet ? { marginHorizontal: FM_SCREEN_PADDING_HORIZONTAL } : { marginLeft: 15 }),
    flexDirection: 'row'
  },
  titleText: {
    flex: 1,
    alignSelf: 'center'
  },
  rightSide: {
    marginRight: 15,
    alignSelf: 'center'
  },
  editableText: {
    flex: 1,
    textAlign: 'right',
    marginRight: 15
  },
  arrowStyle: {
    width: 8,
    height: 13
  }
});

/**
 * Optional Header for groups
 */
SettingsList.Header = createReactClass({
  propTypes: {},
  getDefaultProps() {
    return {
      headerNumberOfLines: 1
    };
  },
  /**
   * not directly rendered
   */
  render() {
    return null;
  }
});

/**
 * Individual Items in the Settings List
 */
SettingsList.Item = createReactClass({
  propTypes: {},
  getDefaultProps() {
    return {
      hasNavArrow: true
    };
  },
  /**
   * not directly rendered
   */
  render() {
    return null;
  }
});
