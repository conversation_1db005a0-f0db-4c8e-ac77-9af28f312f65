import { Image, type ImageStyle } from 'expo-image';
import React from 'react';

export function getImageSource(name: string) {
  switch (name) {
    case 'createGroup':
      return require('@/assets/images/icon-CreateGroup.png');
    case 'joinGroup':
      return require('@/assets/images/icon-JoinGroup.png');
    case 'scanQR':
      return require('@/assets/images/icon-ScanQR.png');
    case 'back':
      return require('@/assets/images/back.png');
    case 'ok':
      return require('@/assets/images/ok.png');
    case 'group':
      return require('@/assets/images/icon-Group.png');
    case 'groupLeader':
      return require('@/assets/images/icon-Group-leader.png');
    case 'groupMember':
      return require('@/assets/images/icon-Group-member-regular.png');
    case 'note':
      return require('@/assets/images/note.png');
    case 'bible':
      return require('@/assets/images/bible.png');
    case 'home':
      return require('@/assets/images/home.png');
    case 'logout':
      return require('@/assets/images/logout.png');
    case 'refresh':
      return require('@/assets/images/refresh.png');
    case 'checkProgress':
      return require('@/assets/images/icon-Check.png');
    case 'groupSelect':
      return require('@/assets/images/icon-Group.png');
    case 'groupSwitch':
      return require('@/assets/images/icon-Group-switch.png');
    case 'attendance':
      return require('@/assets/images/icon-Attendence.png');
    case 'plus':
      return require('@/assets/images/icon-Plus.png');
    case 'voiceButton':
      return require('@/assets/images/icon-Voice-input-button.png');
    case 'voice':
      return require('@/assets/images/icon-Voice-input.png');
    case 'file':
      return require('@/assets/images/icon-file.png');
    case 'camera':
      return require('@/assets/images/icon-Camera.png');
    case 'photo':
      return require('@/assets/images/icon-Photo.png');
    case 'keyboard':
      return require('@/assets/images/icon-Keyboard_Inout.png');
    case 'recording':
      return require('@/assets/images/icon-Voice-input.png');
    case 'playRecording':
      return require('@/assets/images/icon-Voice-input.png');
    case 'copy':
      return require('@/assets/images/icon-Copy.png');
    case 'groupWithName':
      return require('@/assets/images/icon-Class-withname.png');
    case 'groupWithNoName':
      return require('@/assets/images/icon-Class-anonymous.png');
    case 'close':
      return require('@/assets/images/close.png');
    case 'donation':
      return require('@/assets/images/icon-Donation.png');
    case 'hashtag':
      return require('@/assets/images/icon-hashtag.png');
    case 'hashtag2':
      return require('@/assets/images/icon-hashtag2.png');
    case 'removeHashtag':
      return require('@/assets/images/icon-RemoveHashtag.png');
    case 'setReminder':
      return require('@/assets/images/icon-Reminder.png');
    case 'more':
      return require('@/assets/images/icon-More.png');
    case 'reply':
      return require('@/assets/images/icon-reply.png');
    case 'close2':
      return require('@/assets/images/icon-close-white.png');
    case 'chat':
      return require('@/assets/images/icon-Discussion.png');
    case 'groupPage':
      return require('@/assets/images/icon-GroupPageIcon.png');
    case 'deleteGroup':
      return require('@/assets/images/icon-deleteGroup.png');
    case 'exitClass':
      return require('@/assets/images/icon-ExitClass.png');
    case 'exitGroup':
      return require('@/assets/images/icon-ExitGroup.png');
    case 'promoteLeader':
      return require('@/assets/images/icon-LeaderChange-promote.png');
    case 'demoteLeader':
      return require('@/assets/images/icon-LeaderChange-demote.png');
    case 'orgLeader':
      return require('@/assets/images/icon-Org-leader.png');
    case 'orgMember':
      return require('@/assets/images/icon-Org-member.png');
    case 'firstDayOn':
      return require('@/assets/images/icon-FirstDay-on.png');
    case 'firstDayOff':
      return require('@/assets/images/icon-FirstDay-off.png');
    case 'dayNOn':
      return require('@/assets/images/icon-DayN-on.png');
    case 'dayNOff':
      return require('@/assets/images/icon-DayN-off.png');
    case 'lastDayOn':
      return require('@/assets/images/icon-LastDay-on.png');
    case 'lastDayOff':
      return require('@/assets/images/icon-LastDay-off.png');
    case 'export':
      return require('@/assets/images/icon-Export.png');
    case 'feedback':
      return require('@/assets/images/icon-Feedback.png');
    case 'wechat':
      return require('@/assets/images/icon-weChat.png');
    case 'webLink':
      return require('@/assets/images/icon-Weblink.png');
    case 'conference':
      return require('@/assets/images/icon-Conference.png');
    case 'search':
      return require('@/assets/images/icon-SearchGlass.png');
    case 'friend':
      return require('@/assets/images/icon-friend.png');
    case 'exportPrint':
      return require('@/assets/images/icon-ExportPrint.png');
    case 'defaultAvatar':
      return require('@/assets/images/icon-DefaultProfilePicture.png');
    case 'share':
      return require('@/assets/images/icon-Share.png');
    case 'groupSetTop':
      return require('@/assets/images/icon-Group-setTop.png');
    case 'groupSetDown':
      return require('@/assets/images/icon-Group-setDown.png');
    case 'Approve':
      return require('@/assets/images/icon-Accept.png');
    case 'Reject':
      return require('@/assets/images/icon-Reject.png');
    case 'floatMeetingBack':
      return require('@/assets/images/icon-floatMeeting-back.png');
    case 'helpme':
      return require('@/assets/images/helpme.png');
    case 'openMeeting':
      return require('@/assets/images/open-meeting.png');
    default:
      return null;
  }
}

export function getImage(name: string, moreStyles?: ImageStyle) {
  const iconImageStyle = { alignSelf: 'center', width: 30, height: 30, ...moreStyles } as const;
  const chatIconImageStyle = { width: 24, height: 24 };
  const chatIconSmallImageStyle = { width: 16, height: 16 };
  const ratio = 3.5;
  const dayNavStyle = { width: 148 / ratio, height: 128 / ratio, marginTop: 3, marginLeft: -3, ...moreStyles };
  const dayNavStyle2 = { width: 128 / ratio, height: 128 / ratio, marginTop: 3, marginLeft: -3, ...moreStyles };
  switch (name) {
    case 'groupSelect':
      return <Image style={{ width: 100, height: 100 }} source={getImageSource(name)} />;
    case 'voice':
      return <Image style={chatIconImageStyle} source={getImageSource(name)} />;
    case 'recording':
      return <Image style={{ width: 50, height: 50 }} source={getImageSource(name)} />;
    case 'playRecording':
      return <Image style={{ width: 50, height: 50 }} source={getImageSource(name)} />;
    case 'firstDayOn':
      return <Image style={dayNavStyle} source={getImageSource(name)} />;
    case 'firstDayOff':
      return <Image style={dayNavStyle} source={getImageSource(name)} />;
    case 'dayNOn':
      return <Image style={dayNavStyle2} source={getImageSource(name)} />;
    case 'dayNOff':
      return <Image style={dayNavStyle2} source={getImageSource(name)} />;
    case 'lastDayOn':
      return <Image style={dayNavStyle} source={getImageSource(name)} />;
    case 'lastDayOff':
      return <Image style={dayNavStyle} source={getImageSource(name)} />;
    case 'search':
      return <Image style={chatIconSmallImageStyle} source={getImageSource(name)} />;
    case null:
      return null;
    default:
      return <Image style={iconImageStyle} source={getImageSource(name)} />;
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getGroupIcon(group: any) {
  if (group.isOneOnOneGroup) {
    return getImage('friend');
  }

  if (group.isOrgGroup) {
    return group.isGroupLeader ? getImage('orgLeader', { opacity: 0.6 }) : getImage('orgMember', { opacity: 0.6 });
  }

  return group.isGroupLeader ? getImage('groupLeader') : getImage('groupMember');
}
