// https://raw.githubusercontent.com/maherzaidoune/react-native-url-preview/master/index.js

import { Linking, Platform, Text, TouchableOpacity, View } from 'react-native';
import { getObjectAsync, setObjectAsync } from '@/dataStorage/localStorage';

import { Image } from 'expo-image';
import PropTypes from 'prop-types';
import React from 'react';
import { getLinkPreview } from 'link-preview-js';

// eslint-disable-next-line no-useless-escape
const REGEX = /[-a-zA-Z0-9@:%_\+.~#?&//=]{2,256}\.[a-z]{2,4}\b(\/[-a-zA-Z0-9@:%_\+.~#?&//=]*)?/g;

export default class RNUrlPreview extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isUri: false,
      url: undefined,
      linkTitle: undefined,
      linkDesc: undefined,
      linkFavicon: undefined,
      linkImg: undefined
    };
    this.getPreviewAsync(props.text, props.requestOptions);
  }

  getPreviewAsync = async (text, options) => {
    const { onError, onLoad } = this.props;

    try {
      let data = await getObjectAsync(`preview.${text}`);
      if (!data) {
        data = await getLinkPreview(text, options);
        setObjectAsync(`preview.${text}`, data);
      }
      onLoad(data);
      this.setState({
        isUri: true,
        url: data.url,
        linkTitle: data.title ? data.title : undefined,
        linkDesc: data.description ? data.description : undefined,
        linkImg:
          data.images && data.images.length > 0
            ? data.images.find(
                (element) => element.includes('.png') || element.includes('.jpg') || element.includes('.jpeg')
              )
            : undefined,
        linkFavicon: data.favicons && data.favicons.length > 0 ? data.favicons[data.favicons.length - 1] : undefined
      });
    } catch (error) {
      onError(error);
      this.setState({ isUri: false });
    }
  };

  componentDidUpdate(nextProps) {
    if (nextProps.text !== this.props.text) {
      this.getPreviewAsync(nextProps.text);
    } else if (nextProps.text == null) {
      this.setState({ isUri: false });
    }
  }

  _onLinkPressed = () => {
    Linking.openURL(this.props.text.match(REGEX)[0]);
  };

  renderImage = (imageLink, faviconLink, imageStyle, faviconStyle, imageProps) => {
    return imageLink ? (
      <Image style={imageStyle} source={{ uri: imageLink }} {...imageProps} />
    ) : faviconLink ? (
      <Image style={faviconStyle} source={{ uri: faviconLink }} {...imageProps} />
    ) : null;
  };

  renderText = (
    showTitle,
    showDescription,
    title,
    description,
    textContainerStyle,
    titleStyle,
    descriptionStyle,
    titleNumberOfLines,
    descriptionNumberOfLines
  ) => {
    return (
      <View style={textContainerStyle}>
        {showTitle && (
          <Text numberOfLines={titleNumberOfLines} style={titleStyle}>
            {title}
          </Text>
        )}
        {showDescription && (
          <Text numberOfLines={descriptionNumberOfLines} style={descriptionStyle}>
            {description}
          </Text>
        )}
      </View>
    );
  };

  renderLinkPreview = (
    containerStyle,
    imageLink,
    faviconLink,
    imageStyle,
    faviconStyle,
    showTitle,
    showDescription,
    url,
    title,
    description,
    textContainerStyle,
    titleStyle,
    descriptionStyle,
    titleNumberOfLines,
    descriptionNumberOfLines,
    imageProps
  ) => {};

  render() {
    const {
      containerStyle,
      imageStyle,
      faviconStyle,
      textContainerStyle,
      title,
      description,
      titleStyle,
      titleNumberOfLines,
      descriptionStyle,
      descriptionNumberOfLines,
      imageProps,
      onPress,
      onLongPress
    } = this.props;
    const { url, isUri, linkImg, linkFavicon, linkTitle, linkDesc } = this.state;
    if (!isUri) {
      return null;
    }

    return (
      <TouchableOpacity
        style={[styles.containerStyle, containerStyle]}
        activeOpacity={0.9}
        onPress={() => {
          console.log({ url });
          onPress(url);
        }}
        onLongPress={() => {
          onLongPress();
        }}>
        {this.renderImage(linkImg, linkFavicon, imageStyle, faviconStyle, imageProps)}
        {this.renderText(
          title,
          description,
          linkTitle,
          linkDesc,
          textContainerStyle,
          titleStyle,
          descriptionStyle,
          titleNumberOfLines,
          descriptionNumberOfLines
        )}
      </TouchableOpacity>
    );
  }
}

const styles = {
  containerStyle: {
    flexDirection: 'row'
  }
};

RNUrlPreview.defaultProps = {
  onLoad: () => {},
  onError: () => {},
  text: null,
  requestOptions: {},
  containerStyle: {
    backgroundColor: 'rgba(239, 239, 244,0.62)',
    alignItems: 'center'
  },
  imageStyle: {
    width: Platform.isPad ? 160 : 110,
    height: Platform.isPad ? 160 : 110,
    paddingRight: 10,
    paddingLeft: 10
  },
  faviconStyle: {
    width: 40,
    height: 40,
    paddingRight: 10,
    paddingLeft: 10
  },
  textContainerStyle: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    padding: 10
  },
  title: true,
  description: true,
  titleStyle: {
    fontSize: 17,
    color: '#000',
    marginRight: 10,
    marginBottom: 5,
    alignSelf: 'flex-start'
  },
  titleNumberOfLines: 2,
  descriptionStyle: {
    fontSize: 14,
    color: '#81848A',
    marginRight: 10,
    alignSelf: 'flex-start'
  },
  descriptionNumberOfLines: Platform.isPad ? 4 : 3,
  imageProps: { contentFit: 'contain' }
};

RNUrlPreview.propTypes = {
  onLoad: PropTypes.func,
  onError: PropTypes.func,
  text: PropTypes.string,
  containerStyle: PropTypes.object,
  imageStyle: PropTypes.object,
  faviconStyle: PropTypes.object,
  textContainerStyle: PropTypes.object,
  title: PropTypes.bool,
  description: PropTypes.bool,
  titleStyle: Text.propTypes ? Text.propTypes.style : PropTypes.object,
  titleNumberOfLines: Text.propTypes ? Text.propTypes.numberOfLines : PropTypes.number,
  descriptionStyle: Text.propTypes ? Text.propTypes.style : PropTypes.object,
  descriptionNumberOfLines: Text.propTypes ? Text.propTypes.numberOfLines : PropTypes.number,
  requestOptions: PropTypes.shape({
    headers: PropTypes.objectOf(PropTypes.string),
    imagesPropertyType: PropTypes.string,
    proxyUrl: PropTypes.string
  })
};
