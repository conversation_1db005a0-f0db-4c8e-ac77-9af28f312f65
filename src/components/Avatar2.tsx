import React, { useState } from 'react';

import { Image } from 'expo-image';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { Pressable, type PressableProps } from 'react-native-gesture-handler';
import { getImageSource } from '@/components/Images';
import { useEventListener } from 'expo';
import { Text, type TextStyle, View } from 'react-native';
import { FmText, type FmTextStyle } from '@/fishMeet/components/FmText';
import { getHttpsServer } from '@/utils/user';

const avatarVersions: { [index: number]: string } = {};

function getCurrentDateString() {
  const d = new Date();
  return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`;
}

function setAvatarVersion(userId: number, forceUpdateNum: number) {
  avatarVersions[userId] = `${getCurrentDateString()}+${forceUpdateNum}`;
}

function forceUpdateAvatarVersion(userId: number) {
  if (avatarVersions[userId] === undefined) {
    return;
  }

  const forceUpdateNum = parseInt(avatarVersions[userId]!.slice(avatarVersions[userId]!.lastIndexOf('+') + 1));
  setAvatarVersion(userId, forceUpdateNum + 1);
}

type Avatar2Props = {
  userId: number;
  size?: number;
  fullSize?: boolean;
  defaultIcon?: string;
  defaultIconSize?: number;
  onPress?: PressableProps['onPress'];
  onLongPress?: PressableProps['onLongPress'];
  userName?: string;
  userNameStyles?: TextStyle | FmTextStyle;
  disabled?: boolean;
};

export function Avatar2({
  userId,
  size = isFishMeet ? 45 : 40,
  fullSize,
  defaultIcon = 'defaultAvatar',
  defaultIconSize,
  onPress,
  onLongPress,
  userName,
  userNameStyles,
  disabled = !(onPress || onLongPress)
}: Avatar2Props) {
  // Version is considered old each day
  const isOldAvatarVersion =
    avatarVersions[userId]?.slice(0, avatarVersions[userId]?.lastIndexOf('+')) !== getCurrentDateString();

  if (!(userId in avatarVersions) || isOldAvatarVersion) {
    setAvatarVersion(userId, 0);
    console.log(`user id: ${userId}, new avatar version, version: ${avatarVersions[userId]}`);
  }

  useEventListener(globalThis.eventEmitter, 'avatarChanged', () => {
    forceUpdateAvatarVersion(userId);
    console.log(`user id: ${userId}, avatar changed, version: ${avatarVersions[userId]}`);
  });

  const [prevSize, setPrevSize] = useState(size);
  const [prevDefaultIconSize, setPrevDefaultIconSize] = useState(defaultIconSize);
  const [imageSize, setImageSize] = useState(defaultIconSize ?? size);
  // Set imageSize again so item has correct state when used in FlashList
  if (prevSize !== size || prevDefaultIconSize !== defaultIconSize) {
    setPrevSize(size);
    setPrevDefaultIconSize(defaultIconSize);
    setImageSize(defaultIconSize ?? size);
  }

  return (
    <Pressable
      disabled={disabled}
      onPress={(event) => {
        if (onPress) {
          forceUpdateAvatarVersion(userId);
          console.log(`user id: ${userId}, force update avatar, version: ${avatarVersions[userId]}`);
          onPress(event);
        }
      }}
      onLongPress={(event) => {
        if (onLongPress) {
          forceUpdateAvatarVersion(userId);
          console.log(`user id: ${userId}, force update avatar, version: ${avatarVersions[userId]}`);
          onLongPress(event);
        }
      }}>
      <View
        style={{
          width: size,
          height: size,
          alignItems: 'center',
          justifyContent: 'center'
        }}>
        <Image
          style={{
            width: imageSize,
            height: imageSize,
            borderRadius: fullSize ? 0 : imageSize / 2
          }}
          source={{
            uri: fullSize
              ? getHttpsServer(`/blob/public/avatar.${userId}.full.jpg?v=${avatarVersions[userId]}`)
              : getHttpsServer(`/blob/public/avatar.${userId}.jpg?v=${avatarVersions[userId]}`)
          }}
          cachePolicy={fullSize ? 'none' : 'memory-disk'}
          recyclingKey={userId.toString()} // needed to display correctly in FlashList
          contentFit='fill'
          placeholder={getImageSource(defaultIcon)}
          placeholderContentFit='fill'
          onLoad={() => {
            setImageSize(size);
          }}
        />
        {userName &&
          (isFishMeet ? (
            <FmText style={userNameStyles as FmTextStyle} numberOfLines={1}>
              {userName}
            </FmText>
          ) : (
            <Text style={userNameStyles} numberOfLines={1}>
              {userName}
            </Text>
          ))}
      </View>
    </Pressable>
  );
}
