/* eslint-disable react-native/no-unused-styles */

import Animated, { runOnJS, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { CheckBox } from '@rneui/themed';
import { Colors } from '@/styles/colors';
import { FmText } from '@/fishMeet/components/FmText';
import React from 'react';
import { RectButton } from 'react-native-gesture-handler';
import { getCurrentUser } from '@/utils/user';
import { getFmSelectScreenChoicesStyles } from '@/fishMeet/styles/screens/selectScreenStyles';
import { isFishMeet } from '@/utils/deviceOrAppType';

interface IDefaultSelectItem {
  multiple?: boolean;
  onSelect: () => void;
  icon?: React.ReactNode;
  name?: string;
  checked?: boolean;
  id?: string | undefined;
}

const DefaultSelectItem: React.FC<IDefaultSelectItem> = ({ icon, multiple, name, checked, onSelect }) => {
  const choicesStyles = getSelectScreenChoicesStyles(multiple || icon ? false : !!checked);
  const fmChoicesStyles = getFmSelectScreenChoicesStyles(multiple || icon ? false : !!checked);
  const styles = getStyles(icon);

  const translateY = useSharedValue(0);
  const opacity = useSharedValue(1);

  const startAnimation = () => {
    translateY.value = withTiming(checked ? 30 : -30, { duration: 300 });
    opacity.value = withTiming(0, { duration: 300 }, () => {
      translateY.value = 0;
      opacity.value = 1;
      runOnJS(onSelect)(); // Callback after animation
    });
  };

  // Animated styles
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
    opacity: opacity.value
  }));

  const ContentItems = (
    <>
      <View style={styles.selectItemLeft}>
        {icon}
        {isFishMeet ? (
          <FmText style={fmChoicesStyles.choiceText} numberOfLines={1}>
            {name}
          </FmText>
        ) : (
          <Text style={choicesStyles.choiceText} numberOfLines={1}>
            {name}
          </Text>
        )}
      </View>

      {/* fm: not used by fishMeet */}
      {multiple && (
        <CheckBox
          containerStyle={{ padding: 0, margin: 0 }}
          checkedColor={Colors.darkBlue}
          checked={!!checked}
          checkedIcon={
            <View
              style={{
                borderRadius: 12,
                width: 24,
                height: 24,
                borderColor: '#666',
                borderWidth: 2,
                alignItems: 'center',
                justifyContent: 'center'
              }}>
              <Image
                style={{ width: 18, height: 18, borderRadius: 9 }}
                source={require('@/assets/images/icon-checkmark.png')}
              />
            </View>
          }
          uncheckedIcon={
            <View style={{ borderRadius: 12, width: 24, height: 24, borderColor: '#acacac', borderWidth: 2 }} />
          }
          onPress={() => {
            multiple ? startAnimation() : onSelect?.();
          }}
        />
      )}
    </>
  );
  const Content = isFishMeet ? (
    <RectButton style={fmChoicesStyles.choiceView} onPress={multiple ? startAnimation : onSelect}>
      {ContentItems}
    </RectButton>
  ) : (
    <TouchableOpacity style={choicesStyles.choiceView} onPress={multiple ? startAnimation : onSelect} activeOpacity={1}>
      {ContentItems}
    </TouchableOpacity>
  );

  return multiple ? <Animated.View style={animatedStyle}>{Content}</Animated.View> : Content;
};

export default DefaultSelectItem;

const getStyles = (icon: IDefaultSelectItem['icon']) =>
  StyleSheet.create({
    selectItemLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      justifyContent: icon ? 'flex-start' : 'center'
    }
  });

const getSelectScreenChoicesStyles = (isCurrent: boolean) =>
  StyleSheet.create({
    choiceView: {
      marginTop: 10,
      marginHorizontal: 10,
      backgroundColor: isCurrent ? Colors.darkBlue : Colors.lightBlue,
      borderColor: Colors.darkBlue,
      borderWidth: 1,
      borderRadius: 25,
      height: 50,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 2,
      paddingHorizontal: 20
    },
    choiceText: {
      fontSize: getCurrentUser().getSmallFontSize(),
      paddingLeft: 7,
      color: isCurrent ? 'white' : Colors.darkBlue
    }
  });
