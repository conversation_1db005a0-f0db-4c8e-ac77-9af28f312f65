import { Back<PERSON>utton, OkButton } from '@/components/navigation/NavigationButtons';
import { Modal, Text, View, useWindowDimensions } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import Select, { ISelect } from '@/components/Select/Select';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import { FmBackNavIcon } from '@/fishMeet/components/FmIcons';
import { getCurrentUser } from '@/utils/user';
import { isFishMeet } from '@/utils/deviceOrAppType';

interface IPopupSelect<T> extends ISelect<T> {
  visible: boolean;
  onCancel: () => void;
  onConfirm?: () => void;
  title: string;
  multiple?: boolean;
}

interface IUseMultipleSelect<T> {
  visible: boolean;
  defaultSelect?: T[] | undefined;
  keyString: string;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const useMultipleSelect = <T extends Record<string, any>>({
  visible,
  defaultSelect,
  keyString
}: IUseMultipleSelect<T>) => {
  const [selected, setSelected] = useState<T[]>([]);
  useEffect(() => {
    if (!visible) {
      setSelected([]);
    } else {
      setSelected(defaultSelect || []);
    }
  }, [visible, defaultSelect]);
  const onSelect = (choice: T) => {
    if (selected.map((item) => item[keyString]).includes(choice[keyString])) {
      setSelected([...selected.filter((item) => item[keyString] !== choice[keyString])]);
    } else {
      setSelected([...selected, choice]);
    }
  };
  const checkIsCurrent = (choice: T) => {
    return selected.map((item) => item[keyString]).includes(choice[keyString]);
  };
  return { selected, onSelect, checkIsCurrent };
};

const PopupSelect = <T extends object>({
  visible,
  onCancel,
  onConfirm,
  title,
  multiple,
  ...selectProps
}: IPopupSelect<T>) => {
  const { insets } = useContext(AppContext) as {
    insets: { top: number };
  };

  const currentUser = getCurrentUser();

  const { width: windowWidth } = useWindowDimensions();

  return (
    <Modal statusBarTranslucent visible={visible} animationType='slide' onRequestClose={onCancel}>
      <>
        <View
          style={{
            width: windowWidth,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 20,
            paddingTop: insets.top || 0,
            height: 88,
            marginBottom: 16
          }}>
          {isFishMeet ? <FmBackNavIcon onPress={onCancel} /> : <BackButton onPress={onCancel} />}
          <Text style={{ color: Colors.answerText, fontSize: currentUser.getMediumFontSize(), fontWeight: 'bold' }}>
            {title}
          </Text>
          {multiple && onConfirm && <OkButton onPress={onConfirm} />}
        </View>
        <Select<T> {...selectProps} multiple={!!multiple} />
      </>
    </Modal>
  );
};

export default PopupSelect;
