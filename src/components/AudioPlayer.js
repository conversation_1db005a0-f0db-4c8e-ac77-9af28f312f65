import { Dimensions, Platform, Text, TouchableOpacity, View } from 'react-native';
import { FontAwesome, MaterialIcons } from '@expo/vector-icons';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import { Linking } from 'react-native';
import React from 'react';
import Slider from '@react-native-community/slider';
import { connectActionSheet } from '@expo/react-native-action-sheet';
import { i18n2 } from '@/utils/i18n2';

export const AudioPlayerHeight = 57;

class AudioPlayer extends React.Component {
  static contextType = AppContext;

  constructor(props) {
    super(props);

    this.state = {
      didJustFinish: false,
      durationMillis: 0,
      hasJustBeenInterrupted: false,
      isBuffering: false,
      isLoaded: false,
      isLooping: false,
      isMuted: false,
      isPlaying: false,
      playableDurationMillis: 0,
      positionMillis: 0,
      progressUpdateIntervalMillis: 0,
      rate: 1,
      shouldCorrectPitch: false,
      shouldPlay: true,
      uri: '',
      volume: 1
    };

    this.unmounted = false;
  }

  componentDidMount() {
    this.soundListener = this.context.sound.addListener(this.onPlaybackStatusUpdate);
  }

  componentWillUnmount() {
    this.unmounted = true;
    this.context.sound.resetAsync();
    this.soundListener?.remove();
  }

  safeSetState(state) {
    if (!this.unmounted) {
      this.setState(state);
    }
  }

  onPlaybackStatusUpdate = async (status) => {
    const { didJustFinish } = status;
    if (didJustFinish) {
      console.log('didJustFinish');
      this.context.sound.resetAsync();
    }

    this.safeSetState({ ...status });
  };

  getMMSSFromMillis(millis) {
    const totalSeconds = millis / 1000;
    const seconds = Math.floor(totalSeconds % 60);
    const minutes = Math.floor(totalSeconds / 60);

    const padWithZero = (number) => {
      const string = number.toString();
      if (number < 10) {
        return '0' + string;
      }
      return string;
    };
    return padWithZero(minutes) + ':' + padWithZero(seconds);
  }

  onSeekSliderSlidingComplete = (value) => {
    console.log('SeekComplete: ' + value);

    if (this.state.isLoaded) {
      this.context.sound.setPositionAsync(value * this.state.durationMillis).then(() => {
        this.context.sound.playAsync();
      });
    }
  };

  onSeekSlidingStart = (value) => {
    console.log('onSeekSlidingStart: ' + value);
    if (this.state.isLoaded) {
      this.context.sound.pauseAsync();
    }
  };

  onSeekSliderValueChange = (value) => {
    console.log('onSeekSliderValueChange: ' + value);
    const { durationMillis } = this.state;
    this.setState({ positionMillis: value * durationMillis });
  };

  onPlayOrPauseOrReload = async () => {
    const { error, isLoaded, isPlaying } = this.state;
    console.log('onPlayOrPauseOrReload', { error, isLoaded, isPlaying });
    if (error) {
      console.log('Error, reload the audio');
      this.context.sound.resetAsync();
      return;
    }

    if (!isLoaded) {
      console.log('Audio is not loaded');
      return;
    }

    if (isPlaying) {
      this.context.sound.pauseAsync();
      return;
    }

    this.context.sound.playAsync();
  };

  render() {
    const { isLoaded, positionMillis, durationMillis } = this.state;
    if (!isLoaded) {
      return <></>;
    }

    const {
      insets,
      audioPlayerMinimized,
      showAudioClose,
      sound: {
        status: { rate }
      }
    } = this.context;

    const height = AudioPlayerHeight;
    const color = this.props.color || Colors.darkBlue;

    if (audioPlayerMinimized) {
      return (
        <View
          style={{
            position: 'absolute',
            bottom: insets.bottom + Platform.select({ ios: 53, android: 60 }),
            alignItems: 'center',
            marginLeft: Dimensions.get('window').width - 43,
            marginBottom: 13
          }}>
          <TouchableOpacity
            activeOpacity={1}
            underlayColor={'#0000'}
            style={{
              borderWidth: 1,
              borderColor: Colors.darkBlue,
              backgroundColor: this.props.backgroundColor || '#dddddd',
              borderRadius: 16,
              width: 32
            }}
            onPress={() => {
              this.context.setAudioPlayerMinimized(false);
            }}>
            <MaterialIcons name='audiotrack' color={color} size={30} />
          </TouchableOpacity>
        </View>
      );
    }

    const progress = durationMillis > 0 ? positionMillis / durationMillis : 0;
    return (
      <View
        style={{
          position: 'absolute',
          bottom: insets.bottom + Platform.select({ ios: 53, android: 60 }),
          flexDirection: 'row',
          backgroundColor: this.props.backgroundColor || '#dddddd',
          alignItems: 'center',
          width: Dimensions.get('window').width,
          height
        }}>
        <TouchableOpacity
          activeOpacity={1}
          underlayColor={'#0000'}
          style={{
            marginLeft: 15,
            width: 30
          }}
          onPress={() => {
            this.onPlayOrPauseOrReload();
          }}>
          <FontAwesome
            color={color}
            name={
              this.state.error
                ? 'refresh'
                : this.state.isLoaded
                  ? this.state.isPlaying
                    ? 'pause'
                    : 'play'
                  : 'hand-stop-o'
            }
            size={28}
          />
        </TouchableOpacity>
        <Text style={{ width: 90, color }}>
          {this.getMMSSFromMillis(positionMillis)}/{this.getMMSSFromMillis(durationMillis)}
        </Text>
        <Slider
          style={{ width: Dimensions.get('window').width - 180 }}
          thumbStyle={{ height: 28, width: 28 }}
          thumbTintColor={Colors.darkBlue}
          minimumTrackTintColor={Colors.darkBlue}
          maximumTrackTintColor={Colors.lightBlue}
          value={progress}
          onValueChange={this.onSeekSliderValueChange}
          onSlidingStart={this.onSeekSlidingStart}
          onSlidingComplete={this.onSeekSliderSlidingComplete}
          disabled={!this.state.isLoaded}
        />
        <TouchableOpacity
          activeOpacity={1}
          underlayColor={'#0000'}
          style={{
            marginLeft: 11,
            width: 30
          }}
          onPress={() => {
            const options = [
              {
                text: i18n2.t('OpenInBrowser'),
                action: () => {
                  this.context.sound.resetAsync();
                  Linking.openURL(this.context.sound.url);
                }
              }
            ];

            if (rate !== 1) {
              options.push({
                text: i18n2.t('SetSpeed') + '1x',
                action: () => {
                  this.context.sound.setRateAsync(1);
                }
              });
            }
            if (rate !== 1.25) {
              options.push({
                text: i18n2.t('SetSpeed') + '1.25x',
                action: () => {
                  this.context.sound.setRateAsync(1.25);
                }
              });
            }
            if (rate !== 1.5) {
              options.push({
                text: i18n2.t('SetSpeed') + '1.5x',
                action: () => {
                  this.context.sound.setRateAsync(1.5);
                }
              });
            }

            if (showAudioClose) {
              options.push({
                text: i18n2.t('Minimize'),
                action: () => {
                  this.context.setAudioPlayerMinimized(true);
                }
              });

              options.push({
                text: i18n2.t('Close'),
                action: () => {
                  this.context.sound.resetAsync();
                }
              });
            }

            options.push({
              text: i18n2.t('Common.Cancel'),
              action: () => {}
            });

            this.props.showActionSheetWithOptions(
              {
                options: options.map((it) => it.text),
                cancelButtonIndex: options.length - 1,
                destructiveButtonIndex: options.length - 1
              },
              (selectedIndex) => {
                options[selectedIndex]?.action();
              }
            );
          }}>
          <MaterialIcons name='more-horiz' color={color} size={28} />
        </TouchableOpacity>
        {this.context.sound.status.rate > 1 && (
          <Text style={{ position: 'absolute', top: 37, left: 74, color }}>(x{this.context.sound.status.rate})</Text>
        )}
      </View>
    );
  }
}

export default connectActionSheet(AudioPlayer);
