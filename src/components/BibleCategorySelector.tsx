import React from 'react';
import { StyleSheet, Text, TouchableOpacity, ScrollView } from 'react-native';
import { getBibleData, IBibleData, getDefaultTranslation } from '@/utils/bibleUtils';
import { i18n2 } from '@/utils/i18n2';

interface BibleCategorySelectorProps {
  activeCategory: string;
  onCategoryChange: (categoryId: string) => void;
  containerStyle?: object;
}

const BibleCategorySelector: React.FC<BibleCategorySelectorProps> = ({
  activeCategory,
  onCategoryChange,
  containerStyle
}) => {
  const bibleData: IBibleData = getBibleData();

  const translationField = getDefaultTranslation();

  const categories =
    bibleData?.categories.map((cat) => ({
      id: cat.id,
      name: cat.translations[translationField],
      bookRange: cat.bookRange
    })) ?? [];

  return (
    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={[styles.categoriesContainer, containerStyle]}>
      <TouchableOpacity
        key='all'
        style={[styles.categoryButton, activeCategory === 'all' && styles.activeCategoryButton]}
        onPress={() => onCategoryChange('all')}>
        <Text style={[styles.categoryText, activeCategory === 'all' && styles.activeCategoryButtonText]}>
          {i18n2.t('BibleBooksScreen.All')}
        </Text>
      </TouchableOpacity>

      {categories.map((category) => (
        <TouchableOpacity
          key={category.id}
          style={[styles.categoryButton, activeCategory === category.id && styles.activeCategoryButton]}
          onPress={() => onCategoryChange(category.id)}>
          <Text style={[styles.categoryText, activeCategory === category.id && styles.activeCategoryButtonText]}>
            {category.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  categoriesContainer: {
    flexDirection: 'row',
    height: 50,
    backgroundColor: '#fff',
    paddingVertical: 5
  },
  categoryButton: {
    height: 40,
    paddingHorizontal: 15,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
    borderRadius: 20,
    backgroundColor: '#f0f0f0'
  },
  activeCategoryButton: {
    backgroundColor: '#333'
  },
  categoryText: {
    fontSize: 14,
    color: '#333'
  },
  activeCategoryButtonText: {
    color: '#fff'
  }
});

export default BibleCategorySelector;

// 辅助函数，根据分类ID获取对应的书卷范围
export const getBookRangeByCategory = (categoryId: string): [number, number] | null => {
  if (categoryId === 'all') {
    return [1, 66]; // 全部书卷
  }

  const bibleData = getBibleData();
  const category = bibleData.categories.find((cat) => cat.id === categoryId);

  return category ? category.bookRange : null;
};
