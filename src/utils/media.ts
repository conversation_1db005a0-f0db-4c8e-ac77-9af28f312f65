import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import * as Linking from 'expo-linking';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';

import { Alert } from 'react-native';
import { Camera } from 'expo-camera';
import { i18n2 } from '@/utils/i18n2';

export const notifyPermissionIssue = (category: string) => {
  Alert.alert(
    `${i18n2.t('NoPermission')} - ${i18n2.t(category)}`,
    i18n2.t('EnablePermission'),
    [
      {
        text: i18n2.t('Open'),
        onPress: () => {
          Linking.openSettings();
        }
      },
      { text: i18n2.t('Common.Cancel'), onPress: () => {} }
    ],
    { cancelable: true }
  );
};

type attachmentType = {
  image?: ImagePicker.ImagePickerAsset;
  video?: ImagePicker.ImagePickerAsset;
  audio?: { uri: string | null; duration: number };
  file?: DocumentPicker.DocumentPickerAsset;
};
type sendAttachmentAsyncCallback = (attachment: attachmentType) => void;

export async function pickImageAsync(
  sendAttachmentAsync: sendAttachmentAsyncCallback,
  options: ImagePicker.ImagePickerOptions = { mediaTypes: ImagePicker.MediaTypeOptions.All }
) {
  try {
    const { granted } = await Camera.requestCameraPermissionsAsync();
    if (!granted) {
      notifyPermissionIssue('Photos');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync(options);
    if (!result.canceled) {
      const asset = result.assets[0];
      if (asset?.type === 'image') {
        sendAttachmentAsync({ image: asset });
      } else if (asset?.type === 'video') {
        sendAttachmentAsync({ video: asset });
      }
    }
  } catch (error) {
    alert(error);
  }
}

export async function takePictureAsync(
  sendAttachmentAsync: sendAttachmentAsyncCallback,
  options: ImagePicker.ImagePickerOptions
) {
  try {
    const { granted } = await Camera.requestCameraPermissionsAsync();
    if (!granted) {
      notifyPermissionIssue('Camera');
      return;
    }

    const result = await ImagePicker.launchCameraAsync(options);
    if (!result.canceled) {
      const asset = result.assets[0];
      if (asset?.type === 'image') {
        sendAttachmentAsync({ image: asset });
      } else if (asset?.type === 'video') {
        sendAttachmentAsync({ video: asset });
      }
    }
  } catch (error) {
    alert(error);
  }
}

export async function pickFileAsync(sendAttachmentAsync: sendAttachmentAsyncCallback) {
  try {
    const result = await DocumentPicker.getDocumentAsync({ type: '*/*', copyToCacheDirectory: true });
    if (!result.canceled) {
      const asset = result.assets[0];
      if (asset) {
        sendAttachmentAsync({ file: asset });
      }
    }
  } catch (error) {
    alert(error);
  }
}

export async function saveImageToGallery(uri: string, attachment: string) {
  try {
    // permission
    const { status } = await MediaLibrary.requestPermissionsAsync();
    if (status !== 'granted') {
      notifyPermissionIssue('Photos');
      return false;
    }
    // download
    const localUri = `${FileSystem.cacheDirectory}${attachment}`;
    await FileSystem.downloadAsync(uri, localUri);

    // save
    await MediaLibrary.createAssetAsync(localUri);
    return true;
  } catch (error) {
    console.log('error_____', error);
    return false;
  }
}

export async function shareMedia(uri: string) {
  try {
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(uri);
      return true;
    } else {
      return false;
    }
  } catch (e) {
    return false;
  }
}
