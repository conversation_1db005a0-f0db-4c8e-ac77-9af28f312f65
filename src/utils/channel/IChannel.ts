export enum EButtonType {
  Text = 'text',
  Image = 'Image',
  MultiMedia = 'AV',
  ExternalLink = 'AV1'
}

export enum FileType {
  Text = 'text',
  Image = 'image',
  Audio = 'audio',
  Video = 'video',
  ExternalLink = 'link'
}

export interface IContentItem {
  // button
  buttonType: EButtonType;
  // fileType
  type: FileType;
  value:
    | string
    | {
        title: string;
        url: string;
      };
}

export interface IChannel {
  channelId: number;
  name: string;
  description?: string;
  tags?: string;
  // Validate Status: 0-not published, 1-published
  status?: number;
  // Validate Type: 0 - 公开课 (can only be set by user 3+15), 1 - 有限公开课, 2 - 私家课
  type?: number;
  cover?: string;
  isSubscribed?: boolean;
}

export interface IProgram {
  title?: string;
  programId: number;
  channelId: number;
  channelName?: string | undefined;
  // Validate Status: 0-not published, 1-published
  status?: 0 | 1;
  cover?: string;
  imageWidth: number;
  imageHeight: number;
  createTime: number;
  updateTime?: number;
  subtitle?: string;
  tags?: string;
  content?: IContentItem[];
  likeCount: number;
  collect: number;
  share: number;
  views: number;
  isLiked?: boolean;
  isCollected?: boolean;
}
