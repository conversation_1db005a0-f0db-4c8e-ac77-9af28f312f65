import pinyin from 'chinese-to-pinyin';

const _pinyinMap: { [index: string]: string } = {};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function sortByPinYin(items: any[], getItemName: (item: any) => string): any[] {
  return items.sort((a, b) => {
    const originalA = getItemName(a);
    const originalB = getItemName(b);

    const pinyinA = _pinyinMap[originalA] ?? pinyin(originalA, { removeTone: true, keepRest: true, removeSpace: true });
    _pinyinMap[originalA] = pinyinA.toLowerCase();

    const pinyinB = _pinyinMap[originalB] ?? pinyin(originalB, { removeTone: true, keepRest: true, removeSpace: true });
    _pinyinMap[originalB] = pinyinB.toLowerCase();

    return pinyinA.localeCompare(pinyinB);
  });
}

export function getPinYin(name: string) {
  if (!_pinyinMap[name]) {
    const result = pinyin(name, { removeTone: true, keepRest: true, removeSpace: true });
    _pinyinMap[name] = result;
  }
  return _pinyinMap[name];
}
