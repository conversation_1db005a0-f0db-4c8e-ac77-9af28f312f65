import { i18n2 } from '@/utils/i18n2';
import { launchMeetingAsync } from '@/utils/meeting';
import { logEvent } from '@/utils/logger';
import { showServerErrMessage } from '@/components/MessageBox';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function goToStudyProgressAsync(navigation: any, groupId: number, groupName: string) {
  const result = await globalThis.dsObject.getStudyProgress(groupId);
  if (!result) {
    return;
  }

  const classes = result.body;
  if (!Array.isArray(classes)) {
    showServerErrMessage();
    return;
  }

  if (classes.length === 1) {
    navigation.navigate('GroupProgress', {
      groupId,
      groupName,
      classId: classes[0].classId,
      classTitle: classes[0].className
    });
  } else {
    navigation.navigate('GroupProgressList', {
      groupId,
      groupName,
      classes
    });
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function goToChat(group: any, navigation: any) {
  logEvent('goToGroupChatFromMyGroups', { id: group.groupId });

  if (group.isOneOnOneGroup) {
    navigation.navigate('Chat', {
      chatId: group.groupId,
      title: group.name,
      group: {
        ...group,
        isGroupLeader: false
      },
      showGroupIcon: true,
      disableTagging: true,
      disableAtPeople: true,
      enableAudioChat: true
    });
  } else {
    navigation.navigate('Chat', {
      chatId: group.groupId,
      title: group.name,
      group,
      showGroupIcon: true,
      isOrgGroupChat: group.orgId !== 0,
      enableAudioChat: true
    });
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function goToMeetingTab(canOpenJitsi: boolean, canOpenFishMeet: boolean, groups: any, navigation: any) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onSelected = async (group: any) => {
    await launchMeetingAsync(group.groupId, canOpenJitsi, canOpenFishMeet);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const choices = groups.filter((it: any) => it.status);
  navigation.navigate('Select', {
    choices,
    text: choices.length === 0 ? i18n2.t('MeetingUI.NotJoinedGroup') : undefined,
    title: i18n2.t('MeetingUI.SelectMeeting'),
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    getDisplayName: (choice: any) => choice.name,
    isCurrent: () => false,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onSelect: (group: any) => {
      setTimeout(() => {
        onSelected(group);
      }, 0);
    }
  });
}
