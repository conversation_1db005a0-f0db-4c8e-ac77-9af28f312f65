import { i18n2 } from '@/utils/i18n2';

export const getDisappearingMessagesChoicesName = (choice: number) => {
  const names = ['Disabled', 'FourWeeks', 'OneWeek', 'OneDay', 'EightHours', 'OneHour', 'FiveMins', 'OneMin'];
  const choiceName = names[choice] ?? '';
  return i18n2.t(choiceName);
};

export const getDisappearingMessagesText = (value: unknown) => {
  // Disabled, 1 min, '4 weeks', '1 week', '1 day', '8 hours', '1 hour', '5 mins'
  if (typeof value !== 'number' || isNaN(value) || value <= 0) {
    return i18n2.t('Disabled');
  }

  if (value < 60) {
    return i18n2.t('NumMins').replace('{value}', value.toString());
  }

  if (value < 24 * 60) {
    return i18n2.t('NumHours').replace('{value}', (value / 60).toString());
  }

  if (value < 7 * 24 * 60) {
    return i18n2.t('NumDays').replace('{value}', (value / 24 / 60).toString());
  }

  return i18n2.t('NumWeeks').replace('{value}', (value / 7 / 24 / 60).toString());
};
