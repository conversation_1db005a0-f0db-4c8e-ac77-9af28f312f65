import { getCurrentUser } from '@/utils/user';

export const getFontSize = () => {
  const current = getCurrentUser();
  return {
    X2LargeFontSize: current.getX2LargeFontSize(),
    XLargeFontSize: current.getXLargeFontSize(),
    largeFontSize: current.getLargeFontSize(),
    mediumFontSize: current.getMediumFontSize(),
    smallFontSize: current.getSmallFontSize(),
    smallMinusFontSize: current.getSmallMinusFontSize(),
    XSmallMinusFontSize: current.getXSmallFontSize(),
    XXSmallFontSize: current.getXXSmallFontSize(),
    X3SmallFontSize: current.getX3SmallFontSize()
  };
};
