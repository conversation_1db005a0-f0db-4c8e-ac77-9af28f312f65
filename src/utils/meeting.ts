import * as Linking from 'expo-linking';

import { Platform } from 'react-native';
import { getCurrentUser } from '@/utils/user';
import { isFishMeet } from '@/utils/deviceOrAppType';

export const launchMeetingAsync = async (groupId: number, canOpenJitsi: boolean, canOpenFishMeet: boolean) => {
  const result = await globalThis.dsObject.getMeetingLink(groupId);
  if (!result) {
    return;
  }

  const meetingTool = getCurrentUser().getProperty('setting.meetingTool') ?? 0;

  let useJitsiApp = meetingTool === 2;
  let useFishMeetApp = meetingTool === 1;
  const useJitsiSDK = isFishMeet ? true : getCurrentUser().getProperty('setting.useJitsiSDK');

  if (Platform.OS === 'ios' && !canOpenJitsi) {
    useJitsiApp = false;
  }

  if (Platform.OS === 'ios' && !canOpenFishMeet) {
    useFishMeetApp = false;
  }

  if (useFishMeetApp) {
    // change 'https://' (length=8) with FishMeet app deep link
    Linking.openURL(`org.gtinternational.meet://${result.body.url.substring(8)}`);
  } else if (useJitsiApp) {
    // change 'https://' (length=8) with jitsi app deep link
    Linking.openURL(`org.jitsi.meet://${result.body.url.substring(8)}`);
  } else if (useJitsiSDK) {
    globalThis.eventEmitter.emit('meetingRoomValue', result.body.url);
  } else {
    // use browser jitsi version
    Linking.openURL(result.body.url);
  }
};

export const getMapLanguage = () => {
  const language = getCurrentUser().getLanguage();
  const languageMap: { [index: string]: string } = {
    eng: 'en',
    chs: 'zhCN',
    cht: 'zhTW'
  };
  return languageMap[language] || 'en';
};
