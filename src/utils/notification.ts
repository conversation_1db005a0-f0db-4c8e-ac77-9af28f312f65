import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';

import Constants from 'expo-constants';
import { Platform } from 'react-native';
import { getCurrentUser } from '@/utils/user';
import { notifyPermissionIssue } from '@/utils/media';

/**
 * Get push notification token
 *
 * @returns token string, null for error case
 */
export async function getPushTokenAsync(showUI: boolean = true) {
  if (!Device.isDevice) {
    if (showUI) {
      alert('Must use physical device for Push Notifications');
    }
    return null;
  }

  if (Platform.OS === 'android') {
    // Prepare the notification channel
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C'
    });
  }

  let granted = await canUseNotificationsAsync();

  if (!granted) {
    await Notifications.requestPermissionsAsync();
    granted = await canUseNotificationsAsync();
  }

  if (!granted) {
    if (showUI) {
      notifyPermissionIssue('Notifications');
    }
    return null;
  }

  return (
    await Notifications.getExpoPushTokenAsync({
      projectId: Constants.expoConfig?.extra?.['eas'].projectId
    })
  ).data;
}

export async function canUseNotificationsAsync() {
  const { status } = await Notifications.getPermissionsAsync();
  return status === 'granted';
}

export async function registerForPushNotificationsAsync(token: string | null) {
  try {
    if (!token) {
      return false;
    }

    const result = await globalThis.dsObject.registerDevice(token);
    return result ? true : false;
  } finally {
    await getCurrentUser().resetPropertyAsync('registeredPushNotificationTime', Date.now());
  }
}

export async function unregisterForPushNotificationsAsync(token: string) {
  try {
    if (!token) {
      return false;
    }

    const result = await globalThis.dsObject.unregisterDevice(token);
    return result ? true : false;
  } finally {
    await getCurrentUser().deletePropertyAsync('registeredPushNotificationTime');
  }
}

// check for push notification permission
export const checkRegisterForPushNotificationsAsync = async (showUI: boolean = false) => {
  console.log('checkRegisterForPushNotificationsAsync');

  if (!Device.isDevice) {
    return;
  }

  const disablePushNotification = getCurrentUser().getProperty('disablePushNotification');
  if (disablePushNotification) {
    return;
  }

  // request permission
  try {
    const result = await Notifications.requestPermissionsAsync();
    console.log({ result });

    const registeredTime = getCurrentUser().getProperty('registeredPushNotificationTime') ?? 0;
    const now = Date.now();
    const daysDiff = Math.floor(Math.abs(now - registeredTime) / 1000 / 60 / 60 / 24);
    console.log(`registeredPushNotificationTime: daysDiff = ${daysDiff}`);
    if (daysDiff >= 7) {
      const token = await getPushTokenAsync(showUI);
      await registerForPushNotificationsAsync(token);
    }
  } catch (error) {
    console.error('Error checking push notification permission:', error);
    return;
  }
};
