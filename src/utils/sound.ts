import { AVPlaybackStatus, Audio } from 'expo-av';
import { activateKeep<PERSON>wakeAsync, deactivateKeep<PERSON>wake } from 'expo-keep-awake';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type SoundListenerCallback = (...args: any[]) => void;

type SoundListener = {
  id: number;
  callback: SoundListenerCallback;
};

export default class Sound {
  listeners: SoundListener[] = [];
  url: string;
  soundObject: Audio.Sound | null;
  isLoaded: boolean;
  isPlaying: boolean;
  status: AVPlaybackStatus | null;

  constructor() {
    this.listeners = [];
    this.url = '';
    this.soundObject = null;
    this.isLoaded = false;
    this.isPlaying = false;
    this.status = null;
  }

  addListener = (callback: SoundListenerCallback) => {
    const id = this.listeners.length + 1;
    this.listeners.push({ id, callback });
    console.log('addListener', this.listeners.length);
    return {
      remove: () => {
        const index = this.listeners.findIndex((item) => item.id === id);
        if (index !== -1) {
          this.listeners.splice(index, 1);
          console.log('removeListener', this.listeners.length);
        }
      }
    };
  };

  onPlaybackStatusUpdate = (status: AVPlaybackStatus) => {
    this.status = status;
    for (const item of this.listeners) {
      item.callback(status);
    }
  };

  async enableAsync() {
    if (this.soundObject) {
      console.log('Sound:resetAsync:soundObject is already initialized');
      return;
    }

    console.log('Sound:enable');
    try {
      await Audio.setAudioModeAsync({
        staysActiveInBackground: true,
        allowsRecordingIOS: false,
        playsInSilentModeIOS: true,
        playThroughEarpieceAndroid: false,
        shouldDuckAndroid: false
      });
      await Audio.setIsEnabledAsync(true);

      this.soundObject = new Audio.Sound();
      this.soundObject.setOnPlaybackStatusUpdate(this.onPlaybackStatusUpdate);
    } catch (error) {
      console.log('Fatal error: ', error);
      this.soundObject = null;
    }
  }

  async resetAsync() {
    if (!this.soundObject) {
      console.log('Sound:resetAsync:soundObject is null');
      return false;
    }

    console.log('Sound:reset');
    try {
      if (this.isPlaying) {
        await this.soundObject.stopAsync();
        this.isPlaying = false;
      }

      if (this.isLoaded) {
        await this.soundObject.unloadAsync();
        this.isLoaded = false;
      }

      deactivateKeepAwake();
    } catch (error) {
      alert(error);
      console.log(JSON.stringify(error));
      return false;
    }

    return true;
  }

  async loadAsync(uri: string) {
    if (!(await this.resetAsync())) {
      return false;
    }

    console.log(`Sound:loadAsync:${uri}`);

    try {
      await this.soundObject?.loadAsync({ uri }, {}, /* downloadFirst */ false);
      this.isLoaded = true;
      this.url = uri;
    } catch (error) {
      alert(error);
      console.log(JSON.stringify(error));
      return false;
    }

    return true;
  }

  async unloadAsync() {
    if (!this.soundObject) {
      console.log('Sound:resetAsync:soundObject is null');
      return false;
    }

    console.log('Sound:unloadAsync');
    try {
      await this.soundObject.unloadAsync();
      deactivateKeepAwake();
    } catch (error) {
      alert(error);
      console.log(JSON.stringify(error));
      return false;
    }

    return true;
  }

  async playAsync() {
    if (!this.soundObject) {
      console.log('Sound:playAsync:soundObject is null');
      return;
    }

    if (this.isPlaying) {
      return;
    }

    console.log('Sound:playing');

    try {
      await this.soundObject.playAsync();
      this.isPlaying = true;
      activateKeepAwakeAsync(this.url);
      console.log(`activateKeepAwakeAsync ${this.url}`);
    } catch (error) {
      console.log(JSON.stringify(error));
      throw error;
    }
  }

  async pauseAsync() {
    if (!this.soundObject) {
      console.log('Sound:pauseAsync:soundObject is null');
      return;
    }

    if (!this.isPlaying) {
      return;
    }

    console.log('Sound:paused');
    try {
      await this.soundObject.pauseAsync();
      this.isPlaying = false;
      deactivateKeepAwake(this.url);
      console.log(`deactivateKeepAwake ${this.url}`);
    } catch (error) {
      console.log(JSON.stringify(error));
      throw error;
    }
  }

  async setRateAsync(rate: number) {
    if (!this.soundObject) {
      console.log('Sound:setRateAsync:soundObject is null');
      return;
    }

    console.log('Sound:setRateAsync');
    try {
      await this.soundObject.setRateAsync(rate, true);
    } catch (error) {
      console.log(JSON.stringify(error));
      throw error;
    }
  }

  async playFromPositionAsync(positionMillis: number) {
    if (!this.soundObject) {
      console.log('Sound:playFromPositionAsync:soundObject is null');
      return;
    }

    await this.pauseAsync();

    console.log(`Sound:playFromPositionAsync:${positionMillis}`);
    try {
      await this.soundObject.playFromPositionAsync(positionMillis);
      this.isPlaying = true;
      await activateKeepAwakeAsync(this.url);
    } catch (error) {
      console.log(JSON.stringify(error));
      throw error;
    }
  }

  setPositionAsync = async (positionMillis: number) => {
    if (!this.soundObject) {
      console.log('Sound:playFromPositionAsync:soundObject is null');
      return;
    }

    console.log(`Sound:setPositionAsync:${positionMillis}`);
    try {
      await this.soundObject.setPositionAsync(positionMillis);
    } catch (error) {
      console.log(JSON.stringify(error));
      throw error;
    }
  };
}
