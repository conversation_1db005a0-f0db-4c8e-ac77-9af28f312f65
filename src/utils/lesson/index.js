import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';

import { deleteObjectAsync, getFilePath, getObjectAsync, resetObjectAsync } from '@/dataStorage/localStorage';
import { getCurrentUser, getHttpsServer } from '@/utils/user';

import { Alert } from 'react-native';
import { ClassDownloadUtil } from '@/utils/lesson/ClassDownloadUtil';
import { convertFileSizeToString } from '@/utils/helper';
import { i18n2 } from '@/utils/i18n2';

const DownloadLessonListKey = 'download_lesson_list';

// Class directory data storage key
export const getDownloadLessonKey = (id) => `lesson/${id}`;
// Class unit data storage key
export const getDownloadLessonSessionKey = ({ id, file, index }) => `lesson/${id}/-/${file}?index=${index}`;
// Check if already downloaded
export const checkIsDownload = async (id) => {
  const lessons = (await getObjectAsync(DownloadLessonListKey)) || [];
  return lessons.length && lessons.some((item) => item.classId === id);
};
// Get template
export const getTemplateHtml = async (classId, errorCallback) => {
  try {
    // Load template HTML
    let html;
    let template =
      (await getObjectAsync(`LessonTemplate.${classId}`)) || (await getObjectAsync('LessonTemplate.Default'));
    if (template?.data) {
      html = template.data;
      return html;
    } else {
      Alert.alert(i18n2.t('Common.Failed'), i18n2.t('MissingTemplate'));
      errorCallback && errorCallback();
      return false; // Return null if template loading fails
    }
  } catch (e) {
    Alert.alert(i18n2.t('Common.Failed'), i18n2.t('MissingTemplate'));
    errorCallback && errorCallback();
    return false;
  }
};

// Add deleted downloaded classes to the homepage
export const resetLessonList = async (list) => {
  const lessons = (await getObjectAsync(DownloadLessonListKey)) || [];
  const newList = [...(list || [])];
  if (lessons.length) {
    lessons.forEach((i) => {
      if (!newList.some((j) => i.classId === j.classId)) {
        newList.push({ ...i, isMyClass: true });
      }
    });
  }
  return newList;
};
// Process answers for units
export const dealLessonSessionAnswers = (answers = {}, content = {}) => {
  if (!Array.isArray(content.sessions)) {
    return {};
  }

  const validAnswers = {};

  // Helper function to add valid answers
  const addAnswerIfValid = (key) => {
    if (answers[key]) {
      validAnswers[key] = answers[key];
    }
  };

  // Parse sessions for valid answers
  content.sessions.forEach((session) => {
    // Parse session content
    session.content.forEach((item) => {
      if (['question', 'sliderChoice', 'singleChoice', 'multipleChoice'].includes(item.type)) {
        addAnswerIfValid(item.value);
      } else if (item.type === 'inlineQuestion') {
        (item.value.match(/{[A-Za-z_0-9]+}/g) || []).forEach((it) => {
          const id = `iq_${it.slice(1, -1)}`; // Trim curly braces and prefix with 'iq_'
          addAnswerIfValid(id);
        });
      }
    });
  });

  return validAnswers;
};
// Process Class unit data
export const dealLessonSessionResult = async (lessonOrigin, result, index, context, day = 1) => {
  const lesson = result.body.lesson || {};
  const answers = dealLessonSessionAnswers(result.body.answers || {}, lesson);
  const discussions = result.body.discussions;
  const content = [];
  let questionsCount = 0;

  console.log('lesson: ', lesson.options, result.body.options);
  // Helper function to calculate question counts
  const calculateQuestionsCount = (item) => {
    if (['question', 'sliderChoice', 'singleChoice', 'multipleChoice'].includes(item.type)) {
      questionsCount++;
    } else if (item.type === 'inlineQuestion') {
      const answerIds = new Set((item.value.match(/{[A-Za-z_0-9]+}/g) || []).map((it) => it));
      questionsCount += answerIds.size;
    }
  };

  // Parse lesson sessions
  lesson.sessions?.forEach((session) => {
    if (session.title) {
      session.content.unshift({
        type: 'title',
        value: session.title
      });
      session.title = ''; // Clear title after moving
    }

    session.content.forEach(calculateQuestionsCount);

    const position = getCurrentUser().getProperty(`LessonProgress.${lessonOrigin.classId}`);
    const dayIndex = content.length + 1;
    const scrollTop = day === dayIndex && position?.scrollTop ? position.scrollTop : 0;

    content.push({
      // some class can have additional options, used by the class specific template.html
      options: lesson.options,
      currentPage: dayIndex,
      totalPages: lesson.sessions.length,
      title: lesson.title,
      session,
      answers,
      discussions,
      canShare: true,
      position: { scrollTop },
      marginTop: (lesson.sessions.length > 1 ? 90 : 50) + context.insets.top,
      accessToken: getCurrentUser().accessToken,
      delayLoadImage: true,
      fontSize: getCurrentUser().getMediumFontSize(),
      contentUrl: getHttpsServer(`lesson/${lessonOrigin.classId}/content/`),
      pageViewUrl: getHttpsServer(`lessonPageView/${lessonOrigin.classId}/${index}`),
      playAudioInApp: true,
      i18n: {
        NotAnswered: i18n2.t('NotAnswered'),
        TotalShare: i18n2.t('TotalNumPosts'),
        Share: i18n2.t('PostAnswer'),
        Play: i18n2.t('Play')
      }
    });
  });

  const stateContent = { content, answers, lesson };

  return { stateContent, questionsCount, discussions };
};

// Get audio and video links to be downloaded
const getMediaList = (content, key, index, mediaList, classId) => {
  content.forEach(({ session, accessToken, contentUrl }, i) => {
    if (session.content && session.content.length) {
      // Optimize handling - downloaded classes do not retain update prompts.
      session.content = session.content.filter(
        (s, sIndex) => !(sIndex === 0 && s.type === 'link' && s.url === 'https://www.idigest.app/install')
      );
      session.content.forEach((s, j) => {
        if (s.type === 'video' || s.type === 'audio' || s.type === 'image') {
          let value = s.value;
          const mediaKey = `lesson_${classId}_${index}_${i}_${j}_${value.split('?')[0].split('/').pop()}`;
          const mediaListItem = { value, i, j, index, type: s.type, mediaKey };
          if (mediaList[key]) {
            mediaList[key].push(mediaListItem);
          } else {
            mediaList[key] = [mediaListItem];
          }
        }
      });
    }
  });
};

// Download Class unit
export const downloadLessonSession = async ({ lesson, index, top }, mediaList) => {
  // Download session when not on web
  if (lesson.lesson && lesson.lesson.toLowerCase().endsWith('.json')) {
    try {
      const result = await globalThis.dsObject.loadLesson(lesson.classId, lesson.lesson, index);
      if (!result) {
        return false;
      }
      const { stateContent } = await dealLessonSessionResult(lesson, result, index, { insets: { top } });
      const key = getDownloadLessonSessionKey({ id: lesson.classId, file: lesson.lesson, index });
      const { content } = stateContent;
      // Get mediaList and update content links
      getMediaList(content, key, index, mediaList, lesson.classId);
      await resetObjectAsync(key, stateContent);
      return true;
    } catch (e) {
      console.log('e____', e);
      return false;
    }
  }
};
// Get link
function getAccessUrl(src, content) {
  if (/^https:\/\/[-.\w]+.idigest.app/.test(src)) {
    return `${src}?token=${content.accessToken}`;
  } else if (src.toLowerCase().startsWith('http://') || src.toLowerCase().startsWith('https://')) {
    return src;
  }

  return encodeURI(`${content.contentUrl}${src}?token=${content.accessToken}`);
}
// Download media resources
export const downloadMedia = async (mediaList, lessonItem, cancelToken) => {
  if (mediaList && Object.keys(mediaList)) {
    const keys = Object.keys(mediaList).sort((prev, next) => {
      return mediaList[prev][0].index - mediaList[next][0].index;
    });

    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const list = mediaList[key];
      const stateContent = await getObjectAsync(key);
      for (let j = 0; j < list.length; j++) {
        const item = list[j];
        globalThis.eventEmitter.emit('LessonDownload', {
          data: ClassDownloadUtil.getDownloadingMessage(lessonItem.sessions[item.index].title),
          classId: lessonItem.classId
        });
        if (cancelToken && cancelToken.cancelled) {
          return false;
        }
        const downloadResumable = FileSystem.createDownloadResumable(
          getAccessUrl(item.value, stateContent.content[item.i]),
          getFilePath(item.mediaKey),
          {},
          (downloadProgress) => {
            if (cancelToken && cancelToken.cancelled) {
              downloadResumable.pauseAsync(); // 暂停下载任务
            } else {
              const { totalBytesWritten, totalBytesExpectedToWrite } = downloadProgress;
              globalThis.eventEmitter.emit('LessonDownload', {
                data: ClassDownloadUtil.getDownloadingMessage(
                  `${lessonItem.sessions[item.index].title}`,
                  `${convertFileSizeToString(totalBytesWritten)} / ${convertFileSizeToString(totalBytesExpectedToWrite)}`
                ),
                classId: lessonItem.classId
              });
            }
          }
        );

        const { uri } = await downloadResumable.downloadAsync();

        stateContent.content[item.i].session.content[item.j].value = uri;
        stateContent.content[item.i].session.content[item.j].isLocal = true;
        stateContent.content[item.i].delayLoadImage = false;
        if (item.type === 'image') {
          // Resize and get base64 data for the image
          const manipulatedImage = await ImageManipulator.manipulateAsync(
            uri,
            [{ resize: { width: 400 } }], // Adjust width as needed
            { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG, base64: true }
          );

          // Save the base64 data directly
          stateContent.content[item.i].session.content[item.j].base64 =
            `data:image/jpeg;base64,${manipulatedImage.base64}`;
        }
      }
      if (cancelToken && cancelToken.cancelled) {
        return false;
      }
      await resetObjectAsync(key, stateContent);
    }
    return true;
  } else {
    return false;
  }
};
// Download all class content
export const downloadLessonAll = async ({ sessions, lesson, top }, cancelToken) => {
  // Filter sessions from lesson
  const mediaList = {};
  // Process sessions in batches of three
  for (let i = 0; i < sessions.length; i += 3) {
    const tasks = [];
    sessions.slice(i, i + 3).forEach((item, index) => {
      tasks.push(
        downloadLessonSession({ lesson: { ...item, classId: lesson.classId }, top, index: index + i }, mediaList)
      );
    });
    if (cancelToken && cancelToken.cancelled) {
      return false;
    }
    await Promise.all(tasks); // Wait for all three to complete
  }
  // Download media files
  const downloadResult = await downloadMedia(mediaList, { classId: lesson.classId, sessions }, cancelToken);
  if (downloadResult) {
    // Add lesson to the downloaded lessons list
    const lessons = await getObjectAsync(DownloadLessonListKey);
    resetObjectAsync(DownloadLessonListKey, [...(lessons || []), lesson]);
    return true;
  } else {
    return false;
  }
};
// Delete all downloaded class content
export const deleteDownloadLessonAll = async ({ sessions, classId }) => {
  // Remove all cached units
  sessions.forEach((item, index) => {
    const key = getDownloadLessonSessionKey({ id: classId, file: item.lesson, index });
    // Read media keys within and delete them
    getObjectAsync(key).then(({ content }) => {
      const mediaList = {};
      getMediaList(content, key, index, mediaList, classId);
      // Delete media files
      mediaList[key] &&
        mediaList[key].forEach((item) => {
          deleteObjectAsync(getFilePath(item.mediaKey));
        });
      deleteObjectAsync(key);
    });
  });
  // Remove the lesson from downloaded lessons list
  const lessons = (await getObjectAsync(DownloadLessonListKey)) || [];
  resetObjectAsync(
    DownloadLessonListKey,
    lessons.filter((item) => item.classId !== classId)
  );
};
