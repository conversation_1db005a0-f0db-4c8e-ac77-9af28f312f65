import { getCurrentUser } from '@/utils/user';
import { checkIsDownload } from '@/utils/lesson/index';
import { logEvent } from '@/utils/logger';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const goToLesson = async (lesson: any, navigation: any) => {
  getCurrentUser().setPropertyAsync('classOrder', { [lesson.name]: Date.now() });
  let downloadState;
  if (lesson.allowDownload) {
    downloadState = (await checkIsDownload(lesson.classId)) ? 2 : 1;
  }
  logEvent('goToLesson', { id: lesson.classId });
  navigation('LessonTableOfContent', {
    lesson: lesson,
    downloadState
  });
};
