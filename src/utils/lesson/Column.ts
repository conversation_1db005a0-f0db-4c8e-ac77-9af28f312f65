import * as Device from 'expo-device';
import { useWindowDimensions } from 'react-native';
import { useContext, useMemo } from 'react';
import { AppContext } from '@/context/AppContext';

// Phone/Default - 2 columns, Tablet - 4 columns, TV/DESKTOP - 6 columns

export const getColumns = () => {
  switch (Device.deviceType) {
    case Device.DeviceType.TV:
    case Device.DeviceType.DESKTOP:
      return 6;
    case Device.DeviceType.TABLET:
      return 4;
    default:
      return 2;
  }
};

export const useColumn = () => {
  const itemMargin = 10;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const context = useContext(AppContext) as any;
  const columns = getColumns();
  const { width } = useWindowDimensions();
  const itemWidth = useMemo(() => {
    const screenWidth = width - context.insets.left - context.insets.right;
    return (screenWidth - itemMargin * (columns + 1)) / columns;
  }, [width, context, columns]);
  return { columns, itemWidth, itemMargin };
};
