import { deleteDownloadLessonAll, downloadLessonAll } from '@/utils/lesson/index';

import { convertFileSizeToString } from '@/utils/helper';
import { i18n2 } from '@/utils/i18n2';
import { showMessage } from '@/components/MessageBox';

interface SessionItem {
  lesson: string;
  title: string;
}
interface DownloadParams {
  lesson: { classId: number; classNameChs: string; size: number; [key: string]: string | number };
  sessions: SessionItem[];
  top: number;
}

let downloadingList: DownloadParams[] = [];
let isDownloading: DownloadParams | null = null; // holds current classId if downloading
let cancelToken: { cancelled: boolean } | null = null;

// Check if a specific class is currently in the download queue
export function checkIsDownloading(classId: number): boolean {
  return downloadingList.some((item) => item.lesson.classId === classId) || isDownloading?.lesson?.classId === classId;
}

// Start a new download by adding it to the queue
export async function startDownload(params: DownloadParams): Promise<void> {
  downloadingList.push(params);
  await processQueue();
}

// Cancel a download task
export function cancelDownload(classId: number): void {
  // If classId is currently downloading, cancel it
  let item: DownloadParams | null = null;
  if (isDownloading?.lesson?.classId === classId && cancelToken) {
    cancelToken.cancelled = true;
    item = isDownloading;
  } else {
    item = downloadingList.find((item) => item.lesson.classId === classId) || null;
    downloadingList = downloadingList.filter((item) => item.lesson.classId !== classId);
  }
  // Remove the task from the queue if it hasn't started yet
  if (item) {
    deleteDownloadLessonAll({ sessions: item.sessions, classId });
  }
}

// Main download function, handling each session and checking for cancellation
async function downloadAll(params: DownloadParams, cancelToken: { cancelled: boolean }) {
  const dealFail = () => {
    if (!cancelToken?.cancelled) {
      deleteDownloadLessonAll({ sessions: params.sessions, classId: params.lesson.classId });
      showMessage({
        message: params.lesson.classNameChs + i18n2.t('LessonTableScreen.DownloadFailed')
      });
      globalThis.eventEmitter.emit('LessonDownload', {
        data: getDownloadReadyMessage(),
        classId: params.lesson.classId
      });
    }
  };
  try {
    const downloadResult = await downloadLessonAll(params, cancelToken);
    if (!downloadResult) {
      dealFail();
      return false;
    } else {
      globalThis.eventEmitter.emit('LessonDownload', {
        data: getDownloadedMessage(),
        classId: params.lesson.classId
      });
      showMessage({
        type: 'success',
        message: params.lesson.classNameChs + i18n2.t('LessonTableScreen.DownloadSuccess')
      });
      return true;
    }
  } catch (e) {
    console.log('error', e);
    dealFail();
    return false;
  }
}

// Process the download queue
async function processQueue() {
  if (isDownloading || downloadingList.length === 0) {
    return;
  }
  const nextDownload = downloadingList.shift();
  if (!nextDownload) {
    return;
  }

  isDownloading = nextDownload;
  cancelToken = { cancelled: false };

  try {
    await downloadAll(nextDownload, cancelToken);
  } finally {
    isDownloading = null;
    cancelToken = null;
    await processQueue(); // Start the next download in the queue
  }
}

export const getDownloadingMessage = (message?: string, progress?: string) =>
  JSON.stringify({
    type: 'updateDownloadState',
    state: 'downloading',
    title: i18n2.t('LessonTableScreen.CancelDownload'),
    message: message
      ? i18n2.t('LessonTableScreen.DownloadingMediaMessage').replace('{value}', message) +
        (progress ? i18n2.t('LessonTableScreen.FileSizeProcess').replace('{value}', progress) : '')
      : i18n2.t('LessonTableScreen.DownloadingMessage')
  });
export const getDownloadReadyMessage = () =>
  JSON.stringify({
    type: 'updateDownloadState',
    state: 'download',
    title: i18n2.t('LessonTableScreen.Download'),
    message: i18n2?.tValue?.(
      'LessonTableScreen.OnlineModeMessage',
      convertFileSizeToString(isDownloading?.lesson?.size)
    )
  });
const getDownloadedMessage = () =>
  JSON.stringify({
    type: 'updateDownloadState',
    state: 'downloaded',
    title: i18n2.t('LessonTableScreen.DeleteDownload'),
    message: i18n2?.tValue?.(
      'LessonTableScreen.DownloadCompleteMessage',
      convertFileSizeToString(isDownloading?.lesson?.size)
    )
  });

export * as ClassDownloadUtil from '@/utils/lesson/ClassDownloadUtil';
