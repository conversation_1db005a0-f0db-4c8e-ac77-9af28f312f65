import { getChatServer, getCurrentUser } from '@/utils/user';
import cryptoUtils from '@/dataStorage/cryptoproxy/cryptoUtil';

import { i18n2 } from '@/utils/i18n2';
import { showMessage } from '@/components/MessageBox';

const io = require('socket.io-client');

export default class Chat {
  id = '';
  socket = null;
  useNewFav = false;
  newMessageCallback = () => {};
  deleteMessageCallback = () => {};
  connectedCallback = () => {};
  disconnectedCallback = () => {};
  reconnectedCallback = () => {};
  groupTagsCallback = () => {};
  newGroupInfoCallback = () => {};

  constructor({ id, getTags = true, shareInGroups = '', useNewFav = false }) {
    this.id = id;
    this.useNewFav = useNewFav;

    if (useNewFav) {
      getTags = false;
    }

    this.socket = io(getChatServer(), {
      transports: ['websocket'] // use WebSocket first, if available
    });

    this.socket.on('connect', () => {
      console.log(`[${this.socket.id}]connected!`);
      this.onConnected();
      this.connectedCallback();
    });

    this.socket.on('disconnect', (reason) => {
      console.log(`[${this.socket.id}]disconnect! ${reason}`);
      this.disconnectedCallback();
    });

    this.socket.on('reconnect', (error) => {
      console.log(`[${this.socket.id}]reconnect!`);
      this.reconnectedCallback();
    });

    this.socket.on('error', (error) => {
      // no need to show since we will reconnect
      console.log(error);
    });

    this.socket.on('connect_error', (error) => {
      console.log(error);
      // revert back to polling
      this.socket.io.opts.transports = ['polling'];
      this.socket.io.opts.upgrade = false;
    });

    this.socket.on('connect_timeout', (timeout) => {
      // no need to show since we will reconnect
      console.log(`connect_timeout: ${timeout}`);
    });

    this.socket.on('reconnect_error', (error) => {
      console.log(error);
    });

    this.socket.on('reconnect_failed', () => {
      alert('reconnect_failed');
    });

    this.socket.on('newMessages', (data) => {
      console.log('newMessages', data);
      if (getCurrentUser().getUseCryptoProxy()) {
        data = JSON.parse(cryptoUtils.decryptSession(data));
      }
      this.onNewMessage(data);
    });

    this.socket.on('deleteMessage', (data) => {
      console.log('deleteMessage', data);
      if (getCurrentUser().getUseCryptoProxy()) {
        data = JSON.parse(cryptoUtils.decryptSession(data));
      }
      this.deleteMessageCallback(data);
    });

    this.socket.on('groupTags', (data) => {
      console.log('groupTags', data);
      if (getCurrentUser().getUseCryptoProxy()) {
        data = JSON.parse(cryptoUtils.decryptSession(data));
      }
      this.groupTagsCallback(data);
    });

    this.socket.on('newGroupInfo', (data) => {
      console.log('newGroupInfo', data);
      if (getCurrentUser().getUseCryptoProxy()) {
        data = JSON.parse(cryptoUtils.decryptSession(data));
      }
      this.newGroupInfoCallback(data);
    });

    // load initial messages
    this.getMessagesAsync({ getTags, shareInGroups });
  }

  /*
  onConnected() {
    this.socket.emit('enterRoom', {
      chatId: this.id,
      accessToken: getCurrentUser().getAccessToken()
    });
  }
  */

  onConnected() {
    if (getCurrentUser().getUseCryptoProxy()) {
      try {
        const data = cryptoUtils.encryptSession({ chatId: this.id, accessToken: getCurrentUser().getAccessToken() });
        if (!data) {
          console.log('onConnected: invalid1');
          return;
        }
        this.socket.emit('enterRoom', [cryptoUtils.getSessionSecurityToken(), data].join('.'));
      } catch (error) {
        console.log('onConnected: ', error);
        return;
      }
    } else {
      this.socket.emit('enterRoom', {
        chatId: this.id,
        accessToken: getCurrentUser().getAccessToken()
      });
    }
  }

  onNewMessage(data) {
    if (!Array.isArray(data)) {
      alert('Invalid data received from server!');
      return;
    }

    const newMessages = [];
    data.map((newMessage) => {
      const user = newMessage.user;
      if (!user) {
        return;
      }

      let name = user.displayName || '';
      if (name.trim() === '') {
        name = newMessage.user.loginId || '';
      }

      const message = {
        ...newMessage,
        _id: newMessage.messageId || Math.round(Math.random() * 1000000),
        text: newMessage.message,
        createdAt: new Date(newMessage.time * 1000),
        user: {
          _id: newMessage.user.id || 0,
          name: name.trim(),
          email: user.loginId,
          isAnonymous: newMessage.user.loginId === null
        },
        tags: newMessage.tags,
        attachment: newMessage.attachment,
        time: newMessage.time
      };

      newMessages.unshift(message);
    });

    // sort the message by create time
    newMessages.sort((msg1, msg2) => {
      return msg2.createdAt - msg1.createdAt;
    });

    this.newMessageCallback(newMessages);
  }

  async getMessagesAsync({ from = '', to = '', tag = '', shareInGroups = '', search = '', getTags = false }) {
    let result;
    if (this.useNewFav && tag) {
      result = await globalThis.dsObject.getCollectionCategory(this.id, tag === 'GroupFavorite' ? 'group' : 'user');
    } else {
      let queryString = '';
      if (from) {
        queryString += `&from=${from}`;
      }
      if (to) {
        queryString += `&to=${to}`;
      }
      if (tag) {
        queryString += `&tag=${encodeURIComponent(tag)}`;
      }
      if (getTags) {
        queryString += `&getTags=${getTags}`;
      }
      if (shareInGroups) {
        queryString += `&shareInGroups=${shareInGroups}`;
      }
      if (search) {
        queryString += `&search=${encodeURIComponent(search)}`;
      }
      result = await globalThis.dsObject.getChatMessage(this.id, queryString.substring(1));
    }

    if (!result) {
      showMessage({
        message: i18n2.t('Errors.ErrMsgUpdate'),
        type: 'danger'
      });
      return;
    }

    if (Array.isArray(result.body.tags)) {
      this.groupTagsCallback(result.body.tags);
    }

    this.onNewMessage(result.body.messages);
  }

  async sendMessageAsync(data) {
    const result = await globalThis.dsObject.sendMessage(data);
    return result ? true : false;
  }

  async deleteMessageAsync(messageId) {
    const result = await globalThis.dsObject.deleteChatMessage(this.id, messageId);
    return result ? true : false;
  }

  closeChat() {
    this.socket.close();
  }
}
