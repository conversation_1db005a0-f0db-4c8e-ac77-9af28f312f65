import TopScreensChs from '@/locale/TopScreens/chs.json';
import TopScreensCht from '@/locale/TopScreens/cht.json';
import TopScreensEng from '@/locale/TopScreens/eng.json';
import commonChs from '@/locale/common/chs.json';
import commonCht from '@/locale/common/cht.json';
import commonEng from '@/locale/common/eng.json';
import i18next, { i18n } from 'i18next';
import screensChs from '@/locale/screens/chs.json';
import screensCht from '@/locale/screens/cht.json';
import screensEng from '@/locale/screens/eng.json';
import translationChs from '@/locale/i18n2_chs.json';
import translationCht from '@/locale/i18n2_cht.json';
import translationEng from '@/locale/i18n2_eng.json';

interface I18n2 extends i18n {
  tValue?: (key: string, v: string) => string;
}

console.log('start initI18n');
const instance: I18n2 = i18next.createInstance();
instance.init(
  {
    compatibilityJSON: 'v3',
    resources: {
      eng: {
        TopScreens: TopScreensEng,
        common: commonEng,
        screens: screensEng,
        translation: translationEng
      },
      cht: {
        TopScreens: TopScreensCht,
        common: commonCht,
        screens: screensCht,
        translation: translationCht
      },
      chs: {
        TopScreens: TopScreensChs,
        common: commonChs,
        screens: screensChs,
        translation: translationChs
      }
    },
    fallbackLng: 'eng',
    ns: ['TopScreens', 'common', 'screens', 'translation'],
    defaultNS: 'TopScreens',
    fallbackNS: ['common', 'screens', 'translation'],
    interpolation: {
      escapeValue: false // react already safes from xss => https://www.i18next.com/translation-function/interpolation#unescape
    }
  },
  (err) => {
    if (err) {
      return console.log('something went wrong loading i18next', err);
    }
  }
);

instance.tValue = (key, value) => {
  return instance.t(key)?.replace('{value}', value);
};

console.log('+++++++i18next inited.');

export { instance as i18n2 };
