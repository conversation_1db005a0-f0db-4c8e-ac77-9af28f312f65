import * as FileSystem from 'expo-file-system';

import { debounce } from 'lodash-es';
import { getCurrentUser } from '@/utils/user';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
let logs: any[] = [];

const getLogFile = () => {
  return `${FileSystem.documentDirectory}logs.txt`;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const logEvent = async (...args: any[]) => {
  const logToFile = !!getCurrentUser().getProperty('setting.logToFile');
  if (logToFile) {
    let data = new Date().toISOString();
    for (const item of args) {
      if (typeof item === 'object') {
        // This handles Error object gracefully
        data += ' ' + JSON.stringify(item, Object.getOwnPropertyNames(item));
      } else {
        data += ' ' + item;
      }
    }

    logs.push(data);
    debounceAppendLogAsync();
  }
};

const deleteEventsAsync = async () => {
  try {
    const file = getLogFile();
    const fileInfo = await FileSystem.getInfoAsync(file);
    if (fileInfo.exists) {
      await FileSystem.deleteAsync(file);
    }
  } catch (error) {
    console.error(error);
  }
};

const debounceAppendLogAsync = debounce(async () => {
  try {
    const file = getLogFile();
    const fileInfo = await FileSystem.getInfoAsync(file);
    let data = '';
    if (fileInfo.exists) {
      data = await FileSystem.readAsStringAsync(file, { encoding: 'utf8' });
    }

    data += logs.join('\n');
    logs = [];

    // max 5MB log file size
    const maxLogFileSize = 5 * 1024 * 1024;
    if (data.length > maxLogFileSize) {
      data = data.slice(data.length - maxLogFileSize);
    }
    await FileSystem.writeAsStringAsync(file, data, { encoding: 'utf8' });
  } catch (error) {
    console.error(error);
  }
}, 3000);

export { logEvent, deleteEventsAsync, getLogFile };
