import Constants, { ExecutionEnvironment } from 'expo-constants';

import { Platform } from 'react-native';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let GoogleSignin: any = {
  configure: () => {}
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let GoogleSigninButton: any = () => undefined;
GoogleSigninButton.Size = {
  Wide: 0
};
GoogleSigninButton.Color = {
  Dark: 0
};

export async function checkToShowGoogleSignin() {
  const country = await globalThis.dsObject.getCountryFromIP();
  console.log('country:' + country);

  // only imports google-signin when client isn't expo-go and country is not China
  if (
    Constants.executionEnvironment !== ExecutionEnvironment.StoreClient &&
    country !== 'cn' &&
    Platform.OS === 'android'
  ) {
    import('@react-native-google-signin/google-signin')
      .then((googleSignIn) => {
        GoogleSignin = googleSignIn.GoogleSignin;
        GoogleSigninButton = googleSignIn.GoogleSigninButton;
        console.log('Google Signin loaded.');
      })
      .catch((error) => {
        alert(error);
      });
  }
}
