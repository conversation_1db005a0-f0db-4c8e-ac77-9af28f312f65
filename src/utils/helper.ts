import * as Linking from 'expo-linking';

import { getCurrentUser, getHttpsServer } from '@/utils/user';

import { i18n2 } from '@/utils/i18n2';
import { showMessage } from '@/components/MessageBox';

const GBBytes = 1024 * 1024 * 1024;
const MBBytes = 1024 * 1024;
const KBBytes = 1024;

const contactPrefix = '!contact!';

export function convertFileSizeToString(size?: number) {
  if (size === undefined || size <= 0) {
    return '';
  }

  if (size > GBBytes) {
    return `(${(size / GBBytes).toFixed(2)}GB)`;
  } else if (size > MBBytes) {
    return `(${(size / MBBytes).toFixed(2)}MB)`;
  } else if (size > KBBytes) {
    return `(${(size / KBBytes).toFixed(2)}KB)`;
  } else {
    return `(${size}Byte)`;
  }
}

const isEmailValid = (value: string) => {
  // eslint-disable-next-line no-useless-escape
  return /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(value);
};

const isUserUniqueIdValid = (value: string) => {
  // eslint-disable-next-line no-useless-escape
  return /^[1-9]\d{6}$/.test(value);
};

export const isEmailOrUserUniqueIdValid = (value: string) => {
  return isEmailValid(value) || isUserUniqueIdValid(value);
};

export const updateHtmlFontSize = (html: string) =>
  html
    .replace(/font-size: 18px;/g, `font-size: ${getCurrentUser().getMediumFontSize()}px;`)
    .replace(/font-size: 16px;/g, `font-size: ${getCurrentUser().getSmallFontSize()}px;`)
    .replace(/font-size: 14px;/g, `font-size: ${getCurrentUser().getSmallMinusFontSize()}px;`);

export const getClassCoverUrl = (classId: number) => {
  // update every 1 day (new dayDelta)
  const dayDelta = Math.floor(Date.now() / 1000 / 60 / 60 / 24);
  return getHttpsServer(`/blob/public/lesson.${classId}.jpg?t=${dayDelta}`);
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const openUrl = async (url: string, navigation: any, context: { setLaunchUrlAsync: any }) => {
  console.log(url, navigation, context);
  try {
    if (url.startsWith(contactPrefix)) {
      const items = url.split('!');
      const contactUniqueId = items[2] || '';
      const contactDisplayName = items[3] || '';
      console.log('openUrl:', contactDisplayName, contactUniqueId);
      navigation.navigate('AcceptContactCard', {
        name: contactDisplayName,
        uniqueId: contactUniqueId
      });
      return;
    }

    const launchIdx = url.indexOf('/launch?code=');
    if (launchIdx !== -1) {
      const launchCode = url.slice(launchIdx + '/launch?code='.length);
      const code = 'idigest://' + launchCode ?? '';
      await context.setLaunchUrlAsync(code);
      return;
    }

    const lowerCaseUrl = url.toLowerCase();
    if (lowerCaseUrl.startsWith('https://zoom.us') || lowerCaseUrl.startsWith('https://meet.mybsf.org')) {
      // open zoom in browser
      Linking.openURL(url);
    } else if (/^https:\/\/(\w|-)+\.idigest.app|^https:\/\/idigest\.app/.test(lowerCaseUrl)) {
      // open "https://*idigest.app" in browser (e.g. jitsi meetings)
      // open join meeting on Android in browser (jitsi app cannot be opened from Android WebView)
      Linking.openURL(url);
    } else {
      const pos = url.lastIndexOf('.');
      // some files cannot be opened in embedded browser
      if (pos !== -1 && ['doc', 'docx', 'ppt', 'pptx'].indexOf(url.substring(pos + 1).toLowerCase()) !== -1) {
        Linking.openURL(url);
      } else if (lowerCaseUrl.startsWith('https://')) {
        navigation.navigate('WebApp', { url, enableShare: true });
      } else {
        Linking.openURL(url);
      }
    }
  } catch (error) {
    console.log(error);
    showMessage({
      message: i18n2.t('Error'),
      description: i18n2.t('InstallApp'),
      type: 'danger'
    });
  }
};

export const isVideo = (url: string) => {
  if (!url) {
    return false;
  }

  // 正则表达式匹配常见的视频文件扩展名
  const videoExtensionsRegex = /\.(mp4|mpeg|mov|webm|avi|mkv|wmv|flv|ogv)$/i;

  return videoExtensionsRegex.test(url);
};
