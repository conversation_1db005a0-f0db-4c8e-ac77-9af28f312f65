import AsyncStorage from '@react-native-async-storage/async-storage';
import Base64 from 'Base64';
import { Config } from '@/dataStorage/config';
import { i18n2 } from '@/utils/i18n2';
import { isNua } from '@/utils/deviceOrAppType';
import cryptoUtils from '@/dataStorage/cryptoproxy/cryptoUtil';

let currentUser;

export function getCurrentUser() {
  if (!currentUser) {
    currentUser = new User();
  }

  return currentUser;
}

export function getHttpsServer(url) {
  const base = getCurrentUser().getHttpsServer();
  if (!url) {
    return base;
  }

  if (url.startsWith('/')) {
    return base + url;
  }

  return `${base}/${url}`;
}

export function getChatServer() {
  return getCurrentUser().getChatServer();
}

function getDefaultLanguage() {
  if (!isNua) {
    // for iDigest and fishMeet default to chs
    return 'chs';
  } else {
    // for Nua default to eng
    return 'eng';
  }
}

function getDefaultBibleVersion() {
  const lang = getDefaultLanguage();
  let version = 'cuvs';
  switch (lang) {
    case 'chs':
      version = 'cuvs';
      break;
    case 'cht':
      version = 'cuvt';
      break;
    default:
      version = isNua ? 'niv2011' : 'asv';
      break;
  }
  console.log('bible is ' + version);
  return version;
}

function getBibleVersionForLanguage(language) {
  let bible = 'cuvs';
  switch (language) {
    case 'chs':
      bible = 'cuvs';
      break;
    case 'cht':
      bible = 'cuvt';
      break;
    default:
      bible = isNua ? 'niv2011' : 'asv';
  }
  console.log('bible version is:' + bible);

  return bible;
}

async function loadUser() {
  try {
    const value = await AsyncStorage.getItem(Config.LocalUserKey);
    if (value) {
      console.log('loadUser: ' + value);
      return JSON.parse(value);
    }
    console.log('loadUser: no user to load');
  } catch (error) {
    alert(error);
    console.log(error);
  }

  return null;
}

async function saveUserAsync(user) {
  try {
    if (user) {
      // console.log('saveUserAsync: ' + JSON.stringify(user));
      await AsyncStorage.setItem(Config.LocalUserKey, JSON.stringify(user));
    } else {
      console.log('deleteUser');
      await AsyncStorage.removeItem(Config.LocalUserKey);
    }
  } catch (error) {
    alert(error);
    console.log(error);
  }
}

export default class User {
  constructor() {
    this.reset();

    // Do not modify language in this.reset() to prevent changing language on logout
    this.language = getDefaultLanguage();

    // below are not in the reset, because they contain settings for the app
    // and should be preserved for the app
    this.setUpHttpsServer(Config.DefaultHttpsServer, false);
    this.myDownloadServer = Config.DefaultDownloadServer;
    this.myHttpsServer2 = Config.DefaultHttpsServer2;
    this.cryptoProxy = Config.DefaultCryptoProxy;
    this.useCryptoProxy = false;
  }

  reset() {
    this.cellphone = '';
    this.loggedOn = false;
    this.bibleVersion = getDefaultBibleVersion();
    this.bibleVersion2 = Config.DefaultBibleVersion2;
    this.fontSize = Config.DefaultFontSize;
    this.permissions = {};
    this.validBibles = null;
    this.email = '';
    this.accessToken = '';
    this.cryptoState = '';

    this.jwt = {
      userId: -1,
      loginId: 'N/A'
    };
    this.nickname = '';
    this.listeners = [];
    this.lessonProgress = {};
    this.lessonPreferGroup = {};
    this.property = {};

    cryptoUtils.reset();
  }

  isBibleVersionValid(version) {
    if (!this.validBibles) {
      this.validBibles = [];
      for (const lang in Config.BibleVersions) {
        const items = Config.BibleVersions[lang];
        for (const i in items) {
          this.validBibles.push(items[i].id);
        }
      }
    }

    return this.validBibles.indexOf(version) !== -1;
  }

  getUseCryptoProxy() {
    return this.useCryptoProxy || !!this.getProperty('setting.development');
  }

  setUseCryptoProxy(useCrypto) {
    this.useCryptoProxy = useCrypto;
  }

  async loadExistingUserAsync() {
    const existingUser = await loadUser();
    if (existingUser) {
      this.cellphone = existingUser.cellphone;
      if (Config.ValidLanguages.indexOf(existingUser.language) !== -1) {
        this.language = existingUser.language;
      }
      if (this.isBibleVersionValid(existingUser.bibleVersion)) {
        this.bibleVersion = existingUser.bibleVersion;

        // Fix some existing user settings
        if (this.bibleVersion === 'rcuvss') {
          this.bibleVersion = 'cuvs';
        } else if (this.bibleVersion === 'rcuvts') {
          this.bibleVersion = 'cuvt';
        }
      }
      if (this.isBibleVersionValid(existingUser.bibleVersion2)) {
        // we don't use the same version
        this.bibleVersion2 =
          existingUser.bibleVersion2 === existingUser.bibleVersion ? null : existingUser.bibleVersion2;
        // Fix some existing user settings
        if (this.bibleVersion2 === 'rcuvss') {
          this.bibleVersion2 = 'cuvs';
        } else if (this.bibleVersion2 === 'rcuvts') {
          this.bibleVersion2 = 'cuvt';
        }
      }
      if (existingUser.fontSize) {
        this.fontSize = existingUser.fontSize;
        if (this.fontSize < 1 || this.fontSize > 4) {
          this.fontSize = 2;
        }
      }
      if (existingUser.email) {
        this.email = existingUser.email;
      }
      if (existingUser.cryptoState) {
        this.cryptoState = existingUser.cryptoState;
        cryptoUtils.init(existingUser.cryptoState);
      }
      if (existingUser.accessToken) {
        if (!this.setAccessTokenInternal(existingUser.accessToken)) {
          return;
        }
      }
      if (existingUser.nickname) {
        this.nickname = existingUser.nickname;
      }
      if (existingUser.lessonProgress) {
        this.lessonProgress = existingUser.lessonProgress;
      }
      if (existingUser.lessonPreferGroup) {
        this.lessonPreferGroup = existingUser.lessonPreferGroup;
      }
      if (existingUser.property) {
        this.property = existingUser.property;
      }
      if (existingUser.myHttpsServer) {
        this.setUpHttpsServer(existingUser.myHttpsServer, false);
      }
      if (existingUser.cryptoProxy) {
        this.setCryptoProxy(existingUser.cryptoProxy, false);
      }
      if (existingUser.myHttpsServer2) {
        this.myHttpsServer2 = existingUser.myHttpsServer2;
      }
      if (existingUser.thirdPartyLogin !== undefined) {
        this.thirdPartyLogin = existingUser.thirdPartyLogin;
      } else {
        this.thirdPartyLogin = false;
      }

      if (this.jwt && this.jwt.userId !== -1) {
        this.loggedOn = true;
        this.initUser();
      }
      // test set the permission.chat as true
      this.permissions.chat = true;
    }
  }

  setUpHttpsServer(url, save = true) {
    const uri = new URL(url);
    this.myHttpsServer = `https://${uri.hostname}${uri.pathname === '/' ? '' : uri.pathname}`; // no ending "/"
    // Note: chat server (socket.io) do not use sub-domain
    this.chatServer = `https://${uri.hostname}`;
    console.log({ myHttpsServer: this.myHttpsServer, chatServer: this.chatServer });
    if (save) {
      saveUserAsync(this.getUserInfo()).then(() => {
        this.logUserInfo();
      });
    }
  }

  setUpHttpsServer2(url) {
    this.myHttpsServer2 = url;
    saveUserAsync(this.getUserInfo()).then(() => {
      this.logUserInfo();
    });
  }

  getHttpsServer2() {
    return this.myHttpsServer2;
  }

  getHttpsServer() {
    return this.myHttpsServer;
  }

  setUpDownloadServer(url) {
    this.myDownloadServer = url;
  }

  getDownloadServer() {
    return this.myDownloadServer;
  }

  getCryptoProxy() {
    return this.cryptoProxy + '/api';
  }

  setCryptoProxy(url, save = false) {
    this.cryptoProxy = url;
    if (save) {
      saveUserAsync(this.getUserInfo()).then(() => {
        this.logUserInfo();
      });
    }
  }

  getChatServer() {
    if (this.getUseCryptoProxy()) {
      return this.cryptoProxy;
    }
    return this.chatServer;
  }

  isLoggedOn() {
    return this.loggedOn;
  }

  getCellphone() {
    if (!this.loggedOn) {
      return '';
    }
    return this.cellphone;
  }

  getLanguage() {
    return this.language;
  }

  getLanguageDisplayName() {
    const language = this.getLanguage();
    for (let i in Config.Languages) {
      if (language === Config.Languages[i].Value) {
        return Config.Languages[i].DisplayName;
      }
    }
    return null;
  }

  async setCellphoneAsync(celphone) {
    if (!this.loggedOn) {
      return;
    }

    this.cellphone = celphone;
    await saveUserAsync(this.getUserInfo());
    this.logUserInfo();
  }

  async setLanguageAsync(language) {
    const prevLanguage = this.language;
    console.log('setLanguageAsync:', language);
    this.language = language;
    i18n2.changeLanguage(language);

    if (this.loggedOn) {
      if (prevLanguage !== language) {
        globalThis.eventEmitter.emit('languageChanged');
        await saveUserAsync(this.getUserInfo());
        this.logUserInfo();
      }

      await this.setBibleVersionAsync(getBibleVersionForLanguage(this.language));
    }
  }

  async setFontSizeAsync(size) {
    if (!this.loggedOn) {
      return;
    }

    this.fontSize = size;
    globalThis.eventEmitter.emit('fontChanged');
    await saveUserAsync(this.getUserInfo());
    this.logUserInfo();
  }

  getFontSize() {
    if (!this.loggedOn) {
      return Config.DefaultFontSize;
    }
    return this.fontSize;
  }

  getX9LargeFontSize() {
    return 32 + this.getFontSize() * 3;
  }

  getX8LargeFontSize() {
    return 30 + this.getFontSize() * 3;
  }

  getX7LargeFontSize() {
    return 28 + this.getFontSize() * 3;
  }

  getX6LargeFontSize() {
    return 26 + this.getFontSize() * 3;
  }

  getX5LargeFontSize() {
    return 24 + this.getFontSize() * 3;
  }

  getX4LargeFontSize() {
    return 22 + this.getFontSize() * 3;
  }

  getX3LargeFontSize() {
    return 20 + this.getFontSize() * 3;
  }

  getX2LargeFontSize() {
    return 18 + this.getFontSize() * 3;
  }

  getXLargeFontSize() {
    return 16 + this.getFontSize() * 3;
  }

  getLargeFontSize() {
    return 14 + this.getFontSize() * 3;
  }

  getMediumFontSize() {
    return 12 + this.getFontSize() * 3;
  }

  getSmallFontSize() {
    return 10 + this.getFontSize() * 3;
  }

  getSmallMinusFontSize() {
    return 8 + this.getFontSize() * 3;
  }

  getXSmallFontSize() {
    return 6 + this.getFontSize() * 3;
  }

  getXXSmallFontSize() {
    return 4 + this.getFontSize() * 3;
  }

  getX3SmallFontSize() {
    return 2 + this.getFontSize() * 3;
  }

  getBibleVersion() {
    return this.bibleVersion;
  }

  async setBibleVersionAsync(version) {
    if (this.bibleVersion === version) {
      return;
    }

    this.bibleVersion = version;
    globalThis.eventEmitter.emit('bibleChanged', { bibleVersion: version });
    await saveUserAsync(this.getUserInfo());
    this.logUserInfo();
  }

  getBibleVersion2() {
    return this.bibleVersion2;
  }

  async setBibleVersion2Async(version) {
    if (this.bibleVersion2 === version) {
      return;
    }

    globalThis.eventEmitter.emit('bibleChanged', { bibleVersion2: version });
    this.bibleVersion2 = version;
    await saveUserAsync(this.getUserInfo());
    this.logUserInfo();
  }

  getBibleVersionDisplayName() {
    const version = this.getBibleVersion();
    for (let lang in Config.BibleVersions) {
      const data = Config.BibleVersions[lang];
      for (let i in data) {
        if (version === data[i].id) {
          return data[i].name;
        }
      }
    }
    return null;
  }

  getBibleVersion2DisplayName() {
    const version = this.getBibleVersion2();
    for (let lang in Config.BibleVersions) {
      const data = Config.BibleVersions[lang];
      for (let i in data) {
        if (version === data[i].id) {
          return data[i].name;
        }
      }
    }
    return null;
  }

  async logoutAsync() {
    if (this.loggedOn) {
      this.reset();

      //user data is cleared, but user object contains settings such as
      //server URL etc, these needs to be saved/preserved
      await saveUserAsync(this.getUserInfo());

      globalThis.eventEmitter.emit('userChanged', { reason: 'logout' });

      this.listeners.forEach((listener) => {
        listener.remove();
      });
      this.listeners = [];
    }
  }

  async loginAsync(thirdParty = false) {
    this.loggedOn = true;
    this.thirdPartyLogin = thirdParty;

    await this.setBibleVersionAsync(getBibleVersionForLanguage(this.language));

    await saveUserAsync(this.getUserInfo());
    this.logUserInfo();
    this.initUser();

    return true;
  }

  initUser() {
    globalThis.eventEmitter.emit('userChanged', { reason: 'login' });

    // when server returns 401, logout user
    this.listeners = globalThis.eventEmitter.addListener('serverLogout', () => {
      this.logoutAsync();
    });
  }

  getVersionNumber(version) {
    if (!version) {
      return 0;
    }

    // version is "a.b.c" or "a.b.c.d"
    const versionNumbers = version.split('.');
    let value = 0;
    for (const i in versionNumbers) {
      value = value * 1000 + parseInt(versionNumbers[i]);
    }
    if (versionNumbers.length === 3) {
      value = value * 1000;
    }
    return value;
  }

  logUserInfo() {
    console.log(JSON.stringify({ loggedOn: this.loggedOn, ...this.getUserInfo() }));
  }

  getUserInfo() {
    return {
      cellphone: this.cellphone,
      language: this.language,
      bibleVersion: this.bibleVersion,
      fontSize: this.fontSize,
      bibleVersion2: this.bibleVersion2,
      email: this.email,
      accessToken: this.accessToken,
      cryptoState: this.cryptoState,
      cryptoProxy: this.cryptoProxy,
      nickname: this.nickname,
      lessonProgress: this.lessonProgress,
      lessonPreferGroup: this.lessonPreferGroup,
      property: this.property,
      myHttpsServer: this.myHttpsServer,
      myHttpsServer2: this.myHttpsServer2,
      thirdPartyLogin: this.thirdPartyLogin
    };
  }

  getUserPermissions() {
    if (!this.loggedOn) {
      return {};
    }

    return this.permissions;
  }

  getQuestionId(question) {
    if (question.homiletics && this.permissions.isGroupLeader) {
      return 'H' + question.id;
    }

    return question.id;
  }

  getEmail() {
    return this.email;
  }

  getAccessToken() {
    return this.accessToken;
  }

  setCryptoStateAsync = async (cryptoState) => {
    this.cryptoState = cryptoState;
    await saveUserAsync(this.getUserInfo());
  };

  setAccessTokenAsync = async (token) => {
    if (this.setAccessTokenInternal(token)) {
      await saveUserAsync(this.getUserInfo());
    }
  };

  setAccessTokenInternal = (accessToken) => {
    try {
      this.jwt = parseJwt(accessToken);
    } catch (error) {
      alert(error);
      // wrong Jwt, logout
      this.logoutAsync();
      return false;
    }

    this.accessToken = accessToken;
    if (!this.cryptoState) {
      cryptoUtils.init({ accessToken });
    }
    return true;
  };

  getLessonProgress(id) {
    if (this.lessonProgress[id] !== undefined) {
      return this.lessonProgress[id];
    }

    return 0;
  }

  async setLessonProgressAsync(id, progress) {
    this.lessonProgress[id] = progress;
    await saveUserAsync(this.getUserInfo());
  }

  getLessonPreferGroup(classId) {
    return this.lessonPreferGroup[classId];
  }

  async setLessonPreferGroupAsync(classId, studyGroupId) {
    this.lessonPreferGroup[classId] = studyGroupId;
    await saveUserAsync(this.getUserInfo());
  }

  getProperty(name) {
    const value = this.property[name];
    // console.log(`user::getProperty ${name}=>${JSON.stringify(value)}`);
    return value;
  }

  async setPropertyAsync(name, value) {
    switch (typeof value) {
      case 'object':
        this.property[name] = { ...this.property[name], ...value };
        // console.log(`user::setProperty ${name}=>${JSON.stringify(this.property[name])}`);
        break;
      case 'function':
        console.log(`user::setProperty ${name} to function - not supported!`);
        return;
      default:
        this.property[name] = value;
        // console.log(`user::setProperty ${name}=>${value}`);
        break;
    }
    await saveUserAsync(this.getUserInfo());
  }

  async resetPropertyAsync(name, value) {
    this.property[name] = value;
    // console.log(`user::resetPropertyAsync ${name}=>${JSON.stringify(value)}`);
    await saveUserAsync(this.getUserInfo());
  }

  async deletePropertyAsync(name) {
    if (this.property[name] !== undefined) {
      delete this.property[name];
      await saveUserAsync(this.getUserInfo());
    }
  }

  async deleteAllSettingsAsync() {
    // Do not modify language in this.reset() to prevent changing language on logout
    this.language = getDefaultLanguage();

    // below are not in the reset, because they contain settings for the app
    // and should be preserved for the app
    this.setUpHttpsServer(Config.DefaultHttpsServer, false);
    this.myDownloadServer = Config.DefaultDownloadServer;
    this.myHttpsServer2 = Config.DefaultHttpsServer2;
    this.cryptoProxy = Config.DefaultCryptoProxy;
    this.useCryptoProxy = false;

    this.property = {};
    await saveUserAsync(this.getUserInfo());
  }

  async setUserInfoAsync(user) {
    if (user.email !== undefined) {
      this.email = user.email;
    }
    if (user.accessToken !== undefined) {
      if (!this.setAccessTokenInternal(user.accessToken)) {
        return;
      }
    }
    if (user.nickname !== undefined) {
      this.nickname = user.nickname;
    }
    if (user.cellphone !== undefined) {
      this.cellphone = user.cellphone;
    }
    if (user.thirdPartyLogin !== undefined) {
      this.thirdPartyLogin = user.thirdPartyLogin;
    }
    await saveUserAsync(this.getUserInfo());
    this.logUserInfo();
  }

  getNickName() {
    if (!this.loggedOn) {
      return '';
    }
    return this.nickname;
  }

  getJwtObj() {
    return this.jwt;
  }

  getUserId() {
    return this.jwt.userId;
  }

  getLoginId() {
    return this.jwt.loginId;
  }

  isTest() {
    return this.jwt.isTest;
  }

  isAdmin() {
    return this.jwt.isAdmin;
  }

  getLocale() {
    switch (this.getLanguage()) {
      case 'chs':
        return 'zh-CN';
      case 'cht':
        return 'zh-HK';
      default:
        return 'en-US';
    }
  }
}

// function to parse token
// in order to get the userid
function parseJwt(token) {
  let base64Url = token.split('.')[1];
  let base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  let jsonPayload = decodeURIComponent(
    Base64.atob(base64)
      .split('')
      .map((c) => {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      })
      .join('')
  );

  return JSON.parse(jsonPayload);
}
