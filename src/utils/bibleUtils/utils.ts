import { getCurrentUser } from '@/utils/user';
import { ETranslation, IVerseInfo, IVerse } from './types';
import { BOOK_TRANSLATIONS } from './constants';

/**
 * Get the default Bible translation based on the user's language
 * @returns Default translation
 */
export const getDefaultTranslation = (): ETranslation => {
  const language = getCurrentUser().language;

  // English environment
  if (language === 'eng') {
    return ETranslation.asv;
  }

  // Traditional Chinese environment
  if (language.includes('cht')) {
    return ETranslation.cuvt;
  }

  // Default to Simplified Chinese
  return ETranslation.cuvs;
};

/**
 * Parse a verse ID into its components
 * @param verseId The verse ID to parse
 * @returns Object containing bookId, chapter, and verse
 */
export function parseVerseId(verseId: number) {
  const bookId = Math.floor(verseId / 1000000);
  const chapter = Math.floor((verseId % 1000000) / 1000);
  const verse = verseId % 1000;
  return { bookId, chapter, verse };
}

/**
 * Get the name of a book in the specified translation
 * @param bookId The book ID
 * @param translation The translation to use
 * @returns The book name
 */
export function getBookName(bookId: number, translation = ETranslation.cuvs) {
  if (!BOOK_TRANSLATIONS?.[bookId]) {
    return '';
  }
  return BOOK_TRANSLATIONS?.[bookId]?.[translation];
}

/**
 * Get verse information from a verse ID
 * @param verseId The verse ID
 * @param translation The translation to use
 * @returns Verse information
 */
export const getVerseInfoByVerseId = (verseId: number, translation: ETranslation): IVerseInfo => {
  const { bookId, chapter, verse } = parseVerseId(verseId);
  return {
    bookId,
    bookName: getBookName(bookId, translation) || '',
    chapter,
    verse
  };
};

/**
 * Chapter data interface for scroll calculation
 */
export interface ChapterData {
  chapter: number;
  verses: IVerse[];
}

/**
 * Calculate precise verse scroll position based on line count and spacing
 * @param chapterData The chapter data containing verses
 * @param targetVerse The target verse number to scroll to
 * @param chapterHeight The total height of the chapter container
 * @param windowHeight The height of the screen/window
 * @param headerOffset The offset for header elements (like chapter selector)
 * @param fontSize The font size being used for verse content
 * @param windowWidth The width of the screen (for line wrapping calculation)
 * @returns The calculated scroll offset position
 */
export const calculateVerseScrollPosition = (
  chapterData: ChapterData,
  targetVerse: number,
  chapterHeight: number,
  windowHeight: number,
  headerOffset: number = 0,
  fontSize: number = 16, // Default medium font size
  windowWidth: number = 375 // Default width
): number => {
  if (!chapterData || !chapterData.verses.length) {
    return headerOffset;
  }

  // Style constants from BibleChapterScreen
  const chapterPaddingVertical = 15; // styles.chapterContainer.paddingVertical
  const verseMarginBottom = 10; // styles.verseContainer.marginBottom
  const verseContentMarginTop = 5; // styles.verseContentContainer.marginTop
  const translationMarginBottom = 4; // styles.translationContent.marginBottom
  const paragraphIndicatorHeight = 10; // styles.paragraphIndicator.height
  const contentPaddingHorizontal = 15; // contentContainerStyle.paddingHorizontal

  // Calculate effective content width for line wrapping
  const effectiveContentWidth = windowWidth - contentPaddingHorizontal * 2;

  // Line height calculation (fontSize + 4 as per the code)
  const lineHeight = fontSize + 4;
  const verseIdLineHeight = fontSize * 0.8 + 4; // smallFontSize is typically smaller

  // Calculate total lines and height for the entire chapter
  let totalChapterLines = 0;
  let totalChapterSpacing = chapterPaddingVertical * 2; // Top and bottom padding

  // Calculate lines and height from verse 1 to target verse
  let targetVerseLines = 0;
  let targetVerseSpacing = chapterPaddingVertical; // Top padding only

  for (const verse of chapterData.verses) {
    // Parse verse number (handle ranges like "1-3")
    const verseNum = typeof verse.verse === 'string' ? parseInt(verse.verse.split('-')[0] || '1') : verse.verse;

    // Calculate lines for this verse
    let verseLines = 0;
    let verseSpacing = verseMarginBottom + verseContentMarginTop + verseIdLineHeight;

    if (verse.translations) {
      Object.values(verse.translations).forEach((content) => {
        if (content) {
          // Estimate lines based on character count and effective width
          // Assuming average character width is fontSize * 0.6
          const avgCharWidth = fontSize * 0.6;
          const charsPerLine = Math.floor(effectiveContentWidth / avgCharWidth);
          const contentLines = Math.ceil(content.length / charsPerLine);
          verseLines += contentLines;
          verseSpacing += translationMarginBottom;
        }
      });
    }

    // Add paragraph indicator height if needed
    if (verse.paraStart === 1) {
      verseSpacing += paragraphIndicatorHeight;
    }

    // Add to totals
    totalChapterLines += verseLines;
    totalChapterSpacing += verseSpacing;

    // Add to target if this verse is before or at target
    if (verseNum <= targetVerse) {
      targetVerseLines += verseLines;
      targetVerseSpacing += verseSpacing;
    }
  }

  // Calculate total content height
  const totalContentHeight = totalChapterLines * lineHeight + totalChapterSpacing;
  const targetContentHeight = targetVerseLines * lineHeight + targetVerseSpacing;

  // If calculated height doesn't match actual height, use ratio adjustment
  const heightRatio = totalContentHeight > 0 ? chapterHeight / totalContentHeight : 1;
  const adjustedTargetHeight = targetContentHeight * heightRatio;

  // Center the target verse on screen
  const screenCenter = (windowHeight - headerOffset) / 2;
  const scrollOffset = Math.max(0, adjustedTargetHeight - screenCenter);

  return headerOffset - scrollOffset;
};
