import { IBibleData } from './types';
import { BO<PERSON>_TRANSLATIONS, BOOK_CATEGORIES } from './constants';

/**
 * Get Bible data including books and categories
 * @returns Bible data
 */
export function getBibleData(): IBibleData {
  const books = Object.entries(BOOK_TRANSLATIONS).map(([id, book]) => ({
    bookId: parseInt(id),
    ...book
  }));

  return {
    books,
    categories: BOOK_CATEGORIES
  };
}
