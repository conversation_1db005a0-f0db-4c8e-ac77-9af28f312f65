/**
 * Bible module types
 */

/**
 * Bible translation enum
 */
export enum ETranslation {
  cuvs = 'cuvs',
  cuvt = 'cuvt',
  asv = 'asv'
}

/**
 * Book interface
 */
export interface IBook {
  bookId: number;
  cuvs: string;
  cuvt: string;
  asv: string;
  abbr: string;
  abbr_s: string;
  abbr_t: string;
  numChapters: number;
}

/**
 * Category interface
 */
export interface ICategory {
  id: string;
  bookRange: [number, number];
  translations: {
    cuvs: string;
    cuvt: string;
    asv: string;
  };
}

/**
 * Bible data interface
 */
export interface IBibleData {
  books: IBook[];
  categories: ICategory[];
}

/**
 * Verse interface
 */
export interface IVerse {
  verse: number | string;
  verseId: number;
  content: string;
  paraStart: number;
  translations?: {
    [key in ETranslation]?: string;
  };
}

/**
 * Chapter interface
 */
export type IChapter = {
  [chapterNumber: string]: IVerse[];
};

/**
 * Verse info interface
 */
export interface IVerseInfo {
  bookId: number;
  bookName: string;
  chapter: number;
  verse: number;
}

/**
 * Bible JSON format interface for downloaded data
 */
export interface BibleJsonFormat {
  [verseId: string]: string;
}

/**
 * Search result interface
 */
export interface ISearchResult {
  bookId: number;
  bookName: string;
  chapter: number;
  verse: number;
  verseId: number;
  content: string;
}
