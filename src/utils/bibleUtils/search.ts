import { getObjectAsync } from '@/dataStorage/localStorage';
import { ETranslation, IChapter, ISearchResult } from './types';
import { getBibleData } from './data';

/**
 * Search Bible content
 * @param keyword The keyword to search for
 * @param translation The translation to search in
 * @param bookIdRange Optional range of books to search
 * @param nextToken Optional token for pagination
 * @param limit Maximum number of results to return
 * @returns Search results and next token
 */
export const searchBibleContent = async (
  keyword: string,
  translation: ETranslation,
  bookIdRange?: [number, number] | number, // Can be a range or a single bookId
  nextToken?: string | null,
  limit: number = 20
): Promise<{
  results: ISearchResult[];
  nextToken: string | null;
}> => {
  try {
    // Get Bible configuration data for book names
    const bibleConfig = getBibleData();
    const results: ISearchResult[] = [];

    // Parse nextToken
    let startBookId = 1;
    let startChapter = 1;
    let startVerse = 1;

    if (nextToken) {
      const [tokenBookId, tokenChapter, tokenVerse] = nextToken.split(':').map(Number);
      startBookId = tokenBookId || 1;
      startChapter = tokenChapter || 1;
      startVerse = tokenVerse || 1;
    }

    // Process bookIdRange parameter
    let startRange = 1;
    let endRange = 66;

    if (bookIdRange) {
      if (Array.isArray(bookIdRange)) {
        // If it's a range, use the start and end values
        [startRange, endRange] = bookIdRange;
      } else {
        // If it's a single bookId, set the range to that bookId
        startRange = endRange = bookIdRange;
      }
    }

    // Determine which books to search
    const searchBooks: number[] = [];
    for (let i = startRange; i <= endRange; i++) {
      if (i >= startBookId) {
        searchBooks.push(i);
      }
    }

    // Search each book
    for (const currentBookId of searchBooks) {
      // Get book name
      const book = bibleConfig.books.find((b) => b.bookId === currentBookId);
      if (!book) {
        continue;
      }

      const bookName = book[translation] || book.cuvs;

      // Get all chapters for this book
      const storageKey = `bible.chapters.${currentBookId}.${translation}`;
      const bookData = (await getObjectAsync(storageKey)) as IChapter;

      if (!bookData) {
        console.log(`No data found for book ${currentBookId}`);
        continue;
      }

      // Get all chapter numbers
      const chapters = Object.keys(bookData)
        .map(Number)
        .sort((a, b) => a - b);

      // Start searching from startChapter (if it's a new book, start from chapter 1)
      const startChapterIndex = currentBookId === startBookId ? chapters.findIndex((ch) => ch >= startChapter) : 0;

      if (startChapterIndex === -1) {
        continue;
      }

      // Search each chapter
      for (let i = startChapterIndex; i < chapters.length; i++) {
        const currentChapter = chapters[i] || 1;
        const verses = bookData[currentChapter] || [];

        // Start searching from startVerse (if it's a new chapter, start from verse 1)
        const startVerseIndex =
          currentBookId === startBookId && currentChapter === startChapter
            ? verses.findIndex((v) => {
                const verseNum = typeof v.verse === 'number' ? v.verse : parseInt(v.verse.toString());
                return verseNum >= startVerse;
              })
            : 0;

        if (startVerseIndex === -1) {
          continue;
        }

        const searchVerses = verses.slice(startVerseIndex);

        // Search each verse
        for (const verse of searchVerses) {
          if (verse.content && verse.content.toLowerCase().includes(keyword.toLowerCase())) {
            results.push({
              bookId: currentBookId,
              bookName,
              chapter: currentChapter,
              verse: typeof verse.verse === 'number' ? verse.verse : parseInt(verse.verse.toString()),
              verseId: verse.verseId,
              content: verse.content
            });

            // If results reach the limit, return
            if (results.length >= limit) {
              // Generate next token
              const lastResult = results[results.length - 1];
              const nextToken = `${lastResult?.bookId}:${lastResult?.chapter}:${(lastResult?.verse || 1) + 1}`;
              return { results, nextToken };
            }
          }
        }

        // Reset startVerse, as we only use it in the first iteration
        startVerse = 1;
      }

      // Reset startChapter, as we only use it in the first iteration
      startChapter = 1;
    }

    // If no more results
    return { results, nextToken: null };
  } catch (error) {
    console.error('Error searching Bible content:', error);
    return { results: [], nextToken: null };
  }
};
