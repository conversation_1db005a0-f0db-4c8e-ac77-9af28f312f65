function splitString(text, chars) {
  const result = [];
  let current = null;
  for (const ch of text) {
    if (chars.indexOf(ch) !== -1) {
      if (current !== null && current.trim()) {
        result.push(current);
        current = null;
      }
      result.push(ch);
    } else {
      if (current === null) {
        current = ch;
      } else {
        current += ch;
      }
    }
  }

  if (current !== null && current.trim()) {
    result.push(current);
  }

  return result;
}

function isValidChapterSeparator(ch) {
  return ':章'.indexOf(ch) !== -1;
}

function getVerse(arr) {
  // '1' or '1章' => '1'
  if (arr.length === 1 || arr.length === 2) {
    return arr[0];
  }

  // '1-3' or '1-3章' => '1-3'
  if ((arr.length === 3 || arr.length === 4) && arr[1] === '-') {
    return `${arr[0]}-${arr[2]}`;
  }

  // '1:29' or '1章29' or '1章29节' => '1:29'
  if ((arr.length === 3 || arr.length === 4) && isValidChapterSeparator(arr[1])) {
    return `${arr[0]}:${arr[2]}`;
  }

  // '1:29-31' or '1章29-31节' => '1:29-31'
  if ((arr.length === 5 || arr.length === 6) && isValidChapterSeparator(arr[1]) && arr[3] === '-') {
    return `${arr[0]}:${arr[2]}-${arr[4]}`;
  }

  // '1:29-2:12' or '1章29-2章12' or '1章29-2章12节' => '1:29-2:12'
  if (
    (arr.length === 7 || arr.length === 8) &&
    isValidChapterSeparator(arr[1]) &&
    arr[3] === '-' &&
    isValidChapterSeparator(arr[5])
  ) {
    return `${arr[0]}:${arr[2]}-${arr[4]}:${arr[6]}`;
  }

  // '1章29节-2章12' or '1章29节-2章12节' => '1:29-2:12'
  if (
    (arr.length === 8 || arr.length === 9) &&
    isValidChapterSeparator(arr[1]) &&
    arr[4] === '-' &&
    isValidChapterSeparator(arr[6])
  ) {
    return `${arr[0]}:${arr[2]}-${arr[5]}:${arr[7]}`;
  }

  return '';
}

function isValidChapterVerseChar(text, endPos) {
  return endPos < text.length && ' \t\n0123456789:-章节'.indexOf(text[endPos]) !== -1;
}

function addBibleRef(bibleReferences, text, book, verseText, start, end) {
  bibleReferences.push({
    text: text.trim(),
    book: book.trim(),
    verse: verseText.trim(),
    start,
    end
  });
}

let _bookNames = [];
let _bookChapters = {};

export function initializeAsNeeded(bibleData) {
  if (_bookNames.length === 0 || Object.keys(_bookChapters).length === 0) {
    _bookNames = [];
    _bookChapters = {};
    for (let index = 0; index < bibleData.length; index++) {
      const bookData = bibleData[index].split('|');
      const count = parseInt(bookData[bookData.length - 1]);
      for (let i = 0; i < bookData.length - 1; i++) {
        _bookNames.push(bookData[i]);
        _bookChapters[bookData[i]] = count;
      }
    }

    // reconstruct bookNames to move shorter names at later order
    _bookNames = _bookNames.sort((a, b) => {
      if (a.length > b.length) {
        return -1;
      } else if (a.length < b.length) {
        return 1;
      } else {
        return 0;
      }
    });
  }
}

export function getBibleRef(text) {
  const bibleReferences = [];
  for (const name of _bookNames) {
    let startPos = 0;
    let pos = text.indexOf(name, startPos);
    while (pos !== -1) {
      startPos = pos + name.length;
      let endPos = startPos;
      do {
        while (isValidChapterVerseChar(text, endPos)) {
          endPos++;
        }

        const verseText = text.substr(startPos, endPos - startPos);
        const arr = splitString(verseText, [':', '-', '章', '节']);
        const chapter = parseInt(arr[0]);
        if (chapter <= 0 || isNaN(chapter) || chapter > _bookChapters[name]) {
          break;
        }

        const verse = getVerse(arr);
        if (!verse) {
          break;
        }
        const txt = (startPos === pos + name.length ? name : '') + verseText;
        addBibleRef(bibleReferences, txt, name, verse, pos, endPos);
        pos = endPos + 1;

        let subChapter = chapter;
        while (endPos < text.length && text[endPos] === ',' && (startPos = ++endPos)) {
          while (isValidChapterVerseChar(text, endPos)) {
            endPos++;
          }
          if (endPos > startPos) {
            const subVerse = text.substr(startPos, endPos - startPos).trim();
            // subVerse has several format
            // 1) '8:12' or '8:1-3' - different chapter and verse
            const subVerseArr = splitString(subVerse, [':']);
            if (subVerseArr.length == 3) {
              subChapter = parseInt(subVerseArr[0]);
              addBibleRef(bibleReferences, subVerse, name, `${subChapter}:${subVerseArr[2]}`, pos, endPos);
            } else {
              // 2) '8', '1-3' - verse number from the same subChapter
              addBibleRef(bibleReferences, subVerse, name, `${subChapter}:${subVerse}`, pos, endPos);
            }
            pos = endPos + 1;
          }
        }
      } while (endPos < text.length && text[endPos] === ';' && (startPos = ++endPos));

      pos = text.indexOf(name, startPos);
    }
  }

  bibleReferences.sort(function (a, b) {
    if (a.start < b.start) {
      return -1;
    }

    if (a.start > b.start) {
      return 1;
    }

    // same starting point, longer book name is preferred (e.g. '耶利米书' > '书')
    if (a.book.length > b.book.length) {
      return -1;
    }

    if (a.book.length < b.book.length) {
      return 1;
    }

    return 0;
  });
  if (bibleReferences.length === 0) {
    return [{ type: 'text', value: text }];
  }

  const result = [];
  let start = 0;
  for (const item of bibleReferences) {
    if (item.start < start) {
      // skip this item. This item is "以赛亚书1:1" got parsed with one addition book of "书1:1"
      continue;
    }

    if (item.start !== start) {
      const value = text.substr(start, Math.max(0, item.start - start));
      if (value.length > 0) {
        result.push({ type: 'text', value });
      }
    }

    result.push({ type: 'bible', value: item.text, book: item.book, verse: item.verse });
    start = item.end;
  }

  if (start < text.length) {
    result.push({ type: 'text', value: text.substr(start) });
  }

  return result;
}
