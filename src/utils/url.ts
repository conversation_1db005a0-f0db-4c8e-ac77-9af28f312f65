import { getCurrentUser, getHttpsServer } from '@/utils/user';

export const getUrlByParams = (url: string, params: Record<string, string | undefined>) => {
  const resetParams: Record<string, string> = {};
  Object.keys(params).forEach((key) => {
    if (params[key]) {
      resetParams[key] = params[key] as string;
    }
  });
  const qrString = new URLSearchParams(resetParams).toString();
  const isParams = url.includes('?');
  return `${url}${isParams ? '&' : '?'}${qrString}`;
};

export const getChannelImageUrl = (channelId: number, value: string, programId?: number) => {
  return getUrlByParams(getHttpsServer(`/channels/${channelId}/content/${value}`), {
    token: getCurrentUser().getAccessToken(),
    programId: programId ? `${programId}` : '',
    time: new Date().getTime().toString()
  });
};
