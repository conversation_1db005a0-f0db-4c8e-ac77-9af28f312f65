/* eslint-disable @typescript-eslint/no-explicit-any */

import { Platform, TouchableOpacity, View, useWindowDimensions } from 'react-native';
import React, { useContext, useEffect } from 'react';

import { AppContext } from '@/context/AppContext';
import HomeScreenA from '@/screens/HomeScreen.js';
import HomeScreenB from '@/screens/HomeScreen/index';

import MomentsScreen from '@/screens/MomentsScreen';
import MyStudyGroupsScreen from '@/screens/MyStudyGroupsScreen/MyStudyGroupsScreen';
import SettingsScreen from '@/screens/SettingsScreen';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet, isIDigest, isIPad } from '@/utils/deviceOrAppType';
import { useNavigation } from '@react-navigation/native';
import useShareIntent from '@/hooks/useShareIntent';
import { Image, type ImageProps } from 'expo-image';
import { RedDot } from '@/components';
import { FmText } from '@/fishMeet/components/FmText';
import { Text } from 'react-native';
import { goToMeetingTab } from '@/utils/goToScreen';
import { getCurrentUser } from '@/utils/user';

type TabIconProps = {
  focused: boolean;
  focusedImage: ImageProps['source'];
  unfocusedImage: ImageProps['source'];
  text: string;
  showRedDot: boolean;
};

// TODO: add fishMeet TabIcon
export const TabIcon = (props: TabIconProps) => {
  const iconSize = 27;
  const fontSize = 12;
  const { focused, focusedImage, unfocusedImage, text, showRedDot } = props;

  const context = useContext(AppContext) as {
    insets: { top: number; bottom: number; left: number; right: number };
  };
  const { width } = useWindowDimensions();
  const iconWidth = width / 5;
  const marginBottom = isIPad ? context.insets.bottom : 0;
  return (
    <View style={{ alignItems: 'center', width: iconWidth, marginBottom, marginTop: 4 }}>
      <View>
        <Image style={{ height: iconSize, width: iconSize }} source={focused ? focusedImage : unfocusedImage} />
        {showRedDot ? <RedDot top={0} right={-5} /> : null}
      </View>
      {isFishMeet ? (
        <FmText numberOfLines={1} style={{ fontSize, marginTop: 3, fontWeight: focused ? 'bold' : 'normal' }}>
          {text}
        </FmText>
      ) : (
        <Text numberOfLines={1} style={{ fontSize, marginTop: 3, fontWeight: focused ? 'bold' : 'normal' }}>
          {text}
        </Text>
      )}
    </View>
  );
};

const IDigestBottomTab = createBottomTabNavigator();
let lockProcessedShareIntent = false;
export function IDigestBottomTabNavigator() {
  const context = useContext(AppContext) as {
    insets: { top: number; bottom: number; left: number; right: number };
    groups: any;
    hasNewChatMessage: boolean;
    inAppUpdateNeeded: boolean;
    inStoreUpdateNeeded: boolean;
    hasUnseenMoments: boolean;
    canOpenJitsi: boolean;
    canOpenFishMeet: boolean;
  };
  const { shareIntent, resetShareIntent } = useShareIntent();
  const navigation = useNavigation<any>();
  const channelMode = getCurrentUser().getProperty('setting.channelMode');
  const bibleMode = getCurrentUser().getProperty('setting.bibleMode');

  useEffect(() => {
    const lockProcessedShareIntentListener = globalThis.eventEmitter.addListener(
      'lockProcessedShareIntent',
      (value) => {
        lockProcessedShareIntent = value;
      }
    );

    // Force re-render when Bible mode changes
    const bibleModeChangedListener = globalThis.eventEmitter.addListener('bibleModeChanged', () => {
      // This will cause the component to re-render with the updated bibleMode value
      navigation.setParams({ update: Date.now() });
    });

    if (shareIntent) {
      console.log('Shared intent received:', shareIntent);
      const sendMsg = (group: any) => {
        if (group.isOneOnOneGroup) {
          navigation.replace('Chat', {
            chatId: group.groupId,
            title: group.name,
            group: {
              ...group,
              isGroupLeader: false
            },
            showGroupIcon: true,
            disableTagging: true,
            disableAtPeople: true,
            enableAudioChat: true,
            shareIntentData: shareIntent
          });
        } else {
          navigation.replace('Chat', {
            chatId: group.groupId,
            title: group.name,
            group,
            showGroupIcon: true,
            isOrgGroupChat: group.orgId !== 0,
            enableAudioChat: true,
            shareIntentData: shareIntent
          });
        }
      };
      if (context.groups && !lockProcessedShareIntent) {
        const choices = context.groups.filter((it: any) => it.status);
        const state = navigation.getState();
        const chatRoute = state.routes.find((route: { name: string }) => route.name === 'Chat');
        const method = chatRoute ? 'replace' : 'navigate';
        navigation[method]('Select', {
          choices,
          text: choices.length === 0 ? i18n2.t('MeetingUI.NotJoinedGroup') : undefined,
          title: i18n2.t('GroupsUI.SelectSession'),
          skipNavigateBack: true,
          getDisplayName: (choice: { name: any }) => choice.name,
          isCurrent: () => false,
          onSelect: (group: any) => {
            sendMsg(group);
          }
        });
      }
      resetShareIntent();
    }

    return () => {
      lockProcessedShareIntentListener.remove();
      bibleModeChangedListener.remove();
    };
  }, [context.groups, navigation, resetShareIntent, shareIntent]);

  return (
    <IDigestBottomTab.Navigator
      initialRouteName='HomeTab'
      screenOptions={{
        tabBarActiveTintColor: 'white',
        tabBarShowLabel: false,
        tabBarStyle: {
          height: Platform.select({ ios: 85, android: 60 }),
          backgroundColor: '#EEEEEE'
        },
        headerShadowVisible: true,
        headerTitleAlign: 'center'
      }}
      tabBar={({ state, navigation }) => {
        return (
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              marginBottom: context.insets.bottom
            }}>
            {state.routes.map((route, index) => {
              const isFocused = state.index === index;

              const onLongPress = () => {
                navigation.emit({
                  type: 'tabLongPress',
                  target: route.key
                });
              };

              let showRedDot = false;
              let focusedImage = null;
              let unfocusedImage = null;
              let text = '';
              switch (route.name) {
                case 'HomeTab':
                  showRedDot = false;
                  focusedImage = require('@/assets/images/icon-Discover-active.png');
                  unfocusedImage = require('@/assets/images/icon-Discover.png');
                  text = i18n2.t('DiscoverUI.Title');
                  break;
                case 'GroupTab':
                  showRedDot = context.hasNewChatMessage;
                  focusedImage = require('@/assets/images/icon-Group-active.png');
                  unfocusedImage = require('@/assets/images/icon-Group.png');
                  text = i18n2.t('GroupsUI.Title');
                  break;
                case 'MeetingTab':
                  showRedDot = false;
                  focusedImage = require('@/assets/images/icon-Conference.png');
                  unfocusedImage = require('@/assets/images/icon-Conference.png');
                  text = i18n2.t('MeetingUI.Title');
                  break;
                case 'MomentsTab':
                  showRedDot = context.hasUnseenMoments;
                  focusedImage = require('@/assets/images/icon-Moments-active.png');
                  unfocusedImage = require('@/assets/images/icon-Moments.png');
                  text = i18n2.t('MomentsUI.Title');
                  break;
                case 'SettingsTab':
                  showRedDot = context.inAppUpdateNeeded || context.inStoreUpdateNeeded;
                  focusedImage = require('@/assets/images/icon-Settings-active.png');
                  unfocusedImage = require('@/assets/images/icon-Settings.png');
                  text = i18n2.t('SettingsUI.Title');
                  break;
              }

              return (
                <TouchableOpacity
                  key={route.name}
                  activeOpacity={1}
                  onPress={() => {
                    if (route.name === 'MeetingTab') {
                      console.log('meeting tab clicked.');
                      goToMeetingTab(context.canOpenJitsi, context.canOpenFishMeet, context.groups, navigation);
                      return;
                    }

                    const event = navigation.emit({
                      type: 'tabPress',
                      target: route.key,
                      canPreventDefault: true
                    });

                    if (!isFocused && !event.defaultPrevented) {
                      // The `merge: true` option makes sure that the params inside the tab screen are preserved
                      navigation.navigate({ name: route.name, params: {}, merge: true });
                    }
                  }}
                  onLongPress={onLongPress}
                  style={{ marginBottom: 5 }}>
                  <TabIcon
                    focused={isFocused}
                    showRedDot={showRedDot}
                    focusedImage={focusedImage}
                    unfocusedImage={unfocusedImage}
                    text={text}
                  />
                </TouchableOpacity>
              );
            })}
          </View>
        );
      }}>
      <IDigestBottomTab.Screen
        name='HomeTab'
        component={channelMode || bibleMode ? HomeScreenB : HomeScreenA}
        options={{ headerShown: false }}
      />
      <IDigestBottomTab.Screen
        name='GroupTab'
        component={MyStudyGroupsScreen}
        options={MyStudyGroupsScreen.navigationOptions}
      />
      {isIDigest && (
        <>
          <IDigestBottomTab.Screen
            name='MeetingTab'
            component={channelMode || bibleMode ? HomeScreenB : HomeScreenA}
            options={{ headerShown: false }}
          />
          <IDigestBottomTab.Screen name='MomentsTab' component={MomentsScreen} />
        </>
      )}
      <IDigestBottomTab.Screen
        name='SettingsTab'
        component={SettingsScreen}
        options={SettingsScreen.navigationOptions}
      />
    </IDigestBottomTab.Navigator>
  );
}
