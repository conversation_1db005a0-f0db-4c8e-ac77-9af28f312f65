import * as Device from 'expo-device';
import * as Linking from 'expo-linking';
import * as Notifications from 'expo-notifications';
import * as Updates from 'expo-updates';

import { Alert, AppState, Platform, Vibration } from 'react-native';
import { cloneDeep, debounce, isEqual } from 'lodash-es';
import { getObjectAsync, setObjectAsync } from '@/dataStorage/localStorage';

import React from 'react';
import Sound from '@/utils/sound';
import { getCurrentUser } from '@/utils/user';
import { getPinYin } from '@/utils/pinyin';
import { i18n2 } from '@/utils/i18n2';
import { resetLessonList } from '@/utils/lesson';
import { showServerErrMessage } from '@/components/MessageBox';

export class AppContextProvider extends React.Component {
  constructor(props) {
    super(props);

    this.listeners = [];
    this.lastCheckAppUpdateDay = 0;
    this.lastRefreshAccessTokenTime = 0;
    this.isShowingNotification = false;
    this.lastSeenMomentId = -1;
    this.momentsCursor = -1;
    this.noMoreMoments = false;
    this.lastSeenClass = -1;
    this.latestClass = -1;

    this.state = {
      appState: AppState.currentState,
      notification: null,
      classes: [],
      groups: [],
      loadGroupsAsync: this.loadGroupsAsync,
      hasNewChatMessage: false,
      inAppUpdateNeeded: false,
      inStoreUpdateNeeded: false,
      insets: props.insets || { top: 0, bottom: 0, left: 0, right: 0 },
      checkUserUpdateAsync: this.checkUserUpdateAsync,
      bibleModeEnabled: !!getCurrentUser().getProperty('setting.bibleMode'),
      // moments
      moments: [],
      loadMomentsAsync: this.loadMomentsAsync,
      deleteMomentAsync: this.deleteMomentAsync,
      likeMomentAsync: this.likeMomentAsync,
      unlikeMomentAsync: this.unlikeMomentAsync,
      setSeenMomentAsync: this.setSeenMomentAsync,
      hasUnseenMoments: false,
      setMomentClicked: this.setMomentClicked,
      setMomentDiscussionRead: this.setMomentDiscussionRead,
      reloadMomentAsync: this.reloadMomentAsync,
      launchUrl: '',
      canOpenJitsi: false,
      canOpenFishMeet: false,
      hasNewClass: false,
      setUserLastSeenClassAsync: this.setUserLastSeenClassAsync,
      userProfile: {
        displayName: getCurrentUser().getNickName(),
        hideMessages: 0
      },
      loadUserProfileAsync: this.loadUserProfileAsync,
      refreshAccessTokenIfNeededAsync: this.refreshAccessTokenIfNeededAsync,
      setUserProfileAsync: this.setUserProfileAsync,
      currentProgress: getCurrentUser().getProperty('currentProgress'),
      setCurrentProgressAsync: this.setCurrentProgressAsync,
      sound: new Sound(),
      showAudioClose: true,
      setShowAudioClose: this.setShowAudioClose,
      audioPlayerMinimized: false,
      setAudioPlayerMinimized: this.setAudioPlayerMinimized,
      friends: null,
      loadFriendsAsync: this.loadFriendsAsync,
      topGroups: [],
      setTopGroupsAsync: this.setTopGroupsAsync,
      setLaunchUrlAsync: this.setLaunchUrlAsync,
      userTags: {},
      blocks: {},
      blockedBy: [],
      loadTagsAsync: this.loadTagsAsync
    };

    this.listeners.push(
      globalThis.eventEmitter.addListener('groupCreated', () => {
        // after group creation, we will update my classes
        this.checkUserUpdateAsync();
        // and update friends
        this.loadFriendsAsync();
      })
    );

    this.listeners.push(
      globalThis.eventEmitter.addListener('friendAdded', () => {
        this.loadFriendsAsync();
      })
    );

    this.listeners.push(
      globalThis.eventEmitter.addListener('userChanged', ({ reason }) => {
        // reset user data when user is changed
        if (reason === 'login') {
          this.loadFriendsAsync();
          this.checkTopGroupsAsync();
        } else {
          if (reason === 'logout') {
            this.state.sound.resetAsync();
          }
          this.setState({ friends: [], topGroups: [] });
        }
      })
    );

    this.listeners.push(
      globalThis.eventEmitter.addListener('channelModeChanged', (enabled) => {
        // Refresh relevant data
        this.checkUserUpdateAsync();
        // Update any channel-related state
        this.setState({ channelModeEnabled: enabled });
      })
    );

    this.listeners.push(
      globalThis.eventEmitter.addListener('bibleModeChanged', (enabled) => {
        // Refresh relevant data
        this.checkUserUpdateAsync();
        // Update any Bible-related state
        this.setState({ bibleModeEnabled: enabled });
      })
    );

    this.state.sound.enableAsync();
  }

  componentDidMount = async () => {
    this.clearNotificationsAsync();

    this._notificationSubscription = Notifications.addNotificationReceivedListener(this.handleNotification);
    this.appStateListener = AppState.addEventListener('change', this.handleAppStateChange);

    // show red dot and navigate user to store to update when needed
    this.updateInStoreNeededListener = globalThis.eventEmitter.addListener('inStoreUpdateNeeded', () => {
      this.setState({ inStoreUpdateNeeded: true });
    });

    if (getCurrentUser().isLoggedOn()) {
      // First time launch, load state from cache first to speed up rendering
      await this.loadStateAsync();

      // first time launch, check if we have any news
      this.checkUserUpdateAsync().then(() => {
        globalThis.eventEmitter.emit('firstLaunchReady');
      });

      this.refreshAccessTokenIfNeededAsync();
    }

    // check launch url
    // for debugging, click "exp://**************:19000?code=abc123"
    // "exp://**************:19000" is from "expo start"
    const launchUrl = await Linking.getInitialURL();
    await this.setLaunchUrlAsync(launchUrl);

    this.urlListener = Linking.addEventListener('url', this.launchUrlHandler);

    // check for canOpenJitsi
    this.checkCanOpenJistiAsync();
  };

  componentWillUnmount() {
    this.urlListener?.remove();
    this.updateInStoreNeededListener?.remove();
    this.appStateListener?.remove();
    this._notificationSubscription?.remove();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.insets !== this.props.insets) {
      this.debouncedUpdateInsets(this.props.insets);
    }
  }

  loadStateAsync = async () => {
    try {
      const data = await getObjectAsync('app.initialState');
      const classes = data?.classes ?? [];
      const groups = data?.groups ?? [];
      const moments = data?.moments ?? [];
      const topGroups = data?.topGroups ?? [];
      console.log(
        `loadStateAsync: classes: ${classes.length}, groups: ${groups.length}, moments: ${moments.length}, topGroups: ${topGroups.length}`
      );
      this.setState({ classes, groups, moments, topGroups });
    } catch (error) {
      console.log(error);
    }
  };

  launchUrlHandler = (event) => {
    this.setLaunchUrlAsync(event.url);
  };

  setLaunchUrlAsync = async (launchUrl) => {
    launchUrl = (launchUrl || '').trim();
    console.log('launchUrl => ' + launchUrl);
    this.setState({ launchUrl });
    if (!launchUrl) {
      return;
    }

    // change local proxy e.g. "idigest://<EMAIL>" or "exp://**************:19000?<EMAIL>"
    const pos = launchUrl.indexOf('proxy@');
    if (pos !== -1) {
      // check if we have access
      const httpsServer = 'https://' + launchUrl.substring(pos + 'proxy@'.length).toLowerCase();
      console.log(`new proxy => ${httpsServer}`);
      const result = await globalThis.dsObject.checkHttpsServer(httpsServer);
      if (result) {
        getCurrentUser().setUpHttpsServer(httpsServer);
      }

      return;
    }

    // otherwise we ask server to parse it
    const result = await globalThis.dsObject.setLaunchUrl(launchUrl);
    console.log(result);
    if (!result) {
      return;
    }
    if (result?.body?.cmd === 'joinStudyGroup') {
      const msg = result?.body?.data.status ? 'Common.JoinSuccess' : 'Common.JoinWaiting';
      Alert.alert(i18n2.t(msg));

      /* TODO: to navigate to the new group --- later
      const groupId = result.body.data.value;
      const groupResult = await globalThis.dsObject.getGroup(groupId);
      if (!groupResult) {
        return;
      }
      const group = groupResult.body;
      group.groupId = parseInt(groupId);

      console.log(group);
      goToChat(group, navigation);
      */
    }
  };

  debouncedUpdateInsets = debounce((insets) => {
    console.log('insets updates', insets);
    this.setState({ insets });
  }, 300);

  handleAppStateChange = async (nextAppState) => {
    const { appState } = this.state;
    console.log(`AppState: ${appState} => ${nextAppState}`);
    if (this.state.appState.match(/inactive|background/) && nextAppState === 'active') {
      console.log('App has come to the foreground!');

      globalThis.eventEmitter.emit('appInForeground');

      this.clearNotificationsAsync();

      if (getCurrentUser().isLoggedOn()) {
        // check if we have any news
        this.checkUserUpdateAsync();

        // refresh access token if needed
        this.refreshAccessTokenIfNeededAsync();

        // check for app update
        this.checkForAppUpdateAsync();

        // check for canOpenJitsi
        this.checkCanOpenJistiAsync();
      }
    } else if (this.state.appState === 'active' && nextAppState.match(/inactive|background/)) {
      console.log('App has come to the background!');
      globalThis.eventEmitter.emit('appInBackground');
    }

    this.setState({ appState: nextAppState });
  };

  handleNotification = (notification) => {
    if (this.state.appState !== 'active') {
      Vibration.vibrate();
    }
    console.log(notification);
    this.setState({ notification });
  };

  clearNotificationsAsync = async () => {
    if (Platform.OS === 'ios') {
      await Notifications.setBadgeCountAsync(0);
    }

    if (Platform.OS === 'android') {
      await Notifications.dismissAllNotificationsAsync();
    }
  };

  render() {
    return <AppContext.Provider value={this.state}>{this.props.children}</AppContext.Provider>;
  }

  refreshAccessTokenIfNeededAsync = async (force = false) => {
    const jwt = getCurrentUser().getJwtObj();
    if (!jwt || typeof jwt.exp !== 'number') {
      return;
    }

    if (!force) {
      const expDate = new Date(jwt.exp * 1000);
      const daysDiff = Math.floor((expDate - Date.now()) / 1000 / 60 / 60 / 24);
      // refresh token when it's going to expire in 30 days
      force = daysDiff < 30;
    }
    if (force) {
      const result = await globalThis.dsObject.refreshAccessToken();
      if (result && result.body.accessToken) {
        await getCurrentUser().setAccessTokenAsync(result.body.accessToken);
      }
    }
  };

  setTopGroups = (topGroups) => {
    if (!Array.isArray(topGroups)) {
      showServerErrMessage();
      return;
    }

    this.setState({ topGroups });
    setObjectAsync('app.initialState', { topGroups });
  };

  setClassesAsync = async (classes, myClasses = []) => {
    // Add downloaded but deleted lessons back to the list
    const addDownloadClasses = await resetLessonList(classes);
    if (!Array.isArray(addDownloadClasses)) {
      showServerErrMessage();
      return null;
    }

    addDownloadClasses.forEach((it) => {
      it.isMyClass = it.isMyClass || myClasses.indexOf(it.classId) !== -1;
    });

    this.setState({ classes: addDownloadClasses });
    await setObjectAsync('app.initialState', { classes: addDownloadClasses });
  };

  setGroups = (groups) => {
    if (!Array.isArray(groups)) {
      showServerErrMessage();
      return;
    }

    // Show red dot when has pending groups or new chat messages
    let hasNewChatMessage = false;
    for (const group of groups) {
      if (group.status === 0 || group.newChatMessage) {
        hasNewChatMessage = true;
        break;
      }
    }

    this.setState({ groups, hasNewChatMessage });
    setObjectAsync('app.initialState', { groups });
  };

  loadGroupsAsync = async () => {
    try {
      const result = await globalThis.dsObject.getGroups();
      if (!result) {
        return;
      }

      // hide one-on-one group with no messages in UI
      const groups = result.body.filter((it) => (it.isOneOnOneGroup && !it.lastMessageTime ? false : true));
      this.setGroups(groups);
    } catch (error) {
      console.log(error);
      Alert.alert(error);
    }
  };

  checkUserUpdateAsync = async () => {
    try {
      if (this.isShowingNotification) {
        console.log('Already show notification, skipping');
        return;
      }

      let defaultTemplate = await getObjectAsync('LessonTemplate.Default');
      if (!defaultTemplate) {
        defaultTemplate = { version: 0 };
      }

      const result = await globalThis.dsObject.checkUserUpdate(defaultTemplate.version);
      if (!result) {
        return;
      }

      const {
        groups,
        messages,
        moments,
        lastSeenMomentId,
        classes,
        myClasses,
        lastSeenClass,
        user,
        template,
        toplist
      } = result.body;

      if (template.version !== defaultTemplate?.version || !defaultTemplate.data) {
        await setObjectAsync('LessonTemplate.Default', template);
      }

      // set latestClass & lastSeenClass
      this.lastSeenClass = lastSeenClass;
      this.latestClass = -1;
      classes.forEach((it) => {
        if (it.classId > this.latestClass) {
          this.latestClass = it.classId;
        }
      });
      this.setState({ hasNewClass: this.latestClass > this.lastSeenClass });

      this.setTopGroups(toplist);
      this.setGroups(groups);
      this.setClassesAsync(classes, myClasses);

      // after getting latest moments, reset moments state
      this.momentsCursor = -1;
      this.noMoreMoments = false;
      this.setMoments([], moments);
      this.updateHasUnseenMoments(moments, lastSeenMomentId);

      if (Array.isArray(messages) && messages.length > 0) {
        let message = '';
        messages.forEach((item) => {
          message += `\n${item.message}\n-----------------------------------\n`;
        });

        this.isShowingNotification = true;
        Alert.alert(i18n2.t('App.GroupReminder'), message, [
          {
            text: i18n2.t('Common.Ok'),
            onPress: async () => {
              this.isShowingNotification = false;
              globalThis.dsObject.deleteMessage(messages[0].id);
            }
          }
        ]);
      }

      if (typeof user === 'object') {
        const userProfile = { ...this.state.userProfile, ...user };
        this.setState({ userProfile });
      }

      globalThis.eventEmitter.emit('afterGetUpdate');
    } catch (error) {
      console.log(error);
      Alert.alert(error);
    }
  };

  checkForAppUpdateAsync = async () => {
    if (!Device.isDevice || __DEV__) {
      return;
    }

    const today = new Date().getDate();
    if (this.lastCheckAppUpdateDay === today) {
      return;
    }

    this.lastCheckAppUpdateDay = today;

    try {
      const { isAvailable } = await Updates.checkForUpdateAsync();
      if (isAvailable) {
        this.setState({ inAppUpdateNeeded: true });
      }
    } catch (e) {
      console.log(JSON.stringify(e));
    }
  };

  checkCanOpenJistiAsync = async () => {
    const url = 'org.jitsi.meet://m.mycbsf.org/public';
    const canOpenJitsi = await Linking.canOpenURL(url);
    console.log(`canOpenJitsi:${canOpenJitsi}`);
    this.setState({ canOpenJitsi });

    const fishMeetUrl = 'org.gtinternational.meet://test';
    let canOpenFishMeet = await Linking.canOpenURL(fishMeetUrl);
    console.log(`canOpenFishMeet:${canOpenFishMeet}`);
    this.setState({ canOpenFishMeet });
  };

  // downloadLessonPackageAsync = async (lessonId) => {
  //   try {
  //     const result = await callWebServiceAsync(getHttpsServer(`lesson/package/${lessonId}`));
  //     const succeed = await showWebServiceCallErrorsAsync(result, 200);
  //     if (!succeed) {
  //       return;
  //     }

  //     const key = `lessonPackage-${lessonId}`;
  //     await setStringAsync(key, result.body);
  //   } catch (error) {
  //     console.log(error);
  //     Alert.alert(error);
  //   }
  // };

  loadMomentsAsync = async (forceReload = false) => {
    try {
      let currentMoments = [];
      if (forceReload) {
        this.momentsCursor = -1;
        this.noMoreMoments = false;
      } else {
        currentMoments = this.state.moments;
      }

      if (this.noMoreMoments) {
        console.log('No more content!');
        return;
      }

      const result = await globalThis.dsObject.loadMomentFrom(this.momentsCursor);
      if (!result) {
        return;
      }

      if (!Array.isArray(result.body)) {
        showServerErrMessage();
        return;
      }

      if (result.body.length === 0) {
        console.log('No more content!');
        this.noMoreMoments = true;
        return;
      }

      this.setMoments(currentMoments, result.body);
    } catch (err) {
      alert(err);
    }
  };

  setMoments = (currentMoments, newMoments) => {
    // merge result
    const moments = cloneDeep(currentMoments);
    const oldCursor = this.momentsCursor;
    for (const item of newMoments) {
      if (this.momentsCursor === -1 || item.id < this.momentsCursor) {
        this.momentsCursor = item.id;
      }

      if (moments.findIndex((it) => it.id === item.id) === -1) {
        moments.push(item);
      }
    }

    moments.sort((a, b) => {
      if (a.id > b.id) {
        return -1;
      }
      if (a.id < b.id) {
        return 1;
      }
      return 0;
    });

    this.setState({ moments });
    setObjectAsync('app.initialState', { moments });

    if (oldCursor === this.momentsCursor) {
      console.log('No more content!');
      this.noMoreMoments = true;
    }
  };

  deleteMomentAsync = async (id) => {
    try {
      const { moments } = this.state;
      const pos = moments.findIndex((it) => it.id === id);
      if (pos === -1) {
        console.log('Invalid id!' + id);
        return;
      }

      const result = await globalThis.dsObject.deleteMoment(id);
      if (result) {
        moments.splice(pos, 1);
        this.setState({ moments });
        setObjectAsync('app.initialState', { moments });
      }
    } catch (err) {
      alert(err);
    }
  };

  likeMomentAsync = async (id) => {
    try {
      const { moments } = this.state;
      const pos = moments.findIndex((it) => it.id === id);
      if (pos === -1) {
        console.log('Invalid id!' + id);
        return;
      }

      if (moments[pos].iLiked) {
        console.log('Already liked!');
        return;
      }

      const result = await globalThis.dsObject.likeMoment(id);
      if (!result) {
        return;
      }

      // server returns new like count
      moments[pos].iLiked = true;
      moments[pos].likedCount = result.body.likedCount;
      this.setState({ moments });
      setObjectAsync('app.initialState', { moments });
    } catch (err) {
      alert(err);
    }
  };

  unlikeMomentAsync = async (id) => {
    try {
      const { moments } = this.state;
      const pos = moments.findIndex((it) => it.id === id);
      if (pos === -1) {
        console.log('Invalid id!' + id);
        return;
      }

      if (!moments[pos].iLiked) {
        console.log('Already unliked!');
        return;
      }

      const result = await globalThis.dsObject.unlikeMoment(id);
      if (!result) {
        return;
      }

      // server returns new like count
      moments[pos].iLiked = false;
      moments[pos].likedCount = result.body.likedCount;
      this.setState({ moments });
      setObjectAsync('app.initialState', { moments });
    } catch (err) {
      alert(err);
    }
  };

  setSeenMomentAsync = async () => {
    const { moments } = this.state;
    if (moments.length === 0) {
      return;
    }

    // clear the red dot for read (not click) moments
    const id = moments[0].id;
    if (this.lastSeenMomentId === id) {
      console.log('skip unnecessary call');
      return;
    }

    const result = await globalThis.dsObject.setSeenMoment(id);
    if (!result) {
      return;
    }

    this.updateHasUnseenMoments(this.state.moments, id);
  };

  updateHasUnseenMoments = (moments, lastSeenMomentId) => {
    this.lastSeenMomentId = lastSeenMomentId;
    const hasUnseenMoments = moments.length > 0 && lastSeenMomentId < moments[0].id;
    this.setState({ hasUnseenMoments });
  };

  setMomentClicked = (id) => {
    const { moments } = this.state;
    const index = moments.findIndex((item) => item.id === id);
    if (index === -1) {
      return;
    }

    if (!moments[index].iClicked) {
      moments[index].iClicked = true;
      this.setState({ moments });
      setObjectAsync('app.initialState', { moments });
    }
  };

  setMomentDiscussionRead = (id) => {
    const { moments } = this.state;
    const index = moments.findIndex((item) => item.id === id);
    if (index === -1) {
      return;
    }

    if (moments[index].hasNewMessage) {
      moments[index].hasNewMessage = false;
      this.setState({ moments });
      setObjectAsync('app.initialState', { moments });
    }
  };

  reloadMomentAsync = async (id) => {
    const result = await globalThis.dsObject.loadMoment(id);
    if (!result || result.body.length !== 1) {
      return;
    }

    const { moments } = this.state;
    const index = moments.findIndex((item) => item.id === id);
    if (index === -1) {
      return;
    }

    if (isEqual(moments[index], result.body[0])) {
      console.log('same moment object, skip');
      return;
    }

    moments[index] = result.body[0];
    this.setState({ moments });
    setObjectAsync('app.initialState', { moments });
  };

  setUserLastSeenClassAsync = async () => {
    if (this.latestClass > this.lastSeenClass) {
      const result = await globalThis.dsObject.setUserLastSeenClass(this.latestClass);
      if (result) {
        this.lastSeenClass = this.latestClass;
        this.setState({ hasNewClass: false });
      }
    }
  };

  setUserProfileAsync = async (user) => {
    const userProfile = { ...this.state.userProfile, ...user };
    const result = await globalThis.dsObject.setUserProfile(userProfile);
    if (result) {
      this.setState({ userProfile });

      // update current user
      if (typeof userProfile.displayName === 'string') {
        getCurrentUser().setUserInfoAsync({ nickname: userProfile.displayName });
      }
    }
  };

  loadUserProfileAsync = async () => {
    const result = await globalThis.dsObject.getUserProfile();
    if (result) {
      this.setState({ userProfile: result.body });
    }
  };

  setCurrentProgressAsync = async (currentProgress) => {
    this.setState({ currentProgress });
    await getCurrentUser().setPropertyAsync('currentProgress', currentProgress);
  };

  setShowAudioClose = (showAudioClose) => {
    this.setState({ showAudioClose });
  };

  setAudioPlayerMinimized = (audioPlayerMinimized) => {
    this.setState({ audioPlayerMinimized });
  };

  loadFriendsAsync = async () => {
    try {
      const result = await globalThis.dsObject.getInviteUsers();
      if (!result) {
        return;
      }

      const thisUserId = getCurrentUser().getUserId();
      const friends = result
        .filter((it) => it.userId !== thisUserId)
        .map((user) => ({
          key: user.loginId,
          userId: user.userId,
          uniqueId: user.loginId || user.uniqueId,
          value: `${getPinYin(user.displayName)[0]}${user.displayName}`
        }));

      this.setState({ friends });
    } catch (err) {
      alert(err);
    }
  };

  checkTopGroupsAsync = async () => {
    try {
      const result = await globalThis.dsObject.getGroupViewTopList();
      if (!result) {
        console.log('checkTopGroupsAsync failed!');
        return;
      }

      const topGroups = result.status === 200 && Array.isArray(result.body) ? result.body : [];
      this.setState({ topGroups });
      setObjectAsync('app.initialState', { topGroups });
    } catch (err) {
      alert(err);
    }
  };

  setTopGroupsAsync = async (groupId, isCurrentTop) => {
    try {
      const { topGroups } = this.state;
      const index = topGroups.indexOf(groupId);
      if (isCurrentTop) {
        if (index === -1) {
          return;
        }
        topGroups.splice(index, 1);
      } else {
        if (index !== -1) {
          return;
        }
        topGroups.push(groupId);
      }

      this.setState({ topGroups });
      setObjectAsync('app.initialState', { topGroups });

      await globalThis.dsObject.setGroupViewTopList(topGroups);
    } catch (err) {
      alert(err);
    }
  };

  loadTagsAsync = async () => {
    const response = await globalThis.dsObject.getAllContactAsync();
    const meData = response.me;

    const newTags = meData.reduce((tags, contact) => {
      tags[contact.userId] = contact.tag;
      return tags;
    }, {});

    const newBlocks = meData.reduce((blocks, contact) => {
      blocks[contact.userId] = contact.block !== 0;
      return blocks;
    }, {});

    const blockedBy = response.blockedBy || [];

    this.setState({ userTags: newTags, blocks: newBlocks, blockedBy });
  };
}

export const AppContext = React.createContext({});
