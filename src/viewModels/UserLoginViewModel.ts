import { useCallback, useContext, useEffect, useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';

import { AppContext } from '@/context/AppContext';
import { Config } from '@/dataStorage/config';
import Constants from 'expo-constants';
import { GoogleSignin } from '@/utils/GoogleSignInHelper';
import { UserLoginModel } from '@/models/UserLoginModel';
import { i18n2 } from '@/utils/i18n2';
import { showServerErrMessage } from '@/components/MessageBox';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';

interface AppContextType {
  checkUserUpdateAsync: () => Promise<void>;
}
export const useUserLoginViewModel = () => {
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [emailError, setEmailError] = useState<boolean>(false);
  const [passwordError, setPasswordError] = useState<boolean>(false);
  const [selectedLang, setSelectedLang] = useState(UserLoginModel.getLanguage());

  const fontSize = UserLoginModel.getMediumFontSize();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const navigation = useNavigation<any>();
  const appContext = useContext(AppContext) as AppContextType;

  useSetNavigationOptions({
    headerLeft: () => null,
    title: i18n2.t('Login')
  });

  const configureGoogleSignIn = useCallback(() => {
    const webClientId = Constants.expoConfig?.extra?.['webClientId'];
    if (webClientId) {
      GoogleSignin.configure({
        webClientId
      });
    }
  }, []);

  useFocusEffect(configureGoogleSignIn);

  useEffect(() => {
    setEmailError(email.length >= 0 && email.length < 6);
    setPasswordError(password.length >= 0 && password.length < 6);
  }, [email, password]);

  const loginUser = async () => {
    if (emailError || passwordError) {
      return;
    }

    const { success, resetRequired, error } = await UserLoginModel.loginUser(email, password);
    if (success) {
      if (resetRequired) {
        navigation.navigate('UpdatePassword', { reset: true });
      } else {
        await appContext.checkUserUpdateAsync();
      }
    } else {
      if (error === 'NoAccessToken') {
        showServerErrMessage();
      }
      return;
    }
  };

  const navigateToForgot = () => {
    navigation.navigate('Forgot');
  };

  const navigateToCreateAccount = () => {
    navigation.navigate('CreateAccount');
  };

  const navigateToLanguageSelect = (titleControl?: React.ReactElement) => {
    navigation.navigate('Select', {
      choices: [0, 1, 2],
      title: 'Change language/更改语言',
      titleControl,
      isCurrent: (choice: number) => Config.Languages[choice]!.Value == selectedLang,
      getDisplayName: (choice: number) => Config.Languages[choice]!.DisplayName,
      onSelect: async (choice: number) => {
        const lang = Config.Languages[choice]!.Value;
        await UserLoginModel.setLanguage(lang);
        setSelectedLang(lang);
      }
    });
  };

  const handleGoogleSignIn = async () => {
    try {
      await UserLoginModel.loginWithGoogle();
      await appContext.checkUserUpdateAsync();
    } catch (error) {
      console.log(error);
    }
  };

  return {
    email,
    setEmail,
    password,
    setPassword,
    loginUser,
    emailError,
    passwordError,
    navigateToForgot,
    navigateToCreateAccount,
    navigateToLanguageSelect,
    fontSize,
    handleGoogleSignIn
  };
};
