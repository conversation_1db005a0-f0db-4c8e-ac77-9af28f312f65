import * as Clipboard from 'expo-clipboard';

import { useCallback, useContext, useEffect, useState } from 'react';

import { AppContext } from '@/context/AppContext';
import { MemberModel } from '@/models/MemberModel';
import { i18n2 } from '@/utils/i18n2';
import { showMessage } from '@/components/MessageBox';
import { useNavigation } from '@react-navigation/native';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';

interface Group {
  createdTime: number;
  disableMemberShowList: number;
  disappearingMessages: number;
  friendUserId?: number;
  groupId: number;
  isOneOnOneGroup: boolean;
  joinRequireLeaderApproval: number;
  lastMessageTime: number | null;
  lastModifiedTime: number;
  name: string;
  onlyLeaderCanPost: number;
  status: number;
  subTitle: string;
  isGroupLeader?: boolean;
  isOrgGroup?: boolean;
}

export const useMemberViewModel = (
  userId: number,
  hideChat: boolean,
  initialTagFromProps: string,
  isCurrentUserBlocking: boolean
) => {
  interface AppContextType {
    insets: {
      top: number;
      bottom: number;
      left: number;
      right: number;
    };
  }

  const [showFullScreenImage, setShowFullScreenImage] = useState<boolean>(false);
  const [showChatButton, setShowChatButton] = useState<boolean>(!hideChat);
  const { insets } = useContext(AppContext) as AppContextType;
  const [userTag, setUserTag] = useState(initialTagFromProps);
  const [initialTag, setInitialTag] = useState('');
  const [isCurrentUserBlockingViewedUser, setIsCurrentUserBlockingViewedUser] = useState(isCurrentUserBlocking);
  const [typingTimeout, setTypingTimeout] = useState<ReturnType<typeof setTimeout> | null>(null);
  const fontSize = MemberModel.getMediumFontSize();
  const { groups } = useContext(AppContext) as { groups: Group[] };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const navigation = useNavigation<any>();

  useSetNavigationOptions({
    title: i18n2.t('MemberScreen.Title')
  });

  const initiateChat = useCallback(
    async (friendId: string) => {
      const existingGroup = groups?.find((group: Group) => group.isOneOnOneGroup && group.subTitle === friendId);

      if (existingGroup) {
        navigation.popToTop();
        navigation.navigate('Chat', {
          chatId: existingGroup.groupId,
          title: existingGroup.name,
          group: { ...existingGroup, isGroupLeader: false },
          showGroupIcon: false,
          disableTagging: true,
          disableUserInfo: true,
          disableAtPeople: true,
          enableAudioChat: true
        });
        return;
      }

      const chatDetails = await MemberModel.initiateChat(friendId);
      if (chatDetails) {
        navigation.popToTop();
        navigation.navigate('Chat', {
          chatId: chatDetails.chatId,
          title: chatDetails.title,
          group: chatDetails.group,
          showGroupIcon: false,
          disableTagging: true,
          disableUserInfo: true,
          disableAtPeople: true,
          enableAudioChat: true
        });
      } else {
        return;
      }
    },
    [navigation, groups]
  );

  const copyEmailToClipboard = useCallback(async (email: string) => {
    await Clipboard.setStringAsync(email);
    showMessage({
      message: i18n2.t('GroupsScreen.CopyToClipboard'),
      description: email
    });
  }, []);

  useEffect(() => {
    const isCurrentUser = MemberModel.isCurrentUser(userId);
    setShowChatButton(!hideChat && !isCurrentUser);
  }, [userId, hideChat]);

  useEffect(() => {
    async function fetchUserInteractionDetails() {
      try {
        const contact = await MemberModel.fetchTagAndBlock(userId);
        const doesCurrentUserBlockViewedUser = contact.block === 1;

        setIsCurrentUserBlockingViewedUser(doesCurrentUserBlockViewedUser);

        if (contact.tag) {
          setInitialTag(contact.tag);
          setUserTag(contact.tag);
        }
      } catch (error) {
        console.error('Error fetching user interaction details:', error);
      }
    }

    fetchUserInteractionDetails();
  }, [userId]);

  const toggleFullScreenImage = () => {
    setShowFullScreenImage(!showFullScreenImage);
  };

  const toggleUserBlock = async () => {
    const newBlockStatus = !isCurrentUserBlockingViewedUser;

    const success = await MemberModel.setBlock(userId, newBlockStatus);
    if (success) {
      setIsCurrentUserBlockingViewedUser(newBlockStatus);
    } else {
      showMessage({ message: i18n2.t('MemberScreen.SetBlockFailed') });
    }
  };

  const setTag = useCallback(
    async (tag: string) => {
      const trimmedTag = tag.trim();
      if (trimmedTag === '' && tag !== '') {
        showMessage({ message: i18n2.t('MemberScreen.NonSpaceUserTag'), type: 'danger' });
        return;
      }

      const success = await MemberModel.setTag(userId, tag);
      if (success) {
        setUserTag(tag);
      } else {
        showMessage({ message: i18n2.t('MemberScreen.SetTagFailed'), type: 'danger' });
      }
    },
    [userId]
  );

  const handleUserTagChange = (newTag: string) => {
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }
    setUserTag(newTag);
    setTypingTimeout(
      setTimeout(() => {
        setTag(newTag);
      }, 1000)
    );
  };

  return {
    showFullScreenImage,
    initiateChat,
    toggleFullScreenImage,
    toggleUserBlock,
    showChatButton,
    copyEmailToClipboard,
    insets,
    setTag,
    initialTag,
    userTag,
    setUserTag: handleUserTagChange,
    isCurrentUserBlockingViewedUser,
    fontSize,
    handleUserTagChange
  };
};
