import { ETranslation, IBibleData, IChapter } from '@/utils/bibleUtils/types';
/* eslint-disable @typescript-eslint/no-explicit-any */
import { IChannel, IProgram } from '@/utils/channel/IChannel';

interface ApiResponse<T = any> {
  headers: any;
  body: T;
  status: any;
}

export interface dataServices {
  lastHttpErrorTime: Date | null;
  isSwitchingToBackup: boolean;

  getCountryFromIP(): Promise<string>;

  getHttpsServer(url: string): string;
  setLastHttpErrorTime(errorTime: Date | null): void;
  getLastHttpErrorTime(): Date | null;
  switchToBackupServer(): void;

  loginUser(
    loginId: string,
    password: string
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getUserAvatars(users: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getMeetingLink(groupId: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  checkHttpsServer(httpsServer: string): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  setLaunchUrl(launchUrl: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  joinGroupViaCode(joinCode: string): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  refreshAccessToken(): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getGroups(): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  checkUserUpdate(version: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  deleteMessage(messageId: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  loadMoment(momentsId: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  deleteMoment(momentsId: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  likeMoment(momentsId: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  unlikeMoment(momentsId: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  setSeenMoment(momentsId: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  loadMomentFrom(momentsCursor: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  setUserLastSeenClass(latestClass: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getUserProfile(): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  setUserProfile(userProfile: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getStudyProgress(groupId: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  registerDevice(token: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  unregisterDevice(token: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  sendMessage(body: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  deleteUser(): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  updateUserProfile(body: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getUserData(dataId: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  setUserData(
    dataId: number,
    body: any
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  postFeedback(body: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getInviteUsers(hideUserId?: number): Promise<Array<{
    id: any;
    userId: any;
    text: any;
    isSelected: any;
    hide: any;
    displayName: any;
  }> | null>;

  addUserEmail(
    groupId: number,
    email: any,
    isOrgInvite: boolean
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  respondToRequest(
    groupId: number,
    requestUserId: number,
    accept: boolean
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getGroup(groupId: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  createUser(body: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  resetPassword(body: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  inviteUser(
    email: string,
    greetings: string
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  createOneOnOne(friendId: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  clickMoment(momentsId: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  createMoment(body: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getMomentPreview(url: string): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getMoments(params?: { includeGroups?: boolean; groupId?: string; lastId?: number | undefined }): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  uploadMoments(loadUri: string): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  saveAnswer(
    classId: any,
    sessionIndex: any,
    body: any
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  setUserTagOrBlock(
    userId: number,
    tag?: string,
    block?: number
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  fetchUserTagAndBlock(userId: number): Promise<{
    tag: string;
    block: number;
  }>;

  getAllContactAsync(): Promise<{
    me: any[];
    blockedBy: any[];
  }>;

  createDiscussion(
    classId: number,
    week: number,
    questionId: number
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  deleteGroup(
    groupId: number,
    showUI: boolean,
    onDismissUI: undefined
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  leaveGroup(
    groupId: number,
    showUI: boolean,
    onDismissUI: undefined
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  createGroup(
    groupId: number,
    body: any
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getClassStudyProgress(
    groupId: number,
    classId: number
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  userHideMessages(
    groupId: number,
    hideMessages: any
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  removeUserFromGroup(
    groupId: number,
    classId: number,
    showUI: boolean,
    onDismissUI: undefined
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  groupUserDemote(
    groupId: number,
    userId: number,
    showUI: boolean,
    onDismissUI: undefined
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  groupUserPromote(
    groupId: number,
    userId: number,
    showUI: boolean,
    onDismissUI: undefined
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getAttendance(groupId: number): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  deleteAttendance(
    groupId: number,
    dateString: string
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  setAttendance(
    groupId: number,
    dateString: string,
    body: any
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  deleteCollection(
    chatId: number,
    source: any,
    messageId: number
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  setCollection(
    chatId: number,
    target: any,
    body: any
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getCollectionCategory(
    chatId: number,
    category: string
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getChatMessage(
    chatId: number,
    queryStr: string
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  deleteChatMessage(
    chatId: number,
    messageId: number
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getChatTagMessage(
    messageId: number,
    tag: string
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  setChatTagMessage(
    messageId: number,
    tag: string
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  deleteChatTagMessage(
    messageId: number,
    tag: string
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getLesson(
    lessonName: string,
    version: any
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  getClassCreateGuidePreview(): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  shareGroupLesson(
    groupId: number,
    classId: number
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  shareUserLesson(
    classId: number,
    userId: number
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  deleteGroupLesson(
    groupId: number,
    classId: number
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  deleteUserLesson(
    classId: number,
    userId: number
  ): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  setUserAvatars(localUri: string): Promise<boolean | null>;

  sendChatFileMessage(localUri: any, queryStr: string): Promise<boolean | null>;

  getGroupViewTopList(): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  setGroupViewTopList(body: number[]): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;
  getChannelOwned(): Promise<ApiResponse<IChannel[]> | null>;
  getChannelSubscribes(): Promise<ApiResponse<IChannel[]> | null>;
  getChannel(channelId: number): Promise<ApiResponse<IChannel> | null>;
  putChannelSubscribe(channelId: number): Promise<ApiResponse | null>;
  putChannelUnsubscribe(channelId: number): Promise<ApiResponse | null>;
  getProgramsRecommend(
    nextToken: string | null,
    search?: string
  ): Promise<ApiResponse<{ programs: IProgram[]; nextToken: null | string }> | null>;
  getPrograms(
    channelId: number,
    tag?: string,
    nextToken?: string | null
  ): Promise<ApiResponse<{ programs: IProgram[]; nextToken: null | string }> | null>;
  getUserProgramsByType(
    type: string,
    nextToken: string | null,
    search?: string
  ): Promise<ApiResponse<{ programs: IProgram[]; nextToken: null | string }> | null>;
  getProgram(id: number): Promise<ApiResponse<IProgram> | null>;
  postProgramLike(id: number): Promise<ApiResponse | null>;
  postProgramUnlike(id: number): Promise<ApiResponse | null>;
  postProgramCollect(id: number): Promise<ApiResponse | null>;
  postProgramUncollect(id: number): Promise<ApiResponse | null>;
  putProgramShare(id: number): Promise<ApiResponse | null>;
  putProgramViews(id: number): Promise<ApiResponse | null>;
  getBibleData(): Promise<ApiResponse<IBibleData> | null>;
  getBibleChapters(bookId: number, translation?: ETranslation): Promise<ApiResponse<IChapter> | null>;
  getBibleSearch(queryParams: string): Promise<ApiResponse<{
    results: Array<{
      bookId: number;
      bookName: string;
      chapter: number;
      verse: number;
      verseId: number;
      content: string;
    }>;
    nextToken: string | null;
  }> | null>;

  getPreMeetingData(): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;

  setPreMeetingData(body: any): Promise<{
    headers: any;
    body: any;
    status: any;
  } | null>;
}
