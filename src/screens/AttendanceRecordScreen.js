import { Alert, Dimensions, ScrollView, Text, TouchableOpacity, View } from 'react-native';

import { AntDesign } from '@expo/vector-icons';
import { AppContext } from '@/context/AppContext';
import { CheckBox } from '@rneui/themed';
import { Colors } from '@/styles/colors';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { LoadingIndicator } from '@/components';
import { OkButton } from '@/components/navigation/NavigationButtons';
import React from 'react';
import { cloneDeep } from 'lodash-es';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { sortByPinYin } from '@/utils/pinyin';

export default class AttendanceRecordScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ route }) => {
    const { date, selectDate, saveRecord } = route.params;
    const title = `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;
    return {
      headerTitle: () => (
        <>
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
              selectDate();
            }}>
            <View
              style={{
                borderColor: 'black',
                borderWidth: 1,
                borderRadius: 10,
                paddingVertical: 3,
                paddingHorizontal: 10,
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center'
              }}>
              <AntDesign name={'calendar'} size={25} color={'black'} />
              <Text style={{ fontSize: 16, paddingLeft: 10 }}>{title}</Text>
            </View>
          </TouchableOpacity>
        </>
      ),

      headerRight: () => (
        <OkButton
          right={true}
          onPress={() => {
            saveRecord();
          }}
        />
      )
    };
  };

  constructor(props) {
    super(props);

    const params = props.route.params;
    this.groupId = params.groupId;
    this.oldDate = params.createNew ? null : params.date;
    this.existingDate = params.existingDate;
    this.users = cloneDeep(params.users);
    this.allUsers = sortByPinYin(cloneDeep(params.allUsers), (item) => item.name);

    this.setLoadNeeded = params.setLoadNeeded;

    const attendance = [];
    const userChecked = {};
    this.users.forEach((user) => {
      userChecked[user.loginId] = 1;
      attendance.push({
        checked: true,
        ...user
      });
    });

    this.allUsers.forEach((user) => {
      if (!userChecked[user.loginId]) {
        attendance.push(user);
      }
    });

    this.state = {
      attendance,
      date: params.date,
      showDatePicker: false
    };

    this.props.navigation.setParams({ selectDate: this.selectDateAsync, saveRecord: this.saveRecordAsync });
  }

  selectDateAsync = async () => {
    this.setState({ showDatePicker: !this.state.showDatePicker });
  };

  deleteRecordAsync = async (date) => {
    const dateString = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    const result = await globalThis.dsObject.deleteAttendance(this.groupId, dateString);
    return result ? true : false;
  };

  saveInternalAsync = async (date, oldDate, users, allUsers) => {
    if (oldDate && date !== oldDate) {
      await this.deleteRecordAsync(oldDate);
    }

    const dateString = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    const result = await globalThis.dsObject.setAttendance(this.groupId, dateString, {
      users,
      allUsers
    });
    if (!result) {
      return;
    }

    this.setLoadNeeded();
    this.props.navigation.pop();
  };

  saveRecordAsync = async () => {
    const userIds = [];
    const { attendance, date } = this.state;
    attendance.forEach((user) => {
      if (user.checked) {
        userIds.push(user.userId);
      }
    });

    const allUserIds = this.allUsers.map((user) => user.userId);
    const key = new Date(date).toDateString();
    if (this.oldDate !== date && this.existingDate[key]) {
      const title = `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;
      Alert.alert(i18n2.t('Common.Warning'), title + i18n2.t('AttendanceRecordScreen.AttendanceRecordExist'), [
        {
          text: i18n2.t('Common.Yes'),
          onPress: () => {
            this.saveInternalAsync(date, this.oldDate, userIds, allUserIds);
          }
        },
        {
          text: i18n2.t('Common.No')
        }
      ]);
      return;
    }

    await this.saveInternalAsync(date, this.oldDate, userIds, allUserIds);
  };

  getTitle(index, user) {
    return `#${index}: ${user.name.trim()} (${user.loginId})`;
  }

  onCheck(user) {
    const attendance = this.state.attendance;
    for (let i in attendance) {
      if (attendance[i].userId === user.userId) {
        attendance[i].checked = !user.checked;
        break;
      }
    }

    this.setState({ attendance });
  }

  render() {
    const { attendance } = this.state;
    if (!Array.isArray(attendance)) {
      return <LoadingIndicator />;
    }

    let keyIndex = 0;
    let index = 0;
    const windowWidth = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right;
    return (
      <View style={{ flex: 1 }}>
        <ScrollView
          scrollIndicatorInsets={{ right: 1 }}
          style={{
            backgroundColor: 'white',
            marginLeft: this.context.insets.left,
            marginRight: this.context.insets.right
          }}>
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              marginTop: 5,
              marginBottom: 5
            }}>
            {this.state.attendance.map((user) => (
              <CheckBox
                containerStyle={{ width: windowWidth - 20 }}
                textStyle={{ fontSize: getCurrentUser().getSmallFontSize() }}
                checkedColor={Colors.darkBlue}
                key={keyIndex++}
                title={this.getTitle(++index, user)}
                checked={user.checked}
                onPress={() => {
                  this.onCheck(user);
                }}
              />
            ))}
          </View>
          <View style={{ height: this.context.insets.bottom }} />
        </ScrollView>
        <DateTimePickerModal
          isVisible={this.state.showDatePicker}
          mode='date'
          date={this.state.date}
          onConfirm={(date) => {
            this.setState({ date, showDatePicker: false });
            this.props.navigation.setParams({ date });
          }}
          onCancel={() => {
            this.setState({ showDatePicker: false });
          }}
          headerTextIOS={i18n2.t('AttendanceRecordScreen.PickDate')}
          confirmTextIOS={i18n2.t('Common.Confirm')}
          cancelTextIOS={i18n2.t('Common.Cancel')}
        />
      </View>
    );
  }
}
