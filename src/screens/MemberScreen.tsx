import {
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  useWindowDimensions
} from 'react-native';

import { Avatar2 } from '@/components/Avatar2';
import { Button } from '@/components';
import { Colors } from '@/styles/colors';
import { Overlay } from '@rneui/themed';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { useMemberViewModel } from '@/viewModels/MemberViewModel';
import { useNavigation } from '@react-navigation/native';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const MemberScreen = ({ route }: any) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const navigation = useNavigation<any>();

  const { id, uniqueId, name, email, userTag: initialUserTag, isCurrentUserBlocking } = route.params;
  const {
    showFullScreenImage,
    toggleFullScreenImage,
    toggleUserBlock,
    initiateChat,
    showChatButton,
    copyEmailToClipboard,
    insets,
    setTag,
    initialTag,
    userTag,
    handleUserTagChange,
    isCurrentUserBlockingViewedUser
  } = useMemberViewModel(id, route.params.hideChat, initialUserTag, isCurrentUserBlocking);

  const { width } = useWindowDimensions();
  const contentWidth = width - insets.left - insets.right;
  const fontSize = getCurrentUser().getMediumFontSize();
  const lineHeight = 1.3 * fontSize;
  const inputWidth = fontSize * 7 + 20; // userTag input should display 7 words

  if (showFullScreenImage) {
    return (
      <Overlay isVisible={true} onBackdropPress={toggleFullScreenImage} fullScreen={true}>
        <TouchableOpacity style={styles.fullScreenOverlay} onPress={toggleFullScreenImage}>
          <Avatar2 userId={id} fullSize={true} size={contentWidth} onPress={toggleFullScreenImage} />
        </TouchableOpacity>
      </Overlay>
    );
  }

  const share = () => {
    navigation.navigate('SelectGroupOrUser', {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      onSelect: async (choice: { groupId?: any; uniqueId?: any }) => {
        let groupId = choice.groupId;
        if (choice.uniqueId) {
          //need to create OneOnOne group, and then sendMessage
          const result = await globalThis.dsObject.createOneOnOne(choice.uniqueId);
          if (!result) {
            return;
          }
          groupId = result.body.studyGroupId;
        }
        if (groupId) {
          await globalThis.dsObject.sendMessage({
            room: groupId,
            message: `!contact!${email}!${name}`
          });
        }
      }
    });
  };

  const leftStyle = [styles.left, { fontSize, lineHeight }];
  const rightStyle = [styles.right, { fontSize, lineHeight }];
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={{ margin: 10 }}>
        <Avatar2 userId={id} size={150} onPress={toggleFullScreenImage} />
      </View>
      <View style={styles.contentContainer}>
        {email && (
          <View style={styles.row}>
            <Text style={leftStyle}>{i18n2.t('MemberScreen.ID')}:</Text>
            <TouchableOpacity
              style={styles.right}
              onPress={
                !isCurrentUserBlockingViewedUser || isCurrentUserBlocking
                  ? () => copyEmailToClipboard(email)
                  : undefined
              }
              disabled={isCurrentUserBlockingViewedUser || isCurrentUserBlocking}>
              <Text style={{ fontSize }}>{isCurrentUserBlockingViewedUser || isCurrentUserBlocking ? '' : email}</Text>
            </TouchableOpacity>
          </View>
        )}
        <View style={styles.row}>
          <Text style={leftStyle}>{i18n2.t('DisplayName')}:</Text>
          <Text style={rightStyle}>{name}</Text>
        </View>
        <View style={styles.row}>
          <Text style={leftStyle}>{i18n2.t('MemberScreen.UserTag')}:</Text>
          <TextInput
            style={[
              rightStyle,
              {
                width: inputWidth,
                borderColor: '#888888',
                borderWidth: 0.5,
                backgroundColor: '#DDDDDD',
                color: Colors.darkBlue,
                fontSize,
                paddingHorizontal: 10
              }
            ]}
            maxLength={12}
            value={userTag}
            placeholder={userTag ? userTag : i18n2.t('MemberScreen.NoTag')}
            placeholderTextColor='white'
            onBlur={() => {
              if (userTag !== initialTag) {
                setTag(userTag);
              }
            }}
            onChangeText={handleUserTagChange}
          />
        </View>
        {
          // Originally, the plan was to handle the blocking status in the same way as the usertag,
          // obtaining the initial states of isUserBlocked and isCurrentUserBlocking from the parent
          // component and set them as the initial state of the block switch, and then updating the UI feedback
          // through the ViewModel. However, this approach requires adding state management in the screen,
          // which violates the MVVM pattern; meanwhile moving state management to the ViewModel will
          // cause real block data not be displayed in time during initial loading
        }
        <View style={styles.row}>
          <Text style={leftStyle}>{i18n2.t('MemberScreen.BlockUser')}:</Text>
          <View style={styles.right}>
            <Switch
              style={{ alignSelf: 'flex-start', height: 32 }}
              value={isCurrentUserBlockingViewedUser}
              onValueChange={toggleUserBlock}
            />
          </View>
        </View>
        <View
          style={{
            width: '70%'
          }}>
          <Text
            style={{
              width: '50%',
              paddingVertical: 10,
              fontSize: getCurrentUser().getXSmallFontSize()
            }}>
            {i18n2.t('MemberScreen.BlockUserText')}
          </Text>
        </View>
      </View>
      {showChatButton && (
        <Button
          title={i18n2.t('Common.Chat')}
          onPress={() => initiateChat(uniqueId)}
          style={
            isCurrentUserBlockingViewedUser || isCurrentUserBlocking ? styles.chatButtonDisabled : styles.chatButton
          }
          disabled={isCurrentUserBlockingViewedUser || isCurrentUserBlocking}
        />
      )}
      <Button
        title={i18n2.t('Common.Share')}
        onPress={share}
        style={isCurrentUserBlockingViewedUser || isCurrentUserBlocking ? styles.chatButtonDisabled : styles.chatButton}
        disabled={isCurrentUserBlockingViewedUser || isCurrentUserBlocking}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center'
  },
  contentContainer: {
    width: '100%',
    alignItems: 'center'
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginHorizontal: 10,
    marginTop: 10
  },
  left: {
    width: '50%',
    textAlign: 'right',
    right: 5
  },
  right: {
    width: '50%',
    left: 5
  },
  fullScreenOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  chatButton: {
    marginTop: 20
  },
  chatButtonDisabled: {
    marginTop: 20,
    backgroundColor: 'white'
  }
});

export default MemberScreen;
