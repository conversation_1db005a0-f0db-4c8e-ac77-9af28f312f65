import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import SettingsList from '@/components/SettingsList';
import { AppContext } from '@/context/AppContext';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { Colors } from '@/styles/colors';

export default class LoginInfoSettingScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = () => ({
    title: i18n2.t('SettingsScreen.LoginInfo')
  });

  constructor(props) {
    super(props);
    this.state = {
      loginId: getCurrentUser().getLoginId(),
      showRealContent: false
    };
  }

  renderPlaceholderList(fontSize) {
    return (
      <SettingsList isFishMeet={true} underlayColor='#FFFFFF' borderColor='#424350'>
        <SettingsList.Item title='item 1' titleStyle={{ fontSize }} hasNavArrow={false} />
        <SettingsList.Item title='item 2' titleStyle={{ fontSize }} hasNavArrow={false} />
        <SettingsList.Item title='item 3' titleStyle={{ fontSize }} hasNavArrow={false} />
        <SettingsList.Item title='item 4' titleStyle={{ fontSize }} hasNavArrow={false} />
        <SettingsList.Item title='item 5' titleStyle={{ fontSize }} hasNavArrow={false} />
      </SettingsList>
    );
  }

  renderRealContent(fontSize) {
    const { thirdPartyLogin } = getCurrentUser();
    return (
      <View style={styles.realContainer}>
        <SettingsList underlayColor='#FFFFFF' borderColor='#DCDCDC'>
          <SettingsList.Item
            title={i18n2.t('SettingsScreen.MyEmail')}
            titleStyle={{ fontSize }}
            titleInfo={this.state.loginId}
            titleInfoStyle={[styles.infoStyle, { fontSize }]}
            hasNavArrow={false}
          />
          {!thirdPartyLogin && (
            <SettingsList.Item
              title={i18n2.t('Common.Pwd')}
              titleStyle={{ fontSize }}
              rightSideContent={<Text style={{ fontSize, color: Colors.darkBlue }}>********</Text>}
              onPress={() => {
                this.props.navigation.navigate('UpdatePassword');
              }}
            />
          )}
        </SettingsList>
      </View>
    );
  }

  render() {
    const fontSize = getCurrentUser().getSmallFontSize();
    if (this.state.showRealContent) {
      return this.renderRealContent(fontSize);
    } else {
      return (
        <View style={styles.container}>
          {/* First part*/}
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.button} onPress={() => this.setState({ showRealContent: true })}>
              <Text style={styles.buttonText}>Email and Password</Text>
            </TouchableOpacity>
          </View>
          {/* Second part */}
          <View style={styles.groupContainer}>{this.renderPlaceholderList(fontSize)}</View>
        </View>
      );
    }
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 28,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF'
  },
  realContainer: {
    flex: 1,
    paddingHorizontal: 28,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF'
  },
  infoStyle: {
    color: Colors.darkBlue
  },
  buttonContainer: {
    marginBottom: 20,
    alignItems: 'center'
  },
  button: {
    backgroundColor: '#F0F0F0',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8
  },
  buttonText: {
    fontSize: 16,
    color: Colors.darkBlue,
    fontWeight: '600'
  },
  groupContainer: {
    width: 334,
    borderRadius: 20,
    borderWidth: 1,
    backgroundColor: '#F9FDFE',
    borderColor: '#42435080',
    marginTop: 20,
    padding: 10
  }
});
