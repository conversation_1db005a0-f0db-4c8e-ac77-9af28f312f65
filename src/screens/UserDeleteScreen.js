import { Text, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { Button } from '@/components';
import { CheckBox } from '@rneui/themed';
import { Colors } from '@/styles/colors';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';

export default class UserDeleteScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = () => ({
    title: i18n2.t('UserScreen.DeleteAcct')
  });

  constructor(props) {
    super(props);

    this.state = {
      confirmed: false
    };
  }

  deleteUserAsync = async () => {
    const result = await globalThis.dsObject.deleteUser();
    if (!result) {
      return;
    }

    getCurrentUser().logoutAsync();
  };

  render() {
    const fontSize = getCurrentUser().getLargeFontSize();
    const { confirmed } = this.state;
    return (
      <View style={{ flex: 1, marginHorizontal: 30 }}>
        <Text style={{ marginTop: 30, color: 'red', fontSize }}>{i18n2.t('UserScreen.DeleteAcctMsg')}</Text>

        <View style={{ alignItems: 'center', marginVertical: 30 }}>
          <Text style={{ color: Colors.darkBlue, fontSize, fontWeight: 'bold' }}>{getCurrentUser().getLoginId()}</Text>
        </View>

        <CheckBox
          containerStyle={{ backgroundColor: 'transparent' }}
          textStyle={{ fontSize }}
          checkedColor={Colors.darkBlue}
          title={i18n2.t('UserScreen.IConfirm')}
          checked={confirmed}
          onPress={() => {
            this.setState({ confirmed: !confirmed });
          }}
        />

        <View style={{ alignItems: 'center', marginTop: 20 }}>
          <Button
            title={i18n2.t('Delete')}
            disabled={!confirmed}
            onPress={() => {
              this.deleteUserAsync();
            }}
          />
        </View>
      </View>
    );
  }
}
