import {
  getPushTokenAsync,
  registerForPushNotificationsAsync,
  unregisterForPushNotificationsAsync
} from '@/utils/notification';

import React from 'react';
import SettingsList from '@/components/SettingsList';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';

export default class DebugScreen extends React.Component {
  static navigationOptions = () => ({
    title: i18n2.t('DebugScreen.Title')
  });

  sendPushNotification = async () => {
    try {
      const token = await getPushTokenAsync();
      if (!token) {
        return;
      }

      const message = {
        to: token,
        sound: 'default',
        title: 'New message from iDigest🤖️',
        body: 'This is a test message 😄👌',
        data: { data: 'goes here' },
        _displayInForeground: true
      };
      await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(message)
      });
    } catch (error) {
      alert(error);
    }
  };

  render() {
    const fontSize = getCurrentUser().getSmallFontSize();
    return (
      <SettingsList underlayColor='#FFFFFF' borderColor='#DCDCDC'>
        <SettingsList.Item
          title={'Throw unhandled exception'}
          titleStyle={{ fontSize }}
          hasNavArrow={false}
          onPress={() => {
            alert(this.abc.abc);
          }}
        />
        <SettingsList.Item
          title={'Register push notification'}
          titleStyle={{ fontSize }}
          hasNavArrow={false}
          onPress={async () => {
            const token = await getPushTokenAsync();
            if (await registerForPushNotificationsAsync(token)) {
              alert('Succeeded!');
            }
          }}
        />
        <SettingsList.Item
          title={'Unregister push notification'}
          titleStyle={{ fontSize }}
          hasNavArrow={false}
          onPress={async () => {
            const token = await getPushTokenAsync();
            if (await unregisterForPushNotificationsAsync(token)) {
              alert('Succeeded!');
            }
          }}
        />
        <SettingsList.Item
          title={'Send push notification'}
          titleStyle={{ fontSize }}
          hasNavArrow={false}
          onPress={() => {
            this.sendPushNotification();
          }}
        />
      </SettingsList>
    );
  }
}
