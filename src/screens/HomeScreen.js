import * as Device from 'expo-device';

import { Dimensions, FlatList, Keyboard, Platform, RefreshControl, Text, TouchableOpacity, View } from 'react-native';

import { AntDesign } from '@expo/vector-icons';
import { AppContext } from '@/context/AppContext';
import { Button } from '@/components';
import { ClassCover } from '@/components/ClassCover';
import { Colors } from '@/styles/colors';
import { CommonActions } from '@react-navigation/native';
import { Image } from 'expo-image';
import { NavBarButton } from '@/components/navigation/NavigationButtons';
import { NavChip } from '@/components/navigation/NavChip';
import React from 'react';
import SearchBar from '@/components/SearchBar';
import { checkIsDownload } from '@/utils/lesson';
import { cloneDeep } from 'lodash-es';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { logEvent } from '@/utils/logger';

export default class HomeScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = () => ({ headerShown: false });

  constructor(props) {
    super(props);

    this.listeners = [];
    this.canShowReload = false;
    this.userId = getCurrentUser().getUserId();
    this.cursor = -1;
    this.scrollView = React.createRef();

    this.state = {
      page: 0,
      refreshing: false,
      searchText: '',
      isSearching: false
    };
  }

  async componentDidMount() {
    this.listeners.push(globalThis.eventEmitter.addListener('homeTabClicked', this.onHomeTabClicked));

    this.listeners.push(
      globalThis.eventEmitter.addListener('firstLaunchReady', () => {
        this.canShowReload = true;
      })
    );

    // this.props.navigation.addListener('blur', async () => {
    //   await ScreenOrientation.unlockAsync();
    // });
  }

  onHomeTabClicked = () => {
    const { page } = this.state;
    this.goToPage((page + 1) % 3);
  };

  goToPage = (page) => {
    this.setState({ page });
    this.scrollView.scrollToOffset({ y: 0, x: 0, animated: true });
    if (page === 1) {
      this.context.setUserLastSeenClassAsync();
    }
  };

  componentWillUnmount() {
    this.listeners.forEach((listener) => {
      listener.remove();
    });
  }

  loadLessonsAsync = async () => {
    await this.context.checkUserUpdateAsync();
  };

  goToLesson = async (item) => {
    getCurrentUser().setPropertyAsync('classOrder', { [item.name]: Date.now() });
    let downloadState;
    if (item.allowDownload) {
      downloadState = (await checkIsDownload(item.classId)) ? 2 : 1;
    }
    logEvent('goToLesson', { id: item.classId });
    this.props.navigation.navigate('LessonTableOfContent', {
      lesson: item,
      downloadState
    });
  };

  handleSearchTextChange = (text) => {
    this.setState({ searchText: text });
  };

  handleSearchTextFocus = () => {
    this.setState({ isSearching: true });
  };

  handleSearchTextClose = () => {
    Keyboard.dismiss();
    this.setState({ isSearching: false, searchText: '' });
  };

  // mode: 0 - popularity, 1 - latest first, 2 - my popularity
  renderLessons = (mode) => {
    const classes = cloneDeep(this.context.classes);
    const { refreshing, searchText, isSearching } = this.state;

    if (classes.length === 0) {
      //Classes not loaded yet, don't cause the Flatlist
      //to have a fixed column, since numColumn can't be changed
      return;
    }

    // sort classes
    let sortedClasses = [];

    if (searchText?.trim()) {
      sortedClasses = classes.filter((item) => {
        return item.classNameChs.toLowerCase().includes(searchText.toLowerCase());
      });
    } else {
      switch (mode) {
        case 0: {
          // sort by popularity
          sortedClasses = classes;
          break;
        }
        case 1:
          // sort by latest first (only show top 12)
          sortedClasses = classes.sort((a, b) => b.lastModified - a.lastModified).slice(0, 12);
          break;
        case 2: {
          // sort by my-popularity
          const classAccessTime = getCurrentUser().getProperty('classOrder') || {};
          sortedClasses = classes
            .filter((it) => it.isMyClass)
            .sort((a, b) => (classAccessTime[b.name] || 0) - (classAccessTime[a.name] || 0));
          break;
        }
        default:
          break;
      }
    }

    // Phone/Default - 2 columns, Tablet - 4 columns, TV/DESKTOP - 6 columns
    let columns = 2;
    switch (Device.deviceType) {
      case Device.DeviceType.TV:
      case Device.DeviceType.DESKTOP:
        columns = 6;
        break;
      case Device.DeviceType.TABLET:
        columns = 4;
        break;
    }
    const screenWidth = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right;
    const bookMargin = 10;
    const bookWidth = (screenWidth - bookMargin * (columns + 1)) / columns;

    return (
      <View
        style={{
          flex: 1,
          paddingLeft: this.context.insets.left,
          paddingRight: this.context.insets.right
        }}>
        {Platform.OS === 'android' && Device.platformApiLevel <= 23 ? (
          <Text
            style={{
              textAlign: 'center',
              fontSize: getCurrentUser().getMediumFontSize(),
              color: 'red',
              margin: 7
            }}>
            {i18n2.t('Common.AndroidVerTooLow')}
          </Text>
        ) : null}
        {Array.isArray(sortedClasses) ? (
          <FlatList
            ref={(ref) => (this.scrollView = ref)}
            numColumns={columns}
            ListHeaderComponent={
              <View
                style={{
                  paddingTop: isSearching ? this.context.insets.top + 20 : this.context.insets.top + 85 + 40,
                  paddingBottom: 10
                }}>
                <SearchBar
                  autoFocus={false}
                  value={searchText}
                  isCloseVisible={isSearching}
                  onChangeText={this.handleSearchTextChange}
                  onFocus={this.handleSearchTextFocus}
                  onClose={this.handleSearchTextClose}
                />
              </View>
            }
            estimatedItemSize={bookWidth}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                size='large'
                title='Pull to refresh'
                titleColor='#000'
                tintColor='#000'
                onRefresh={() => {
                  this.loadLessonsAsync();
                }}
              />
            }
            scrollEventThrottle={16}
            data={sortedClasses}
            renderItem={({ item }) => {
              return (
                <ClassCover
                  key={item.classId}
                  size={bookWidth}
                  margin={bookMargin}
                  classId={item.classId}
                  text={item.classNameChs}
                  unreadCount={item.newMessageCount}
                  onPress={() => this.goToLesson(item)}
                />
              );
            }}
          />
        ) : null}
        {this.canShowReload && (!Array.isArray(sortedClasses) || sortedClasses.length === 0) && !isSearching ? (
          <View style={{ flex: 1, alignItems: 'center' }}>
            <Button
              title={i18n2.t('Common.Reload')}
              onPress={() => {
                this.loadLessonsAsync();
              }}
            />
          </View>
        ) : null}
      </View>
    );
  };

  renderResume = () => {
    const { currentProgress } = this.context;
    const bookTitle = currentProgress?.LessonScreen?.bookTitle;
    const canResumeLesson = !!bookTitle;
    if (!canResumeLesson) {
      return <></>;
    }

    const screenWidth = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right;
    const buttonWidth = screenWidth / 3;
    return (
      <View
        style={{
          position: 'absolute',
          width: Dimensions.get('window').width,
          alignItems: 'center',
          justifyContent: 'center',
          bottom: 9
        }}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            this.resumeLesson(currentProgress.LessonScreen);
          }}>
          <View
            style={{
              width: buttonWidth,
              borderRadius: 20,
              borderColor: Colors.buttonBorder,
              borderWidth: 1,
              height: 40,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: Colors.lightBlue,
              flexDirection: 'row'
            }}>
            <View style={{ width: buttonWidth - 38, alignItems: 'center' }}>
              <Text
                style={{
                  fontSize: getCurrentUser().getXSmallFontSize()
                }}
                numberOfLines={1}>
                {bookTitle}
              </Text>
            </View>
            <AntDesign name={'playcircleo'} size={22} color={'black'} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  resumeLesson = (lessonScreenParams) => {
    const lesson = lessonScreenParams.lesson;
    if (!lesson) {
      alert('Error lesson');
      return;
    }

    // replace navigation stack
    this.props.navigation.dispatch(
      CommonActions.reset({
        index: 2,
        routes: [
          { name: 'BottomTab' },
          {
            name: 'LessonTableOfContent',
            params: { lesson }
          },
          {
            name: 'Lesson',
            params: lessonScreenParams
          }
        ]
      })
    );
  };

  render() {
    const { page, isSearching } = this.state;
    const screenWidth = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right;
    const { hasNewClass } = this.context;
    return (
      <View style={{ flex: 1, paddingLeft: this.context.insets.left, paddingRight: this.context.insets.right }}>
        {this.renderLessons(page)}
        <View
          style={{
            position: 'absolute',
            top: 0,
            display: isSearching ? 'none' : 'flex',
            width: screenWidth,
            overflow: 'hidden',
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: this.context.insets.top + 7
          }}>
          <Image
            style={{ height: 56, width: '40%' }}
            contentFit={'contain'}
            source={require('@/assets/images/splash.png')}
          />
          <View
            style={{
              position: 'absolute',
              height: 56,
              right: 0,
              top: 0,
              justifyContent: 'center'
            }}>
            <NavBarButton
              backgroundColor='transparent'
              image='helpme'
              onPress={() => {
                this.props.navigation.navigate('HelpMe');
              }}
            />
          </View>
        </View>
        <View
          style={{
            flexDirection: 'row',
            position: 'absolute',
            display: isSearching ? 'none' : 'flex',
            width: screenWidth,
            top: 60,
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: this.context.insets.top + 12,
            zIndex: 0
          }}>
          <NavChip
            page={page}
            pageIndex={0}
            title={i18n2.t('DiscoverUI.Popular')}
            width={screenWidth / 3 - 20}
            onPress={() => {
              this.goToPage(0);
            }}
          />
          <View style={{ width: 7 }} />
          <NavChip
            page={page}
            pageIndex={1}
            title={i18n2.t('DiscoverUI.Latest')}
            width={screenWidth / 3 - 20}
            onPress={() => {
              this.goToPage(1);
            }}
            showRedDot={hasNewClass}
          />
          <View style={{ width: 7 }} />
          <NavChip
            page={page}
            pageIndex={2}
            title={i18n2.t('DiscoverUI.MyStudy')}
            width={screenWidth / 3 - 20}
            onPress={() => {
              this.goToPage(2);
            }}
          />
        </View>
        {this.renderResume()}
      </View>
    );
  }
}
