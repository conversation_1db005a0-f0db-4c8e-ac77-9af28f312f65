import { <PERSON><PERSON>, KeyboardView } from '@/components';
import { Dimensions, StyleSheet, Text, View } from 'react-native';

import { Colors } from '@/styles/colors';
import { ExplanationHeader } from '@/fishMeet/components/ExplanationHeader';
import { FmStyles } from '@/fishMeet/styles/fmStyles';
import { Input } from '@rneui/themed';
import { InputField } from '@/fishMeet/components/InputField';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { getInputContainerStyle } from '@/styles/styles';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';

export default class ForgotPasswordScreen extends React.Component {
  static navigationOptions = () => ({
    title: i18n2.t('RestorePwd')
  });

  constructor(props) {
    super(props);
    this.state = {
      email: ''
    };

    this.emailInputRef = React.createRef(null);
  }

  componentDidMount() {
    this.emailInputRef.current.focus();
  }

  async forgetPassword() {
    if (!this.state.email || this.state.email.length < 6) {
      this.emailInputRef.current.shake();
      this.emailInputRef.current.focus();
      return;
    }

    try {
      this.setState({ busy: true });
      const body = {
        loginId: this.state.email
      };
      const result = await globalThis.dsObject.resetPassword(body);
      if (!result) {
        return;
      }

      this.props.navigation.navigate('PasswordDone');
    } finally {
      this.setState({ busy: false });
    }
  }

  render() {
    const fontSize = getCurrentUser().getMediumFontSize();

    return isFishMeet ? this.fmRender() : this.iDigestRender(fontSize);
  }

  iDigestRender = (fontSize) => (
    <KeyboardView>
      <View style={styles.container}>
        <Text style={{ marginTop: 10, color: Colors.darkBlue, fontSize }}>{i18n2.t('Email')}</Text>

        <Input
          inputContainerStyle={getInputContainerStyle(Dimensions.get('window').width).inputContainerStyle}
          errorStyle={{ height: 0 }}
          placeholder={i18n2.t('EmailExample')}
          underlineColorAndroid='transparent'
          defaultValue={this.state.email}
          ref={this.emailInputRef}
          autoCorrect={false}
          autoCapitalize='none'
          returnKeyType={'next'}
          submitBehavior='submit'
          onChangeText={(text) => {
            this.setState({ email: text });
          }}
          onSubmitEditing={() => {
            this.forgetPassword();
          }}
        />

        <View
          style={{
            margin: 20,
            alignItems: 'center'
          }}>
          <Button
            title={i18n2.t('Submit')}
            onPress={() => {
              this.forgetPassword();
            }}
          />
        </View>
      </View>
    </KeyboardView>
  );

  fmRender = () => (
    <KeyboardView>
      <View style={FmStyles.screenContainer}>
        <ExplanationHeader
          titleText={i18n2.t('ForgotPasswordScreen.HeaderTitle')}
          explanationText={i18n2.t('ForgotPasswordScreen.HeaderExplanation')}
        />

        <InputField
          inputType='email'
          ref={this.emailInputRef}
          defaultValue={this.state.email}
          label={i18n2.t('Email')}
          autoFocus={true}
          returnKeyType='go'
          onChangeText={(text) => {
            this.setState({ email: text });
          }}
          onSubmitEditing={() => {
            this.forgetPassword();
          }}
        />

        <View style={FmStyles.buttonContainer}>
          <Button
            title={i18n2.t('Submit')}
            onPress={() => {
              this.forgetPassword();
            }}
          />
        </View>
      </View>
    </KeyboardView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff'
  }
});
