import { OkButton } from '@/components/navigation/NavigationButtons';
import React from 'react';
import Select from '@/components/Select/Select';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function SelectScreen({ route, navigation }: any) {
  useSetNavigationOptions({
    title: route.params.title,
    headerRight: () =>
      route.params.multiple ? (
        <OkButton
          onPress={() => {
            route.params.onConfirm();
          }}
        />
      ) : null,
    gestureEnabled: true
  });

  const params = route.params;

  return (
    <Select
      {...params}
      onSelect={(item) => {
        params.onSelect(item);
        if (!params.skipNavigateBack && !params.multiple) {
          navigation.pop();
        }
      }}
    />
  );
}

export default SelectScreen;
