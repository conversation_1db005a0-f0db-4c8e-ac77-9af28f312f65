import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useState } from 'react';

import { IProgram } from '@/utils/channel/IChannel';

interface ActionBarProps {
  program: IProgram;
  onLike?: (liked: boolean) => void;
  onCollect?: (collected: boolean) => void;
}

const ActionBar: React.FC<ActionBarProps> = ({ program, onLike, onCollect }) => {
  const [liked, setLiked] = useState(program.isLiked ?? false);
  const [collected, setCollected] = useState(program.isCollected ?? false);
  const [likeCount, setLikeCount] = useState(program.likeCount || 0);
  const [collectCount, setCollectCount] = useState(program.collect || 0);

  const toggleLike = async () => {
    const newLiked = !liked;
    const res = newLiked
      ? await global.dsObject.postProgramLike(program.programId)
      : await global.dsObject.postProgramUnlike(program.programId);
    if (res) {
      setLiked(newLiked);
      setLikeCount((prev) => (newLiked ? prev + 1 : prev - 1));
      onLike?.(newLiked);
    }
  };

  const toggleCollect = async () => {
    const newCollected = !collected;
    const res = newCollected
      ? await global.dsObject.postProgramCollect(program.programId)
      : await global.dsObject.postProgramUncollect(program.programId);
    if (res) {
      setCollected(newCollected);
      setCollectCount((prev) => (newCollected ? prev + 1 : prev - 1));
      onCollect?.(newCollected);
    }
  };

  return (
    <>
      <View style={styles.actionBar}>
        <TouchableOpacity style={styles.actionItem} onPress={toggleLike}>
          <Image
            source={
              liked
                ? require('@/assets/images/channel/icon-Like2.png')
                : require('@/assets/images/channel/icon-Like.png')
            }
            style={styles.icon}
          />
          <Text style={styles.actionText}>{likeCount}</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionItem} onPress={toggleCollect}>
          <Image
            source={
              collected
                ? require('@/assets/images/channel/icon-MyCollections2.png')
                : require('@/assets/images/channel/icon-MyCollections.png')
            }
            style={styles.icon}
          />
          <Text style={styles.actionText}>{collectCount}</Text>
        </TouchableOpacity>
        <View style={styles.actionItem}>
          <Image source={require('@/assets/images/channel/icon_view.png')} style={styles.icon} />
          <Text style={styles.actionText}>{(program.views || 0) + 1}</Text>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderColor: '#eee',
    backgroundColor: '#fff'
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  actionText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#333'
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain'
  }
});

export default ActionBar;
