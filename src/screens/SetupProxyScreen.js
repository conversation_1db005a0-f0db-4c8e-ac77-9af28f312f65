import { Button, KeyboardView } from '@/components';
import { Dimensions, Text, View } from 'react-native';

import { Colors } from '@/styles/colors';
import { Config } from '@/dataStorage/config';
import { Input } from '@rneui/themed';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { getInputContainerStyle } from '@/styles/styles';
import { i18n2 } from '@/utils/i18n2';

export default class SetupProxyScreen extends React.Component {
  static navigationOptions = () => ({
    title: i18n2.t('App.SetupProxy')
  });

  constructor(props) {
    super(props);

    this.state = {
      httpsServer: getCurrentUser().getHttpsServer2()
    };
  }

  async updateHttpsServer() {
    const httpsServer = (this.state.httpsServer || '').toLowerCase();
    if (!httpsServer || !httpsServer.startsWith('https://')) {
      this.input.shake();
      this.input.focus();
      return;
    }

    // check if we have access
    const result = await globalThis.dsObject.checkHttpsServer(httpsServer);
    if (!result) {
      this.input.shake();
      this.input.focus();
      return;
    }

    // set up server
    getCurrentUser().setUpHttpsServer2(httpsServer);
    this.props.navigation.pop();
  }

  render() {
    const httpsServer = Config.DefaultHttpsServer2;
    return (
      <KeyboardView>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center'
          }}>
          <View style={{ marginTop: 20 }} />

          <Text
            style={{
              marginTop: 10,
              color: Colors.darkBlue,
              fontSize: getCurrentUser().getSmallFontSize()
            }}>
            {i18n2.t('ProxyURL')}
          </Text>
          <Input
            inputContainerStyle={getInputContainerStyle(Dimensions.get('window').width).inputContainerStyle}
            contentContainerStyle={{ alignItems: 'center' }}
            placeholder=''
            ref={(input) => (this.input = input)}
            defaultValue={this.state.httpsServer}
            errorStyle={{ color: 'red' }}
            onChangeText={(text) => {
              this.setState({ httpsServer: text });
            }}
            onSubmitEditing={() => this.updateHttpsServer()}
          />
          <View style={{ marginTop: 20 }}>
            <Button title={i18n2.t('Submit')} onPress={() => this.updateHttpsServer()} />
            <Button
              title={i18n2.t('Restore')}
              disabled={this.state.httpsServer === httpsServer}
              onPress={() => {
                this.setState({ httpsServer });
                getCurrentUser().setUpHttpsServer2(httpsServer);
                this.props.navigation.pop();
              }}
            />
          </View>
        </View>
      </KeyboardView>
    );
  }
}
