import { ActivityIndicator, FlatList, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { IChannel, IProgram } from '@/utils/channel/IChannel';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigation, useRoute } from '@react-navigation/native';

import { Button } from '@/components';
import { Colors } from '@/styles/colors';
import { Image as ExpoImage } from 'expo-image';
import MoreContent from '@/components/MoreContent';
import { getChannelImageUrl } from '@/utils/url';
import { getFontSize } from '@/utils/getFontSize';
import { i18n2 } from '@/utils/i18n2';
import moment from 'moment';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';

/* eslint-disable @typescript-eslint/no-explicit-any */

const ChannelProgramScreen = () => {
  const { params } = useRoute();
  const { channelId } = params as any;
  const { push } = useNavigation<any>();

  const [detail, setDetail] = useState<IChannel>();
  const [programs, setPrograms] = useState<IProgram[]>([]);
  const [nextToken, setNextToken] = useState<string | null>(null);
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [subscribed, setSubscribed] = useState<boolean>(false);
  const [initialLoading, setInitialLoading] = useState(true);

  const loadingRef = useRef(false);

  const getPreviewUrl = (v: string, programId?: number) => {
    return getChannelImageUrl(channelId, v, programId);
  };

  const loadPrograms = useCallback(
    async (params: {
      channelId: number;
      selectedTag: string | null;
      nextToken: string | null;
      isLoadMore: boolean;
    }) => {
      if (loadingRef.current) {
        return;
      }
      loadingRef.current = true;

      const resp = await global.dsObject.getPrograms(
        params.channelId,
        params.selectedTag || undefined,
        params.isLoadMore ? params.nextToken : null
      );
      if (resp?.body) {
        setPrograms((prev) => (params.isLoadMore ? [...prev, ...resp.body.programs] : resp.body.programs));
        setNextToken(resp.body.nextToken);
      }

      loadingRef.current = false;
      if (!params.isLoadMore) {
        setInitialLoading(false);
      }
    },
    []
  );

  const initData = useCallback(async (channelId: number) => {
    setInitialLoading(true);
    const respChannel = await global.dsObject.getChannel(channelId);
    if (respChannel?.body) {
      setDetail(respChannel.body);
      setSubscribed(respChannel.body.isSubscribed ?? false);
    }
  }, []);

  const handleSubscribe = async () => {
    if (subscribed) {
      await global.dsObject.putChannelUnsubscribe(channelId);
      setSubscribed(false);
    } else {
      await global.dsObject.putChannelSubscribe(channelId);
      setSubscribed(true);
    }
  };

  useEffect(() => {
    initData(channelId);
    loadPrograms({
      channelId,
      selectedTag: null,
      nextToken: null,
      isLoadMore: false
    });
  }, [initData, loadPrograms, channelId]);

  useSetNavigationOptions({
    title: `${i18n2.t('ChannelDetailScreen.ChannelPrefix')}${detail?.name || ''}`
  });

  if (!detail?.name || initialLoading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator size='large' color={Colors.loadingColor} />
      </View>
    );
  }

  return (
    <FlatList
      data={programs}
      keyExtractor={(item) => item.programId.toString()}
      onEndReachedThreshold={0.4}
      onEndReached={() => {
        if (nextToken) {
          loadPrograms({
            channelId,
            selectedTag,
            nextToken,
            isLoadMore: true
          });
        }
      }}
      ListHeaderComponent={
        <>
          <View style={styles.channel}>
            <ExpoImage style={styles.cover} source={{ uri: getPreviewUrl('cover.jpg') }} />
            {detail?.description && <MoreContent textStyle={styles.description} description={detail.description} />}
            <Button
              style={{
                borderWidth: 0.5,
                borderColor: '#AAAAAA',
                backgroundColor: subscribed ? '#4D4D4D' : '#F4F4F4'
              }}
              textStyle={{
                color: subscribed ? '#FFFFFF' : '#000000'
              }}
              onPress={handleSubscribe}
              title={subscribed ? i18n2.t('ChannelDetailScreen.Unsubscribe') : i18n2.t('ChannelDetailScreen.Subscribe')}
            />
            <View style={styles.tagWrapper}>
              {detail?.tags?.split(',')?.map((tag) => {
                const isSelected = selectedTag === tag;
                return (
                  <TouchableOpacity
                    key={tag}
                    style={[styles.tagButton, isSelected && styles.tagButtonSelected]}
                    onPress={() => {
                      const nexSelectedTag = tag === selectedTag ? null : tag;
                      setSelectedTag(nexSelectedTag);
                      loadPrograms({
                        channelId,
                        selectedTag: nexSelectedTag,
                        nextToken: null,
                        isLoadMore: false
                      });
                      setNextToken(null);
                    }}>
                    <Text style={[styles.tagText, isSelected && styles.tagTextSelected]}>{`#${tag}`}</Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
          <View style={{ height: 16 }} />
        </>
      }
      ListFooterComponent={
        loadingRef.current && nextToken ? (
          <View style={styles.footerLoading}>
            <ActivityIndicator size='small' color={Colors.loadingColor} />
          </View>
        ) : null
      }
      contentContainerStyle={{ paddingHorizontal: 16, paddingBottom: 16 }}
      renderItem={({ item }) => (
        <TouchableOpacity
          style={styles.program}
          onPress={() => {
            push('ChannelProgram', {
              key: Date.now(),
              channelId: item.channelId,
              programId: item.programId
            });
          }}>
          <Text style={styles.time}>
            {moment(item.createTime * 1000).format(i18n2.t('ChannelDetailScreen.DateFormat'))}
          </Text>
          <ExpoImage
            contentFit='cover'
            style={styles.image}
            source={{ uri: getPreviewUrl(item.cover || '', item.programId) }}
          />
          <View style={styles.contentContainer}>
            <Text style={styles.title} numberOfLines={2}>
              {item.title}
            </Text>
            <View style={styles.statsContainer}>
              <View style={styles.stats}>
                <View style={styles.statItem}>
                  <Image source={require('@/assets/images/channel/icon_view.png')} style={styles.icon} />
                  <Text style={styles.statText}>{item.views || 0}</Text>
                </View>
                <View style={styles.statItem}>
                  <Image
                    source={
                      item.isLiked
                        ? require('@/assets/images/channel/icon-Like2.png')
                        : require('@/assets/images/channel/icon-Like.png')
                    }
                    style={styles.icon}
                  />
                  <Text style={styles.statText}>{item.likeCount || 0}</Text>
                </View>
                <View style={styles.statItem}>
                  <Image
                    source={
                      item.isCollected
                        ? require('@/assets/images/channel/icon-MyCollections2.png')
                        : require('@/assets/images/channel/icon-MyCollections.png')
                    }
                    style={styles.icon}
                  />
                  <Text style={styles.statText}>{item.collect || 0}</Text>
                </View>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      )}
    />
  );
};

export default ChannelProgramScreen;

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  channel: {
    alignItems: 'center',
    padding: 16,
    gap: 16
  },
  cover: {
    width: 100,
    height: 100,
    borderRadius: 50
  },
  description: {
    fontSize: getFontSize().mediumFontSize,
    color: Colors.blue
  },
  tagWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
    gap: 8,
    justifyContent: 'center'
  },
  tagButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    backgroundColor: '#f0f0f0'
  },
  tagButtonSelected: {
    backgroundColor: Colors.answerText
  },
  tagText: {
    fontSize: getFontSize().smallFontSize,
    color: Colors.text
  },
  tagTextSelected: {
    color: '#fff'
  },
  program: {
    width: '100%',
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderRadius: 8,
    marginBottom: 16
  },
  time: {
    lineHeight: 40,
    fontSize: getFontSize().smallFontSize,
    color: '#999',
    paddingHorizontal: 14
  },
  image: {
    width: '100%',
    height: 150,
    backgroundColor: '#f8f8f8'
  },
  contentContainer: {
    padding: 14
  },
  title: {
    fontSize: getFontSize().mediumFontSize,
    color: Colors.text,
    fontWeight: 'bold',
    marginBottom: 8
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  stats: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10
  },
  statText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 2
  },
  icon: {
    width: 16,
    height: 16,
    resizeMode: 'contain',
    marginRight: 2
  },
  footerLoading: {
    padding: 12,
    alignItems: 'center'
  }
});
