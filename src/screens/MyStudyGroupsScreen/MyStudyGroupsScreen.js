import { Alert, Dimensions, Keyboard, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { BorderlessButton, RectButton } from 'react-native-gesture-handler';
import {
  <PERSON><PERSON>,
  KeyboardFlashList,
  KeyboardFlatList,
  LoadingIndicator,
  ModalPopup,
  RedDot,
  getGroupIcon,
  getImage
} from '@/components';
import { goToChat, goToStudyProgressAsync } from '@/utils/goToScreen';

import { AppContext } from '@/context/AppContext';
import { Avatar2 } from '@/components/Avatar2';
import { Colors } from '@/styles/colors';
import { ContactsPage } from '@/components/ContactsPage';
import { ExplanationHeader } from '@/fishMeet/components/ExplanationHeader';
import { FM_SCREEN_PADDING_HORIZONTAL } from '@/fishMeet/styles/fmStyles';
import { FmChip } from '@/fishMeet/components/FmChip';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { FmStyles } from '@/fishMeet/styles/fmStyles';
import { FmText } from '@/fishMeet/components/FmText';
import { InputField } from '@/fishMeet/components/InputField';
import { NavChip } from '@/components/navigation/NavChip';
import React from 'react';
import SearchBar from '@/components/SearchBar';
import { getCurrentUser } from '@/utils/user';
import { getFmMyStudyGroupsScreenStyles } from '@/fishMeet/styles/screens/myStudyGroupsScreenStyles';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { launchMeetingAsync } from '@/utils/meeting';
import { openUrl } from '@/utils/helper';

class MyStudyGroups extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ navigation, route }) => {
    const page = route.params?.page ?? 1;
    const showRedDot = route.params?.showRedDot ?? false;

    const iDigestNavigationOptions = {
      headerTitle: () => {
        return (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              height: 100,
              justifyContent: 'center'
            }}>
            <NavChip
              page={page}
              pageIndex={0}
              title={i18n2.t('Common.Contacts')}
              width={100}
              onPress={() => {
                globalThis.eventEmitter.emit('selectMyStudyGroupPage', 0);
              }}
            />
            <View style={{ width: 7 }} />
            <NavChip
              page={page}
              pageIndex={1}
              title={i18n2.t('GroupsUI.Title')}
              width={100}
              onPress={() => {
                globalThis.eventEmitter.emit('selectMyStudyGroupPage', 1);
              }}
              showRedDot={showRedDot}
            />
          </View>
        );
      },
      headerRight: () => (
        <View style={{ marginRight: 7, flexDirection: 'row' }}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
              globalThis.eventEmitter.emit('addButton');
            }}>
            {getImage('plus')}
          </TouchableOpacity>
        </View>
      )
    };

    const fmNavigationOptions = {
      headerTitleAlign: 'center',
      headerTitle: () => {
        return (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
            <FmChip
              page={page}
              pageIndex={0}
              title={i18n2.t('Common.Contacts')}
              width={120}
              onPress={() => {
                globalThis.eventEmitter.emit('selectMyStudyGroupPage', 0);
              }}
            />
            <View style={{ width: 7 }} />
            <FmChip
              page={page}
              pageIndex={1}
              title={i18n2.t('GroupsUI.Title')}
              width={120}
              onPress={() => {
                globalThis.eventEmitter.emit('selectMyStudyGroupPage', 1);
              }}
              showRedDot={showRedDot}
            />
          </View>
        );
      },
      headerRight: () => (
        <View style={{ marginRight: 7, flexDirection: 'row' }}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
              globalThis.eventEmitter.emit('addButton');
            }}>
            {getImage('plus')}
          </TouchableOpacity>
        </View>
      )
    };

    return isFishMeet ? fmNavigationOptions : iDigestNavigationOptions;
  };

  state = {
    showModal: false,
    showModalItems: [],
    showModalTitle: '',
    showModalHideOnPress: true,
    page: this.props.route.params?.page ?? 1,
    isSearching: false,
    searchText: '',
    busy: false
  };

  constructor(props) {
    super(props);

    this.userId = getCurrentUser().getUserId();
    this.nickname = getCurrentUser().nickname;
    this.listeners = [];
  }

  async componentDidMount() {
    // Load data before handling myStudyGroupsScreenMounted for fishMeet,
    // otherwise tags data will not be loaded properly in ContactsPage.
    if (this.props.navigation?.addListener) {
      this.focusListener = this.props.navigation.addListener('focus', () => {
        this.loadAsync();
        this.context.loadTagsAsync();
      });
    }

    // fishMeet will create this screen twice, once on the groups page and once on the contacts page.
    // This will cause the listeners to be added twice, which will cause problems such as calling this.onPlusPress
    // twice, which will display the modal twice. To prevent this, only add the listeners on this screen once.
    if (globalThis.eventEmitter.listenerCount('myStudyGroupsScreenMounted') >= 1) {
      return;
    }
    this.listeners.push(globalThis.eventEmitter.addListener('myStudyGroupsScreenMounted', () => {}));

    this.listeners.push(
      globalThis.eventEmitter.addListener('languageChanged', () => {
        this.props.navigation.setParams({});
        this.forceUpdate();
      })
    );

    this.listeners.push(globalThis.eventEmitter.addListener('addButton', this.onPlusPress));

    this.listeners.push(
      globalThis.eventEmitter.addListener('selectMyStudyGroupPage', (page) => {
        this.onSelectPage(page);
      })
    );
  }

  componentWillUnmount() {
    this.listeners?.forEach((listener) => {
      listener.remove();
    });

    if (this.focusListener?.remove) {
      this.focusListener.remove();
    }
  }

  handleSearchTextChange = (text) => {
    this.setState({ searchText: text });
  };

  handleSearchTextFocus = () => {
    this.setState({ isSearching: true });
  };

  handleSearchTextClose = () => {
    Keyboard.dismiss();
    this.setState({ isSearching: false, searchText: '' });
  };

  onSelectPage = async (page) => {
    if (page === 0) {
      //only do this for the contact tab
      await this.context.loadFriendsAsync();
    }
    this.setState({ searchText: '', page: page, isSearching: false });
    Keyboard.dismiss();
    this.props.navigation.setParams({ page });
  };

  async loadAsync() {
    this.setState({ busy: true });
    if (this.state.page === 0) {
      //only do this for the contact tab
      await this.context.loadFriendsAsync();
    } else {
      //only do this for the group tab
      await this.context.loadGroupsAsync();
    }
    this.setState({ busy: false });

    this.props.navigation.setParams({
      showRedDot: this.context.groups.findIndex((item) => item.newChatMessage) !== -1
    });
  }

  renderStudyGroupItem = ({ item, index }) => {
    const tag =
      item.isOneOnOneGroup && this.context.userTags[item.friendUserId]
        ? `(${this.context.userTags[item.friendUserId]})`
        : '';
    const fontSize = getCurrentUser().getMediumFontSize();
    const xSmallFontSize = getCurrentUser().getXSmallFontSize();
    const screenWidth = Dimensions.get('window').width - this.context.insets.right - this.context.insets.left;

    const actualBgColor = this.isTopGroup(item) ? '#FFFFFF' : '#F4F4F4';

    const icon = item.isOneOnOneGroup ? (
      <Avatar2
        userId={item.friendUserId}
        size={39}
        defaultIcon={'friend'}
        defaultIconSize={33}
        onPress={() => {
          goToChat(item, this.props.navigation);
        }}
      />
    ) : (
      getGroupIcon(item)
    );

    return (
      <TouchableOpacity
        activeOpacity={1}
        key={index}
        onPress={() => {
          goToChat(item, this.props.navigation);
        }}
        onLongPress={() => {
          this.onGroupLongPress(item);
        }}>
        <View
          key={item.groupId}
          style={{
            marginVertical: 4,
            marginHorizontal: 10,
            backgroundColor: actualBgColor,
            borderColor: '#A2A2A2',
            borderWidth: 1,
            borderRadius: 25,
            height: 50,
            alignItems: 'center',
            flexDirection: 'row'
          }}>
          <View
            style={{
              height: 40,
              width: 40,
              marginHorizontal: 5,
              alignItems: 'center',
              justifyContent: 'center'
            }}>
            {icon}
          </View>

          <View style={{ flexDirection: 'column' }}>
            <Text
              style={{
                fontSize,
                width: screenWidth - 116,
                color: Colors.darkBlue,
                marginHorizontal: 0
              }}
              numberOfLines={1}>
              {item.name} {tag}
            </Text>

            {item.newChatMessage && item.newMentioned ? (
              <Text
                style={{
                  paddingTop: 1,
                  fontSize: xSmallFontSize,
                  color: 'red'
                }}>
                {i18n2.t('GroupsScreen.Mentioned')}
              </Text>
            ) : null}
          </View>

          {item.isOneOnOneGroup ? (
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => {
                const items = [];
                items.push({
                  renderIcon: () => getImage('chat'),
                  text: i18n2.t('Common.Chat'),
                  onPress: () => goToChat(item, this.props.navigation)
                });

                items.push({
                  renderIcon: () => getImage('group'),
                  text: i18n2.t('Common.Management'),
                  onPress: () => this.goToGroupManagement(item)
                });

                this.setState({
                  showModal: true,
                  showModalItems: items,
                  showModalTitle: item.name,
                  showModalHideOnPress: true
                });
              }}
              onLongPress={() => this.onGroupLongPress(item)}>
              {getImage('more', { height: 38, width: 38, opacity: 0.6 })}
            </TouchableOpacity>
          ) : null}

          {!!item.isGroupLeader && !item.isOrgGroup ? (
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => {
                const items = [];
                items.push({
                  renderIcon: () => getImage('chat'),
                  text: i18n2.t('Common.Chat'),
                  onPress: () => goToChat(item, this.props.navigation)
                });

                items.push({
                  renderIcon: () => getImage('attendance'),
                  text: i18n2.t('Common.Attendance'),
                  onPress: () => this.goToAttendanceAsync(item)
                });

                items.push({
                  renderIcon: () => getImage('checkProgress'),
                  text: i18n2.t('Common.StudyProgress'),
                  onPress: () => goToStudyProgressAsync(this.props.navigation, item.groupId, item.name)
                });

                items.push({
                  renderIcon: () => getImage('group'),
                  text: i18n2.t('Common.Management'),
                  onPress: () => this.goToGroupManagement(item)
                });

                this.setState({
                  showModal: true,
                  showModalItems: items,
                  showModalTitle: item.name,
                  showModalHideOnPress: true
                });
              }}
              onLongPress={() => this.onGroupLongPress(item)}>
              {getImage('more', { height: 38, width: 38, opacity: 0.6 })}
            </TouchableOpacity>
          ) : null}
        </View>
        {item.newChatMessage ? <RedDot top={7} left={10} /> : null}
      </TouchableOpacity>
    );
  };

  fmRenderStudyGroupItem = ({ item, index }) => {
    const tag =
      item.isOneOnOneGroup && this.context.userTags[item.friendUserId]
        ? `(${this.context.userTags[item.friendUserId]})`
        : '';

    const screenWidth = Dimensions.get('window').width - this.context.insets.right - this.context.insets.left;

    const horizontalPadding =
      Math.max(this.context.insets.left, FM_SCREEN_PADDING_HORIZONTAL) +
      Math.max(this.context.insets.right, FM_SCREEN_PADDING_HORIZONTAL);
    const contentWidth = screenWidth - horizontalPadding;

    const actualBgColor = this.isTopGroup(item) ? FmColors.grayBlue : FmColors.darkBackground;
    const fmStyles = getFmMyStudyGroupsScreenStyles(this.context.insets);

    const displayNameAndTag = item.name ? `${item.name} ${tag}` : '';
    const lastMessageDisplay = item.lastMessage ?? '';

    return (
      <RectButton
        onPress={() => {
          goToChat(item, this.props.navigation);
        }}
        onLongPress={() => {
          this.onGroupLongPress(item);
        }}>
        <View style={[fmStyles.groupContainer, { backgroundColor: actualBgColor, width: contentWidth }]}>
          <View style={fmStyles.groupHeader}>
            <FmText style={fmStyles.groupNameText} numberOfLines={1} ellipsizeMode='tail'>
              {displayNameAndTag}
            </FmText>

            {item.newChatMessage && item.newMentioned ? (
              <FmText style={fmStyles.metioned}>{i18n2.t('GroupsScreen.Mentioned')}</FmText>
            ) : null}

            <FmText style={fmStyles.latestMessage} numberOfLines={1} ellipsizeMode='tail'>
              {lastMessageDisplay}
            </FmText>
          </View>

          <View style={{ justifyContent: 'center', alignItems: 'center', paddingLeft: 10 }}>
            <BorderlessButton
              onPress={async () => {
                await launchMeetingAsync(item.groupId, true, true);
              }}>
              {getImage('openMeeting', { height: 30, width: 30 })}
            </BorderlessButton>
          </View>
        </View>
        {item.newChatMessage ? <RedDot top={7} left={10} /> : null}
      </RectButton>
    );
  };

  goToGroupManagement = (group) => {
    this.props.navigation.navigate('Group', {
      title: group.name,
      group,
      isOrgGroup: group.orgId !== 0 // org feature not yet well defined, by default 0, no meaning yet
    });
  };

  goToAttendanceAsync = async (group) => {
    const result = await globalThis.dsObject.getGroup(group.groupId);
    if (!result) {
      this.props.navigate.pop();
      return;
    }

    this.props.navigation.navigate('AttendanceHome', {
      group,
      groupName: group.name,
      allUsers: result.body.users
    });
  };

  deleteGroup = (group, onCompleted) => {
    const warnText = group.isOneOnOneGroup
      ? 'GroupsScreen.DeleteOneOnOneGroupWarning'
      : 'GroupsScreen.DeleteGroupWarning';
    Alert.alert(i18n2.t('Common.Warning'), i18n2.t(warnText).replace('{0}', group.name), [
      {
        text: i18n2.t('Common.Confirm'),
        onPress: async () => {
          const result = await globalThis.dsObject.deleteGroup(group.groupId, true, () => onCompleted());
          if (result) {
            // delete group will update my classes
            this.context.checkUserUpdateAsync();
            await this.loadAsync();
          }
        }
      },
      {
        text: i18n2.t('Common.Cancel'),
        onPress: () => onCompleted()
      }
    ]);
  };

  leaveGroup = async (group, onCompleted) => {
    Alert.alert(i18n2.t('Common.Warning'), i18n2.t('GroupsScreen.LeaveGroupWarning').replace('{0}', group.name), [
      {
        text: i18n2.t('Common.Confirm'),
        onPress: async () => {
          const result = await globalThis.dsObject.leaveGroup(group.groupId, true, () => onCompleted());
          if (result) {
            await this.loadAsync();
          }
        }
      },
      {
        text: i18n2.t('Common.Cancel'),
        onPress: () => onCompleted()
      }
    ]);
  };

  isTopGroup = (group) => {
    return this.context.topGroups.indexOf(group.groupId) !== -1;
  };

  onPlusPress = () => {
    const title = i18n2.t('GroupsScreen.NewGroupAndContact');
    const items = [];
    const joinText = i18n2.t('GroupsScreen.JoinGroupInstruction');
    const topTextPos = joinText.indexOf('\n');
    const joinTopText = joinText.slice(0, topTextPos);
    const joinSubText = joinText.slice(topTextPos);
    const connectText = i18n2.t('GroupsScreen.ConnectPerson');
    const connectTopTextPos = connectText.indexOf('\n');
    const scanText = i18n2.t('GroupsScreen.ScanQRInstruction');
    const scanTopTextPos = scanText.indexOf('\n');

    items.push({
      renderIcon: () => getImage('createGroup'),
      text: i18n2.t('GroupsScreen.CreateGroup'),
      onPress: () => {
        this.setState({ showModal: false });
        this.props.navigation.navigate('CreateStudyGroup', {
          classId: 0
        });
      }
    });
    items.push({
      renderIcon: () => getImage('joinGroup'),
      text: joinTopText,
      subText: joinSubText,
      onPress: () => {
        this.setState({ showModal: false });
        this.props.navigation.navigate('JoinStudyGroup');
      }
    });
    items.push({
      renderIcon: () => getImage('promoteLeader'),
      text: connectText.slice(0, connectTopTextPos),
      subText: connectText.slice(connectTopTextPos),
      onPress: () => {
        this.setState({ showModal: false });
        this.props.navigation.navigate('AddFriend', {
          onUpdated: () => {
            globalThis.eventEmitter.emit('friendAdded');
          }
        });
      }
    });
    items.push({
      renderIcon: () => getImage('scanQR'),
      text: scanText.slice(0, scanTopTextPos),
      subText: scanText.slice(scanTopTextPos),
      onPress: () => {
        this.setState({ showModal: false });
        this.props.navigation.navigate('QRScan', {
          title: i18n2.t('GroupsScreen.ScanQR'),
          onQRCode: async (url) => {
            await openUrl(url, this.props.navigation, this.context);
            return;
          }
        });
      }
    });
    this.setState({ showModal: true, showModalItems: items, showModalTitle: title, showModalHideOnPress: false });
  };

  onGroupLongPress = (group) => {
    const items = [];
    if (!group.isOrgGroup) {
      if (group.isOneOnOneGroup || group.isGroupLeader) {
        items.push({
          renderIcon: () => getImage('deleteGroup'),
          text: i18n2.t('GroupsScreen.DeleteGroup'),
          onPress: () => this.deleteGroup(group, () => this.setState({ showModal: false }))
        });
      }

      if (!group.isOneOnOneGroup) {
        items.push({
          renderIcon: () => getImage('exitGroup'),
          text: i18n2.t('GroupsScreen.LeaveGroup'),
          onPress: () => this.leaveGroup(group, () => this.setState({ showModal: false }))
        });
      }
    }

    const isTop = this.isTopGroup(group);
    items.push({
      renderIcon: () => getImage(isTop ? 'groupSetDown' : 'groupSetTop'),
      text: i18n2.t(isTop ? 'GroupsScreen.SetDownGroup' : 'GroupsScreen.SetTopGroup'),
      onPress: () => {
        this.context.setTopGroupsAsync(group.groupId, isTop);
        this.setState({ showModal: false });
      }
    });

    this.setState({ showModal: true, showModalItems: items, showModalTitle: group.name, showModalHideOnPress: false });
  };

  renderModal = () => {
    const { showModal, showModalTitle, showModalItems, showModalHideOnPress } = this.state;
    return (
      <ModalPopup
        hideOnPress={showModalHideOnPress}
        insets={this.context.insets}
        visible={showModal}
        showDivider={true}
        setNotVisible={() => this.setState({ showModal: false })}
        title={showModalTitle}
        items={showModalItems}
        iconsPerRow={showModalItems.length}
        showCancel={true}
      />
    );
  };

  render() {
    const { page } = this.state;
    const fontSize = getCurrentUser().getMediumFontSize();

    if (page === 0) {
      return (
        <>
          <ContactsPage
            searchText={this.state.searchText}
            isSearching={this.state.isSearching}
            handleSearchTextFocus={this.handleSearchTextFocus}
            handleSearchTextClose={this.handleSearchTextClose}
            handleSearchTextChange={this.handleSearchTextChange}
            renderNoFriends={() => {
              return isFishMeet ? (
                <View style={FmStyles.screenContainer}>
                  <ExplanationHeader
                    titleText={i18n2.t('GroupsScreen.HeaderTitle')}
                    explanationText={i18n2.t('GroupsScreen.HeaderExplanationContacts')}
                  />
                  <Button
                    title={i18n2.t('AddFriend')}
                    onPress={() => {
                      this.props.navigation.navigate('AddFriend', {
                        onUpdated: () => {
                          globalThis.eventEmitter.emit('friendAdded');
                        }
                      });
                    }}
                  />
                  <Button
                    title={i18n2.t('GroupsScreen.ScanQR')}
                    onPress={() => {
                      this.setState({ showModal: false });
                      this.props.navigation.navigate('QRScan', {
                        title: i18n2.t('GroupsScreen.ScanQR'),
                        onQRCode: async (url) => {
                          await openUrl(url, this.props.navigation, this.context);
                          return;
                        }
                      });
                    }}
                  />
                </View>
              ) : (
                <View style={styles.noFriendsContainer}>
                  <Text style={[styles.titleText, { fontSize }]}>{i18n2.t('GroupsScreen.AddFriendAction')}</Text>
                </View>
              );
            }}
          />
          {this.renderModal()}
        </>
      );
    }

    return (
      <>
        {this.renderGroups()}
        {this.renderModal()}
      </>
    );
  }

  renderListItem = ({ item }) => {
    const fontSize = getCurrentUser().getMediumFontSize();
    const name = item.value.substring(1);
    const userTag = this.context.userTags[item.userId] || '';
    const isUserBlocked = this.context.blocks[item.userId] || false;
    const isCurrentUserBlocking = this.context.blockedBy.includes(item.userId);
    const blockingColor = isUserBlocked || isCurrentUserBlocking ? { color: '#707070' } : {};

    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('Member', {
            id: item.userId,
            uniqueId: item.uniqueId,
            name,
            email: item.key,
            userTag,
            isUserBlocked,
            isCurrentUserBlocking
          });
        }}>
        <View style={{ height: 60, flexDirection: 'row', alignItems: 'center', paddingLeft: 7 }}>
          <Avatar2
            userId={item.userId}
            onPress={() => {
              this.props.navigation.navigate('Member', {
                id: item.userId,
                uniqueId: item.uniqueId,
                name,
                email: item.key,
                userTag,
                isUserBlocked,
                isCurrentUserBlocking
              });
            }}
          />
          <Text style={{ ...blockingColor, fontSize, marginLeft: 7 }} numberOfLines={1}>
            {name} ({item.key}) {userTag && `(${userTag})`}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  renderSectionHeader = (section) => {
    const fontSize = getCurrentUser().getLargeFontSize();
    return (
      <View style={{ height: 40, backgroundColor: Colors.lightBlue, paddingLeft: 10, justifyContent: 'center' }}>
        <Text style={{ fontSize, fontWeight: 'bold' }}>{section.title}</Text>
      </View>
    );
  };

  renderGroups = () => {
    const { searchText, isSearching } = this.state;
    const fontSize = getCurrentUser().getMediumFontSize();
    let acceptedStudyGroups = this.context.groups.filter(
      (studyGroup) =>
        studyGroup.status &&
        !this.context.blockedBy.includes(studyGroup.friendUserId) &&
        !this.context.blocks?.[studyGroup.friendUserId]
    );

    if (searchText?.trim()) {
      acceptedStudyGroups = acceptedStudyGroups.filter((item) => {
        return (
          item.name.toLowerCase().includes(searchText.toLowerCase()) ||
          (item.isOneOnOneGroup &&
            this.context.userTags[item.friendUserId]?.toLowerCase().includes(searchText.toLowerCase()))
        );
      });
    }

    if (acceptedStudyGroups.length === 0 && !searchText?.trim()) {
      if (this.state.busy) {
        return <LoadingIndicator />;
      }

      return isFishMeet ? this.renderNoAcceptedGroupsFishMeet() : this.renderNoAcceptedGroupsIDigest(fontSize);
    }

    acceptedStudyGroups.sort((a, b) => {
      const aTop = this.isTopGroup(a) ? 1 : 0;
      const bTop = this.isTopGroup(b) ? 1 : 0;
      return bTop - aTop;
    });
    const nShow = acceptedStudyGroups.length;
    if (nShow > 120) {
      acceptedStudyGroups = acceptedStudyGroups.slice(0, 120);
    }
    /*
    const acceptedStudyGroupsTopList = [];
    const acceptedStudyGroupsNormalList = [];
    let nshow = 0;
    for (const item of acceptedStudyGroups) {
      if (this.isTopGroup(item)) {
        acceptedStudyGroupsTopList.push(item);
      } else {
        nshow++;
        //only show up to 60 groups.  All groups can be found through search still
        if (nshow <= 120) {
          acceptedStudyGroupsNormalList.push(item);
        }
      }
    }
    */

    return isFishMeet
      ? this.renderAcceptedGroupsFishMeet(searchText, isSearching, fontSize, acceptedStudyGroups, nShow)
      : this.renderAcceptedGroupsIDigest(searchText, isSearching, fontSize, acceptedStudyGroups, nShow);
  };

  renderNoAcceptedGroupsIDigest = (fontSize) => (
    <View
      style={{
        flex: 1,
        paddingLeft: this.context.insets.left,
        paddingRight: this.context.insets.right,
        alignContent: 'center',
        justifyContent: 'center'
      }}>
      <Text style={[styles.titleText, { fontSize }]}>{i18n2.t('GroupsScreen.CreateGroupAction')}</Text>
    </View>
  );

  renderNoAcceptedGroupsFishMeet = () => {
    return (
      <View style={FmStyles.screenContainer}>
        <ExplanationHeader
          titleText={i18n2.t('GroupsScreen.HeaderTitle')}
          explanationText={i18n2.t('GroupsScreen.HeaderExplanation')}
        />
        <Button
          title={i18n2.t('CreateNewStudyGroup')}
          onPress={() =>
            this.props.navigation.navigate('CreateStudyGroup', {
              classId: 0
            })
          }
        />
      </View>
    );
  };

  renderAcceptedGroupsIDigest = (searchText, isSearching, fontSize, acceptedStudyGroups, nshow) => {
    return (
      <View style={{ flex: 1, paddingLeft: this.context.insets.left, paddingRight: this.context.insets.right }}>
        <View style={{ marginVertical: 10 }}>
          <SearchBar
            autoFocus={false}
            value={searchText}
            isCloseVisible={isSearching}
            onFocus={this.handleSearchTextFocus}
            onClose={this.handleSearchTextClose}
            onChangeText={this.handleSearchTextChange}
          />
        </View>
        <KeyboardFlatList
          data={acceptedStudyGroups}
          renderItem={this.renderStudyGroupItem}
          ListFooterComponent={
            nshow <= 120 ? null : (
              <Text style={{ fontSize, color: 'red', margin: 10, textAlign: 'left' }}>
                {i18n2.t('GroupsScreen.Max120Reached')}
              </Text>
            )
          }
        />
      </View>
    );
  };

  renderAcceptedGroupsFishMeet = (searchText, isSearching, fontSize, acceptedStudyGroups, nShow) => {
    const displayedGroups = acceptedStudyGroups.slice(0, nShow);
    const fmStyles = getFmMyStudyGroupsScreenStyles(this.context.insets);

    return (
      <View style={fmStyles.acceptedGroupsContainer}>
        <View>
          <InputField
            value={searchText}
            placeholder={i18n2.t('EnterSearch')}
            showClearButton={true}
            onClearButton={this.handleSearchTextClose}
            onChangeText={this.handleSearchTextChange}
            onSubmitEditing={() => Keyboard.dismiss()}
          />
        </View>

        <KeyboardFlashList
          data={displayedGroups}
          keyExtractor={(item) => item.groupId.toString()}
          renderItem={this.fmRenderStudyGroupItem}
          estimatedItemSize={94}
          estimatedListSize={{ width: Dimensions.get('window').width, height: Dimensions.get('window').height }}
          ListFooterComponent={
            nShow <= 120 ? null : <FmText style={fmStyles.footerText}>{i18n2.t('GroupsScreen.Max120Reached')}</FmText>
          }
        />
      </View>
    );
  };
}

const styles = StyleSheet.create({
  titleText: {
    color: Colors.darkBlue,
    textAlign: 'center',
    marginTop: 7
  }
});

export default MyStudyGroups;
