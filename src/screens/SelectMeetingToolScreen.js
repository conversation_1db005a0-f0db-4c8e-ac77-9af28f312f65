import { <PERSON><PERSON>, <PERSON><PERSON>View, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { Button } from '@/components';
import RadioGroup from 'react-native-radio-buttons-group';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';

export default class SelectMeetingToolScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ route }) => ({
    title: route.params.title,
    gestureEnabled: true
  });

  onPress = async (selectedId) => {
    this.setState({ selectedId });
    this.props.route.params.onSelect(selectedId);
    await getCurrentUser().setPropertyAsync('setting.meetingTool', selectedId);
  };

  constructor(props) {
    super(props);

    this.radioButtons = [
      {
        id: 0, // acts as primary key, should be unique and non-empty string
        label: i18n2.t('SelectMeetingToolScreen.Browser'),
        value: i18n2.t('SelectMeetingToolScreen.Browser')
      }
    ];
    this.radioButtons1 = [
      {
        id: 1,
        label: i18n2.t('SelectMeetingToolScreen.FishMeet'),
        value: i18n2.t('SelectMeetingToolScreen.FishMeet')
      }
    ];
    this.radioButtons2 = [
      {
        id: 2,
        label: i18n2.t('SelectMeetingToolScreen.Jitsi'),
        value: i18n2.t('SelectMeetingToolScreen.Jitsi')
      }
    ];
    this.state = {
      selectedId: this.props.route.params.selectedId
    };
  }

  render() {
    const fontSize = getCurrentUser().getSmallFontSize();

    return (
      <ScrollView
        style={{
          flex: 1,
          paddingLeft: this.context.insets.left,
          paddingRight: this.context.insets.right
        }}
        scrollIndicatorInsets={{ right: 1 }}>
        <View style={{ flex: 1, alignItems: 'left', marginVertical: 5 }}>
          <RadioGroup
            containerStyle={{ alignItems: 'left', marginRight: 25, fontSize }}
            labelStyle={{ fontSize }}
            radioButtons={this.radioButtons}
            onPress={this.onPress}
            selectedId={this.state.selectedId}
          />
          <RadioGroup
            containerStyle={{ alignItems: 'left', marginRight: 25 }}
            labelStyle={{ fontSize }}
            radioButtons={this.radioButtons1}
            onPress={this.onPress}
            selectedId={this.state.selectedId}
          />
          <Button
            title={i18n2.t('SelectMeetingToolScreen.DownloadFishMeet')}
            width={160}
            style={{ marginHorizontal: 60 }}
            textStyle={{ fontSize }}
            onPress={() => {
              Linking.openURL(getCurrentUser().getDownloadServer() + '/fishmeet');
            }}></Button>
          <RadioGroup
            containerStyle={{ alignItems: 'left', marginRight: 25 }}
            labelStyle={{ fontSize }}
            radioButtons={this.radioButtons2}
            onPress={this.onPress}
            selectedId={this.state.selectedId}
          />
          <Button
            title={i18n2.t('SelectMeetingToolScreen.DownloadJitsi')}
            width={160}
            style={{ marginHorizontal: 60 }}
            textStyle={{ fontSize }}
            onPress={() => {
              Linking.openURL(getCurrentUser().getDownloadServer() + '/jitsi');
            }}></Button>
        </View>
        <View style={{ height: this.context.insets.bottom + 10 }} />
      </ScrollView>
    );
  }
}
