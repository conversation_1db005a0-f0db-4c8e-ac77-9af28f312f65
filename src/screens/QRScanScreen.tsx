import { Camera, CameraView } from 'expo-camera';
import React, { useCallback, useState } from 'react';

import { StyleSheet } from 'react-native';
import { notifyPermissionIssue } from '@/utils/media';
import { useFocusEffect } from '@react-navigation/native';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function QRScanScreen({ route, navigation }: any) {
  const [granted, setGranted] = useState(false);
  const [gotCode, setGotCode] = useState(false);

  useSetNavigationOptions({
    title: route.params.title
  });

  const checkCameraPermissions = useCallback(() => {
    async function getCameraPermissions() {
      const { granted } = await Camera.requestCameraPermissionsAsync();
      if (!granted) {
        notifyPermissionIssue('Camera');
        navigation.pop();
        return;
      }
      setGranted(granted);
    }

    getCameraPermissions();
  }, [navigation]);

  useFocusEffect(checkCameraPermissions);

  if (!granted) {
    return null;
  }

  return (
    <CameraView
      style={styles.camStyle}
      facing='back'
      barcodeScannerSettings={{
        barcodeTypes: ['qr']
      }}
      onBarcodeScanned={({ data }) => {
        console.log(data);
        //only react to qrcode once. But this event gets fired
        //multiple times when camera is not holding steady...
        if (!gotCode) {
          setGotCode(true);
          navigation.pop();
          route.params.onQRCode(data);
        }
      }}
    />
  );
}

export default QRScanScreen;

const styles = StyleSheet.create({
  camStyle: {
    position: 'absolute',
    width: '100%',
    height: '100%'
  }
});
