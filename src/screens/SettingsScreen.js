import * as ImageManipulator from 'expo-image-manipulator';
import * as Linking from 'expo-linking';
import * as Updates from 'expo-updates';

import { Alert, StyleSheet, Text, View } from 'react-native';
import { FmText } from '@/fishMeet/components/FmText';
import { LoadingIndicator, RedDot, getImage } from '@/components';
import { getDisappearingMessagesChoicesName, getDisappearingMessagesText } from '@/utils/disappearingMessages';
import { getPushTokenAsync, unregisterForPushNotificationsAsync } from '@/utils/notification';
import { pickImageAsync, takePictureAsync } from '@/utils/media';

import { AppContext } from '@/context/AppContext';
import { Avatar2 } from '@/components/Avatar2';
import { Colors } from '@/styles/colors';
import { Config } from '@/dataStorage/config';
import Constants from 'expo-constants';
import { ExplanationHeader } from '@/fishMeet/components/ExplanationHeader';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { ModalPopup } from '@/components/ModalPopup';
import React from 'react';
import SettingsList from '@/components/SettingsList';
import { appVersion } from '@/dataStorage/storage';
import { getCurrentUser } from '@/utils/user';
import { getFmSettingsScreenStyles } from '@/fishMeet/styles/screens/settingsScreenStyles';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { removeLocalFilesAsync } from '@/dataStorage/localStorage';

import { showMessage } from '@/components/MessageBox';

const SettingItemText = (props) => (
  <View style={{ flex: 1, justifyContent: 'center' }}>
    <Text
      style={{
        textAlign: 'right',
        fontSize: props.fontSize,
        paddingRight: 16,
        color: Colors.darkBlue
      }}
      numberOfLines={1}>
      {props.text}
    </Text>
  </View>
);

export default class SettingsScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = () => ({
    title: i18n2.t('SettingsUI.Title')
  });

  constructor(props) {
    super(props);

    this.displayLanguageText = [];
    Config.Languages.map((item) => this.displayLanguageText.push(item.DisplayName));

    this.userId = getCurrentUser().getUserId();
    this.listeners = [];

    this.meetingToolNames = [];

    this.state = {
      loginId: getCurrentUser().getLoginId(),
      fontSize: '',
      bibleVersion: '',
      displayLanguage: '',
      busy: false,
      showModal: false,
      showModalItems: [],
      showModalTitle: '',
      meetingTool: 0
    };
  }

  componentDidMount() {
    this.listeners.push(globalThis.eventEmitter.addListener('languageChanged', this.reloadUI));
    this.listeners.push(globalThis.eventEmitter.addListener('bibleChanged', this.reloadUI));
    this.listeners.push(globalThis.eventEmitter.addListener('fontChanged', this.reloadUI));
    this.reloadUI();
  }

  componentWillUnmount() {
    this.listeners?.forEach((listener) => {
      listener.remove();
    });
  }

  reloadUI = () => {
    this.fontSizeText = [
      i18n2.t('SettingsScreen.Small'),
      i18n2.t('SettingsScreen.Medium'),
      i18n2.t('SettingsScreen.Large'),
      i18n2.t('SettingsScreen.ExtraLarge')
    ];

    this.meetingToolNames = [
      i18n2.t('SettingsScreen.Browser'),
      i18n2.t('SettingsScreen.FishMeet'),
      i18n2.t('SettingsScreen.Jitsi')
    ];

    this.props.navigation.setParams({
      title: isFishMeet ? i18n2.t('SettingsUI.FishMeetTitle') : i18n2.t('SettingsUI.Title')
    });

    this.setState({
      fontSize: this.fontSizeText[getCurrentUser().getFontSize() - 1],
      bibleVersion: getCurrentUser().getBibleVersionDisplayName(),
      displayLanguage: getCurrentUser().getLanguageDisplayName(),
      meetingTool: getCurrentUser().getProperty('setting.meetingTool') ?? 0
    });
  };

  async checkUpdate() {
    try {
      this.setState({ busy: true });
      const { isAvailable } = await Updates.checkForUpdateAsync();
      if (isAvailable) {
        await Updates.fetchUpdateAsync();
        Updates.reloadAsync();
      } else {
        showMessage({
          message: i18n2.t('SettingsScreen.AppUpToDate'),
          type: 'success'
        });
      }
    } catch (error) {
      showMessage({
        message: i18n2.t('SettingsScreen.NoUpdate')
      });
    } finally {
      this.setState({ busy: false });
    }
  }

  setAvatarAsync = async (data) => {
    try {
      this.setState({ busy: true });
      const actions = [];
      const MaxImageWidth = 1080;
      if (data.image.width > MaxImageWidth) {
        // Resize image if width is exceeding MaxImageWidth
        const ratio = data.image.width / MaxImageWidth;
        actions.push({ resize: { width: MaxImageWidth, height: data.image.height / ratio } });
      }

      // Compress image to 80% JPEG
      const result = await ImageManipulator.manipulateAsync(data.image.uri, actions, {
        compress: 0.8,
        format: ImageManipulator.SaveFormat.JPEG
      });

      const localUri = result.uri;
      const succeed = await globalThis.dsObject.setUserAvatars(localUri);
      if (succeed) {
        globalThis.eventEmitter.emit('avatarChanged');
      }
    } catch (error) {
      showMessage({
        message: error.message,
        type: 'danger'
      });
    } finally {
      this.setState({ busy: false });
    }
  };

  changeAvatar = () => {
    const showModalItems = [
      {
        renderIcon: () => getImage('camera'),
        text: i18n2.t('Common.Camera'),
        onPress: async () => {
          await takePictureAsync(this.setAvatarAsync, { allowsEditing: true, aspect: [1, 1] });
          this.setState({ showModal: false });
        }
      },
      {
        renderIcon: () => getImage('photo'),
        text: i18n2.t('Common.Photo'),
        onPress: async () => {
          await pickImageAsync(this.setAvatarAsync, {
            mediaTypes: 'Images',
            allowsEditing: true,
            aspect: [1, 1]
          });
          this.setState({ showModal: false });
        }
      }
    ];

    this.setState({
      showModal: true,
      showModalItems,
      showModalTitle: i18n2.t('SettingsScreen.SetAvatar'),
      showModalHideOnPress: false
    });
  };

  handleFontSize = () => {
    this.props.navigation.navigate('Select', {
      choices: [1, 2, 3, 4],
      title: i18n2.t('SettingsScreen.FontSize'),
      text: i18n2.t('SettingsScreen.SelectFontSize'),
      getDisplayName: (choice) => this.fontSizeText[choice - 1],
      isCurrent: (choice) => choice === getCurrentUser().getFontSize(),
      onSelect: async (choice) => {
        await getCurrentUser().setFontSizeAsync(choice);
      }
    });
  };

  handleDisplayLng = () => {
    const options = {
      choices: [0, 1, 2],
      getDisplayName: (choice) => this.displayLanguageText[choice],
      isCurrent: (choice) => this.displayLanguageText[choice] === getCurrentUser().getLanguageDisplayName(),
      onSelect: async (choice) => {
        const lang = Config.Languages[choice].Value;
        await getCurrentUser().setLanguageAsync(lang);
      }
    };

    if (isFishMeet) {
      options.title = '';
      options.titleControl = (
        <ExplanationHeader
          titleText={i18n2.t('SelectScreen.ChangeLanguageHeaderTitle')}
          explanationText={i18n2.t('SelectScreen.ChangeLanguageHeaderExplanation')}
        />
      );
    } else {
      options.title = i18n2.t('SettingsScreen.DisplayLng');
      options.text = i18n2.t('SettingsScreen.ChooseLng');
    }

    this.props.navigation.navigate('Select', options);
  };

  handleDisappearMsgGroup = () => {
    const value = [0, 4 * 7 * 24 * 60, 7 * 24 * 60, 24 * 60, 8 * 60, 60, 5, 1];
    const { navigation } = this.props;
    const { userProfile } = this.context;

    navigation.navigate('Select', {
      choices: [0, 1, 2, 3, 4, 5, 6, 7],
      title: i18n2.t('SettingsScreen.DisappearMsgGroup'),
      titleControl: isFishMeet ? (
        <FmText
          style={{
            fontSize: getCurrentUser().getLargeFontSize(),
            color: 'red',
            fontWeight: 'bold',
            margin: 10
          }}>
          {i18n2.t('GroupsScreen.MsgDisappearWarning')}
        </FmText>
      ) : (
        <Text
          style={{
            fontSize: getCurrentUser().getLargeFontSize(),
            color: 'red',
            fontWeight: 'bold',
            margin: 10
          }}>
          {i18n2.t('GroupsScreen.MsgDisappearWarning')}
        </Text>
      ),
      getDisplayName: (choice) => getDisappearingMessagesChoicesName(choice),
      isCurrent: (choice) => choice === value.indexOf(userProfile.hideMessages),
      onSelect: async (choice) => {
        if (value[choice] === 0 || (userProfile.hideMessages !== 0 && value[choice] > userProfile.hideMessages)) {
          await this.context.setUserProfileAsync({ hideMessages: value[choice] });
        } else {
          Alert.alert(
            i18n2.t('Common.Warning'),
            i18n2.t('GroupsScreen.MsgDisappearWarning') + '\n\n' + getDisappearingMessagesText(value[choice]),
            [
              {
                text: i18n2.t('Common.Confirm'),
                onPress: async () => {
                  await this.context.setUserProfileAsync({ hideMessages: value[choice] });
                }
              },
              {
                text: i18n2.t('Common.Cancel'),
                onPress: () => {}
              }
            ]
          );
        }
      }
    });
  };

  handleLogout = async () => {
    try {
      const token = await getPushTokenAsync(false);
      if (token) {
        await unregisterForPushNotificationsAsync(token);
      }
    } catch (error) {
      console.log(error);
    }
    try {
      removeLocalFilesAsync();
    } catch (error) {
      console.log(error);
    }
    await getCurrentUser().logoutAsync();
  };

  render() {
    const fmStyles = getFmSettingsScreenStyles();
    const fontSize = getCurrentUser().getSmallFontSize();
    const { inStoreUpdateNeeded, inAppUpdateNeeded, insets, userProfile } = this.context;
    const { showModal, showModalTitle, showModalItems, meetingTool } = this.state;
    const { thirdPartyLogin } = getCurrentUser();

    return isFishMeet
      ? this.fmRender(
          insets,
          fmStyles,
          fontSize,
          userProfile,
          thirdPartyLogin,
          inAppUpdateNeeded,
          inStoreUpdateNeeded,
          showModal,
          showModalTitle,
          showModalItems
        )
      : this.iDigestRender(
          insets,
          fontSize,
          userProfile,
          thirdPartyLogin,
          meetingTool,
          inAppUpdateNeeded,
          inStoreUpdateNeeded,
          showModal,
          showModalTitle,
          showModalItems
        );
  }

  iDigestRender = (
    insets,
    fontSize,
    userProfile,
    thirdPartyLogin,
    meetingTool,
    inAppUpdateNeeded,
    inStoreUpdateNeeded,
    showModal,
    showModalTitle,
    showModalItems
  ) => (
    <View
      style={{
        flex: 1,
        paddingLeft: insets.left,
        paddingRight: insets.right
      }}>
      <SettingsList underlayColor='#FFFFFF' borderColor='#DCDCDC'>
        <SettingsList.Item
          title={i18n2.t('SettingsScreen.MyEmail')}
          titleStyle={{ fontSize }}
          titleInfo={this.state.loginId}
          titleInfoStyle={[styles.infoStyle, { fontSize }]}
          hasNavArrow={false}
        />
        <SettingsList.Item
          title={i18n2.t('SettingsScreen.MyAccountNoAndQR')}
          titleStyle={{ fontSize }}
          titleInfo={userProfile.uniqueId ?? ''}
          titleInfoStyle={[styles.infoStyle, { fontSize, paddingRight: 15 }]}
          arrowIcon={<View style={{ justifyContent: 'center', right: 15 }}>{getImage('scanQR')}</View>}
          onPress={() => {
            this.props.navigation.navigate('MyInfo');
          }}
        />
        <SettingsList.Item
          title={i18n2.t('SettingsScreen.MyDisplayName')}
          titleStyle={{ fontSize }}
          rightSideContent={<SettingItemText fontSize={fontSize} text={userProfile.displayName} />}
          onPress={() => {
            this.props.navigation.navigate('UpdateName');
          }}
        />
        <SettingsList.Item
          title={i18n2.t('SettingsScreen.Avatar')}
          titleStyle={{ fontSize }}
          hasNavArrow={true}
          arrowIcon={
            <View style={{ marginTop: 5, marginRight: 15 }}>
              <Avatar2
                userId={this.userId}
                onPress={() => {
                  this.changeAvatar();
                }}
              />
            </View>
          }
          onPress={() => {
            this.changeAvatar();
          }}
        />
        {!thirdPartyLogin && (
          <SettingsList.Item
            title={i18n2.t('Common.Pwd')}
            titleStyle={{ fontSize }}
            rightSideContent={<SettingItemText fontSize={fontSize} text={'********'} />}
            onPress={() => {
              this.props.navigation.navigate('UpdatePassword');
            }}
          />
        )}
        <SettingsList.Item
          title={i18n2.t('SettingsScreen.FontSize')}
          titleStyle={{ fontSize }}
          rightSideContent={<SettingItemText fontSize={fontSize} text={this.state.fontSize} />}
          onPress={this.handleFontSize}
        />
        <SettingsList.Item
          title={i18n2.t('SettingsScreen.DisplayLng')}
          titleStyle={{ fontSize }}
          rightSideContent={<SettingItemText fontSize={fontSize} text={this.state.displayLanguage} />}
          onPress={this.handleDisplayLng}
        />
        <SettingsList.Item
          title={i18n2.t('BibleVer')}
          titleStyle={{ fontSize }}
          rightSideContent={<SettingItemText fontSize={fontSize} text={this.state.bibleVersion} />}
          onPress={() => {
            this.props.navigation.navigate('BibleSelect');
          }}
        />
        <SettingsList.Item
          title={i18n2.t('SettingsScreen.DisappearMsgGroup')}
          titleStyle={{ fontSize }}
          titleInfo={getDisappearingMessagesText(userProfile.hideMessages)}
          titleInfoStyle={[styles.infoStyle, { fontSize, paddingRight: 15 }]}
          hasNavArrow={true}
          onPress={this.handleDisappearMsgGroup}
        />
        <SettingsList.Item
          title={i18n2.t('SettingsScreen.MeetingTool')}
          titleStyle={{ fontSize }}
          rightSideContent={<SettingItemText fontSize={fontSize} text={this.meetingToolNames[meetingTool] ?? ''} />}
          onPress={() => {
            this.props.navigation.navigate('SelectMeetingTool', {
              selectedId: this.state.meetingTool,
              title: i18n2.t('SettingsScreen.MeetingTool'),
              onSelect: (selectedId) => {
                this.setState({ meetingTool: selectedId });
              }
            });
          }}
        />
        <SettingsList.Item
          title={`${i18n2.t('SettingsScreen.AppVer')} (v${Constants.expoConfig.version})`}
          titleStyle={{ fontSize }}
          titleInfo={inAppUpdateNeeded || inStoreUpdateNeeded ? i18n2.t('SettingsScreen.ClickToUpdateApp') : appVersion}
          titleInfoStyle={[
            styles.infoStyle,
            {
              fontSize,
              paddingRight: 15,
              color: inAppUpdateNeeded || inStoreUpdateNeeded ? 'red' : Colors.darkBlue,
              fontWeight: inAppUpdateNeeded || inStoreUpdateNeeded ? 'bold' : 'normal'
            }
          ]}
          arrowIcon={
            <View style={{ justifyContent: 'center', right: 15 }}>
              {getImage('refresh')}
              {inAppUpdateNeeded || inStoreUpdateNeeded ? <RedDot top={7} right={0} /> : null}
            </View>
          }
          onPress={() => {
            if (inStoreUpdateNeeded) {
              Linking.openURL(getCurrentUser().getDownloadServer() + '/install');
            } else {
              this.checkUpdate();
            }
          }}
          onLongPress={async () => {
            await Updates.fetchUpdateAsync();
            Updates.reloadAsync();
          }}
        />
        <SettingsList.Item
          title={i18n2.t('SettingsScreen.Donation')}
          titleStyle={{ fontSize }}
          hasNavArrow={true}
          arrowIcon={<View style={{ justifyContent: 'center', right: 15 }}>{getImage('donation')}</View>}
          onPress={() => {
            Linking.openURL(
              'https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=Z62ACEEMVLFNN&source=url'
            );
          }}
        />
        <SettingsList.Item
          title={i18n2.t('SendFeedbackScreen.Title')}
          titleStyle={{ fontSize }}
          hasNavArrow={true}
          arrowIcon={<View style={{ justifyContent: 'center', right: 15 }}>{getImage('feedback')}</View>}
          onPress={() => {
            this.props.navigation.navigate('SendFeedback');
          }}
        />
        <SettingsList.Item
          title={i18n2.t('Common.Advanced')}
          titleStyle={{ fontSize }}
          hasNavArrow={true}
          onPress={() => {
            this.props.navigation.navigate('AdvancedSettings');
          }}
        />
        <SettingsList.Item
          title={i18n2.t('SettingsScreen.Logout')}
          titleStyle={{ fontSize }}
          hasNavArrow={true}
          arrowIcon={<View style={{ justifyContent: 'center', right: 15 }}>{getImage('logout')}</View>}
          onPress={async () => {
            try {
              const token = await getPushTokenAsync(false);
              if (token) {
                await unregisterForPushNotificationsAsync(token);
              }
            } catch (error) {
              console.log(error);
            }

            try {
              removeLocalFilesAsync();
            } catch (error) {
              console.log(error);
            }

            await getCurrentUser().logoutAsync();
          }}
        />
        <SettingsList.Item
          title={i18n2.t('UserScreen.DeleteAcct')}
          titleStyle={{ fontSize }}
          hasNavArrow={true}
          onPress={() => {
            this.props.navigation.navigate('UserDelete');
          }}
        />
      </SettingsList>
      <ModalPopup
        hideOnPress={false}
        insets={this.context.insets}
        visible={showModal}
        showDivider={true}
        setNotVisible={() => this.setState({ showModal: false })}
        title={showModalTitle}
        items={showModalItems}
        iconsPerRow={showModalItems.length}
        showCancel={true}
      />
      {this.state.busy ? <LoadingIndicator /> : null}
    </View>
  );

  fmRender = (
    insets,
    fmStyles,
    fontSize,
    userProfile,
    thirdPartyLogin,
    inAppUpdateNeeded,
    inStoreUpdateNeeded,
    showModal,
    showModalTitle,
    showModalItems
  ) => (
    <View style={{ flex: 1, paddingLeft: insets.left, paddingRight: insets.right }}>
      {/* First part */}
      <View style={[fmStyles.userInfoContainer, { flexDirection: 'row', alignItems: 'center', padding: 10 }]}>
        <View style={{ marginRight: 16 }}>
          <Avatar2
            userId={this.userId}
            size={65}
            onPress={() => {
              this.changeAvatar();
            }}
          />
        </View>
        <View style={{ flex: 1, justifyContent: 'center' }}>
          <FmText
            onPress={() => {
              this.props.navigation.navigate('UpdateName');
            }}
            style={fmStyles.userNameStyle}
            numberOfLines={1}
            ellipsizeMode='tail'>
            {userProfile.displayName}
          </FmText>
          <FmText style={fmStyles.userIdStyle} numberOfLines={1} ellipsizeMode='tail'>
            {'User Id: ' + userProfile.uniqueId}
          </FmText>
        </View>
      </View>

      {/* Second part */}
      <View style={fmStyles.groupContainer}>
        <SettingsList underlayColor={FmColors.background} borderColor={FmColors.mediumGray50}>
          <SettingsList.Item
            title={i18n2.t('SettingsScreen.LoginInformation')}
            titleStyle={{ fontSize }}
            hasNavArrow={false}
            onPress={() => {
              //this.props.navigation.navigate('LoginInfoSetting');
              // Will use the above navigator after LoginInfoSetting screen has more functions
              this.props.navigation.navigate('UpdatePassword', { isFishMeet: true });
            }}
          />
          <SettingsList.Item
            title={i18n2.t('SettingsScreen.FontSize')}
            titleStyle={{ fontSize }}
            hasNavArrow={false}
            onPress={this.handleFontSize}
          />
          <SettingsList.Item
            title={i18n2.t('SettingsScreen.DisplayLng')}
            titleStyle={{ fontSize }}
            hasNavArrow={false}
            onPress={this.handleDisplayLng}
          />
          <SettingsList.Item
            title={i18n2.t('SettingsScreen.DisappearMsgGroup')}
            titleStyle={{ fontSize }}
            titleInfoStyle={[fmStyles.infoStyle, { fontSize, paddingRight: 15 }]}
            hasNavArrow={false}
            onPress={this.handleDisappearMsgGroup}
          />
          <SettingsList.Item
            title={i18n2.t('SendFeedbackScreen.Title')}
            titleStyle={{ fontSize }}
            hasNavArrow={false}
            onPress={() => {
              this.props.navigation.navigate('SendFeedback');
            }}
          />
          <SettingsList.Item
            title={i18n2.t('Common.Advanced')}
            titleStyle={{ fontSize }}
            hasNavArrow={true}
            onPress={() => {
              this.props.navigation.navigate('AdvancedSettings');
            }}
          />
        </SettingsList>
      </View>

      {/* Third part */}
      <View style={fmStyles.groupContainer}>
        <SettingsList underlayColor={FmColors.background} borderColor={FmColors.mediumGray50}>
          <SettingsList.Item
            title={i18n2.t('SettingsScreen.Logout')}
            titleStyle={{ fontSize }}
            hasNavArrow={false}
            onPress={this.handleLogout}
          />
          <SettingsList.Item
            title={i18n2.t('UserScreen.DeleteAcct')}
            titleStyle={{ fontSize }}
            hasNavArrow={false}
            onPress={() => {
              this.props.navigation.navigate('UserDelete');
            }}
          />
        </SettingsList>
      </View>

      <ModalPopup
        hideOnPress={false}
        insets={this.context.insets}
        visible={showModal}
        setNotVisible={() => this.setState({ showModal: false })}
        title={showModalTitle}
        items={showModalItems}
        iconsPerRow={showModalItems.length}
      />
      {this.state.busy ? <LoadingIndicator /> : null}
    </View>
  );
}
const styles = StyleSheet.create({
  infoStyle: {
    color: Colors.darkBlue
  }
});
