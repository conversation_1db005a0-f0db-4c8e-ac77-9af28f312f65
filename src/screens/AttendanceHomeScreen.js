import { Alert, Dimensions, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { LoadingIndicator, NavBarTitle } from '@/components';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import { Entypo } from '@expo/vector-icons';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';

export default class AttendanceHomeScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ route }) => ({
    headerTitle: () => <NavBarTitle title={route.params?.groupName ?? ''} subTitle={i18n2.t('Common.Attendance')} />
  });

  state = {
    data: null
  };

  constructor(props) {
    super(props);

    this.group = props.route.params.group;
    this.allUsers = props.route.params.allUsers;
    this.existingDate = {};
    this.users = [];
    this.loadNeeded = true;
  }

  componentDidMount() {
    this.props.navigation.addListener('focus', () => {
      if (this.loadNeeded) {
        this.loadAsync();
        this.loadNeeded = false;
      }
    });
  }

  async loadAsync() {
    const result = await globalThis.dsObject.getAttendance(this.group.groupId);
    if (!result) {
      this.props.navigation.pop();
      return;
    }

    const { attendance, users } = result.body;
    this.existingDate = {};
    attendance.forEach((item) => {
      this.existingDate[new Date(item.date).toDateString()] = 1;
    });

    this.users = users;
    this.setState({ data: attendance });
  }

  getUsersWithDetails = (userIdArray) => {
    return userIdArray.map((userId) => this.users[userId]);
  };

  deleteRecordAsync = async (date) => {
    const dateString = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    const result = await globalThis.dsObject.deleteAttendance(this.group.groupId, dateString);
    if (!result) {
      return;
    }

    await this.loadAsync();
  };

  render() {
    const { data } = this.state;
    if (!Array.isArray(data)) {
      return <LoadingIndicator />;
    }

    let keyIndex = 0;
    const now = new Date();
    const marginHorizontal = 5;
    const windowWidth = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right;
    const size = Math.min(windowWidth / 4 - marginHorizontal * 2, 85);
    return (
      <ScrollView
        scrollIndicatorInsets={{ right: 1 }}
        style={{
          backgroundColor: 'white',
          marginLeft: this.context.insets.left,
          marginRight: this.context.insets.right
        }}>
        <View
          style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            paddingLeft: (windowWidth % (size + marginHorizontal * 2)) / 2
          }}>
          {data.map((item) => {
            const { users, allUsers } = item;
            const date = new Date(item.date);
            const title = `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;
            const percentile = allUsers.length === 0 ? '-' : ((users.length / allUsers.length) * 100).toFixed(2) + '%';
            return (
              <TouchableOpacity
                activeOpacity={1}
                key={keyIndex++}
                onPress={() => {
                  this.props.navigation.navigate('AttendanceRecord', {
                    groupId: this.group.groupId,
                    date,
                    existingDate: this.existingDate,
                    users: this.getUsersWithDetails(users),
                    allUsers: this.getUsersWithDetails(allUsers),
                    setLoadNeeded: () => {
                      this.loadNeeded = true;
                    }
                  });
                }}
                onLongPress={() => {
                  Alert.alert(i18n2.t('AttendanceHomeScreen.RemoveAttendanceRecord'), undefined, [
                    { text: i18n2.t('Common.Cancel'), style: 'cancel' },
                    {
                      text: i18n2.t('Common.Yes'),
                      onPress: () => {
                        this.deleteRecordAsync(date);
                      }
                    }
                  ]);
                }}>
                <Item size={size} marginHorizontal={marginHorizontal}>
                  <Text
                    style={{
                      fontSize: getCurrentUser().getXSmallFontSize(),
                      color: Colors.yellow
                    }}>
                    {title}
                  </Text>
                  <Text
                    style={{
                      marginTop: 5,
                      fontSize: getCurrentUser().getMediumFontSize(),
                      fontWeight: 'bold'
                    }}>
                    {percentile}
                  </Text>
                </Item>
              </TouchableOpacity>
            );
          })}
          <TouchableOpacity
            activeOpacity={1}
            key={keyIndex++}
            onPress={() => {
              this.props.navigation.navigate('AttendanceRecord', {
                createNew: true,
                groupId: this.group.groupId,
                date: now,
                existingDate: this.existingDate,
                users: [],
                allUsers: this.allUsers,
                setLoadNeeded: () => {
                  this.loadNeeded = true;
                }
              });
            }}>
            <Item size={size} marginHorizontal={marginHorizontal}>
              <Entypo name='plus' color={Colors.darkBlue} size={35} />
            </Item>
          </TouchableOpacity>
        </View>
        <View style={{ height: 80 }} />
      </ScrollView>
    );
  }
}

const Item = (props) => {
  return (
    <View
      style={{
        width: props.size,
        height: props.size,
        borderColor: Colors.darkBlue,
        borderWidth: 0.5,
        borderRadius: 15,
        marginTop: 15,
        marginHorizontal: props.marginHorizontal,
        alignItems: 'center',
        justifyContent: 'center'
      }}>
      {props.children}
    </View>
  );
};
