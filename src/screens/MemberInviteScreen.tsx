import { InviteUser, MemberInvite } from '@/components/MemberInvite/MemberInvite';
import React, { useCallback, useMemo } from 'react';

import { OkButton } from '@/components/navigation/NavigationButtons';
import { fmMergeMembersToAdd } from '@/screens/CreateStudyGroupScreen/createGroup';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { useFocusEffect } from '@react-navigation/native';
import { useMemberInviteState } from '@/components/MemberInvite/useMemberInviteState';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function MemberInviteScreen({ route, navigation }: any) {
  const existingUserIds = useMemo(
    () => (route.params.existingUsers ?? []).map((it: InviteUser) => it.userId),
    [route.params.existingUsers]
  );

  const {
    iDigestUserEnteredMembers,
    iDigestSetUserEnteredMembers,
    fmUserEnteredMembers,
    setFmUserEnteredMembers,
    inviteUsers,
    setInviteUsers
  } = useMemberInviteState();

  useSetNavigationOptions({
    title: i18n2.t('MemberInviteScreen.Title'),
    headerRight: () => (
      <OkButton
        right={true}
        onPress={() => {
          isFishMeet ? fmOnSave() : iDigestOnSave();
        }}
      />
    )
  });

  const loadInviteUsers = useCallback(() => {
    async function getInviteUsers() {
      const inviteUsers = await globalThis.dsObject.getInviteUsers(getCurrentUser().getUserId());
      if (!inviteUsers) {
        navigation.pop();
        return;
      }

      // Remove existing group users
      setInviteUsers(inviteUsers.filter((it) => !existingUserIds.includes(it.userId)));
    }

    getInviteUsers();
  }, [existingUserIds, navigation, setInviteUsers]);

  useFocusEffect(loadInviteUsers);

  async function iDigestOnSave() {
    const emails = iDigestUserEnteredMembers
      .split(',')
      .map((it) => it.trim())
      .filter((item) => item.length > 0);

    for (const email of emails) {
      await globalThis.dsObject.addUserEmail(route.params.groupId, email, route.params.isOrgInvite);
    }

    navigation.pop();
  }

  async function fmOnSave() {
    const members = Object.keys(fmMergeMembersToAdd(fmUserEnteredMembers, inviteUsers));
    for (const id of members) {
      await globalThis.dsObject.addUserEmail(route.params.groupId, id, route.params.isOrgInvite);
    }

    navigation.pop();
  }

  return isFishMeet ? (
    <MemberInvite
      isFishMeet={isFishMeet}
      inviteUsers={inviteUsers}
      userEnteredMembers={fmUserEnteredMembers}
      setInviteUsers={setInviteUsers}
      setUserEnteredMembers={setFmUserEnteredMembers}
    />
  ) : (
    <MemberInvite
      isFishMeet={isFishMeet}
      inviteUsers={inviteUsers}
      userEnteredMembers={iDigestUserEnteredMembers}
      setInviteUsers={setInviteUsers}
      setUserEnteredMembers={iDigestSetUserEnteredMembers}
    />
  );
}

export default MemberInviteScreen;
