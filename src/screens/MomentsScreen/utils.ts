/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';
import { MomentFormTypeEnum, UrlFormDataProps } from '@/screens/CreateMomentScreen/utils';
import { useLoadPageListById } from '@/hooks/useLoadPageListById';
import { useEventListener } from 'expo';

export const MomentScreenContext = React.createContext<{
  momentItemPadding: number;
  avatarMarginRight: number;
  avatarSize: number;
  shareMoment?: (url: string) => void;
  userTags?: Record<string, string> | undefined;
  contentGap: number;
  toShowPreviewImages: (images: string[], index: number) => void;
}>({
  momentItemPadding: 0,
  avatarMarginRight: 0,
  avatarSize: 0,
  contentGap: 0,
  toShowPreviewImages: () => {}
});

export const HolyMomentContext = React.createContext<{
  deleteMoment?: (id: number) => Promise<void>;
  likeMoment?: (id: number) => Promise<void>;
  unlikeMoment?: (id: number) => Promise<void>;
  reloadMoment?: (id: number) => Promise<void>;
}>({});

export interface MomentItemProps extends UrlFormDataProps {
  iClicked: boolean;
  userDisplayName: string;
  userId: number;
  createdAt: number;
  description?: string;
  studyGroupId: number;
  hasNewMessage?: boolean;
  iLiked?: boolean;
  id: number;
  likedCount?: number;
  count: number;
  // article
  type: MomentFormTypeEnum;
  // person
  media?: string[];
}

export const useMomentListener = ({
  refresh,
  scrollToTop
}: {
  refresh: () => Promise<void>;
  scrollToTop: () => void;
}) => {
  useEventListener(globalThis.eventEmitter, 'onMomentCreated', () => {
    refresh().then(() => {
      scrollToTop();
    });
  });
  useEventListener(globalThis.eventEmitter, 'appInBackground', () => {
    scrollToTop();
  });
  useEventListener(globalThis.eventEmitter, 'afterGetUpdate', () => {
    scrollToTop();
  });
};

export const useLoadHomeMoments = () => {
  const [groups, setGroups] = useState<any[]>([]);
  const getMomentsIncludeGroups = async (lastId?: number, filterParams?: Record<string, any>) => {
    const resp = await globalThis.dsObject.getMoments({
      includeGroups: true,
      lastId,
      ...(filterParams || {})
    });
    if (lastId === -1) {
      setGroups(resp?.body?.groups || []);
    }
    return resp?.body?.moments || [];
  };
  const {
    list: moments,
    load,
    setList
  } = useLoadPageListById<any>({
    request: getMomentsIncludeGroups,
    idKey: 'id'
  });
  return {
    holyHomeInfo: { groups, moments },
    resetHomeInfoMoments: setList,
    load
  };
};
