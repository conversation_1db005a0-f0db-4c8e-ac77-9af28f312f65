import React, { useContext } from 'react';
import { MomentScreenContext } from '@/screens/MomentsScreen/utils';
import MoreContent from '@/components/MoreContent';

interface MomentContentProps {
  description: string;
}

const MomentContent: React.FC<MomentContentProps> = ({ description }) => {
  const { avatarSize, avatarMarginRight } = useContext(MomentScreenContext);
  return <MoreContent description={description} lineCount={5} style={{ marginLeft: avatarSize + avatarMarginRight }} />;
};

export default MomentContent;
