import { Dimensions, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Colors } from '@/styles/colors';
import React from 'react';

interface FilterGroupProps {
  value: string[];
  onChange: (value: string[]) => void;
  groups: { id: string; name: string }[];
}

const FilterGroup: React.FC<FilterGroupProps> = ({ value, groups, onChange }) => {
  return (
    <ScrollView horizontal style={styles.container}>
      <View style={styles.groups}>
        {groups?.map((item) => {
          const isSelected = value?.includes(item.id);
          return (
            <TouchableOpacity
              onPress={() => {
                onChange(isSelected ? value?.filter((v) => v !== item.id) : [...value, item.id]);
              }}
              key={item.id}
              style={[styles.groupItem, isSelected && styles.groupItemSelected]}>
              <Text numberOfLines={3} style={[styles.groupText, isSelected && styles.groupSelectedText]}>
                {item.name}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </ScrollView>
  );
};

const widthHeight = (Dimensions.get('window').width - 16 * 2) / 5 - 8;

const styles = StyleSheet.create({
  container: {
    width: Dimensions.get('window').width,
    flexGrow: 0,
    height: widthHeight + 24
  },
  groups: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'row',
    gap: 8
  },
  groupItem: {
    width: widthHeight,
    height: widthHeight,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: widthHeight / 2,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: Colors.buttonBorder,
    backgroundColor: '#fff'
  },
  groupItemSelected: {
    backgroundColor: Colors.buttonBackground
  },
  groupText: {
    fontSize: 12,
    lineHeight: 14,
    color: Colors.darkBlue,
    textAlign: 'center'
  },
  groupSelectedText: {
    color: Colors.answerText
  }
});

export default FilterGroup;
