import React, { useCallback, useRef, useState } from 'react';
import { FlatList } from 'react-native';
import MomentItem from '@/screens/MomentsScreen/components/MomentItem';
import { useMomentListener } from '@/screens/MomentsScreen/utils';

interface HolyMomentContainerProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  moments: any[];
  refresh: () => Promise<void>;
  loadMore: () => Promise<void>;
}

const HolyMomentContainer: React.FC<HolyMomentContainerProps> = ({ moments, refresh, loadMore }) => {
  const [refreshing, setRefreshing] = useState<boolean>(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const momentRef = useRef<any>();
  const scrollToTop = useCallback(() => {
    if (moments?.length && momentRef.current) {
      momentRef.current.scrollToIndex({ viewPosition: 0, index: 0, animated: true });
    }
  }, [moments, momentRef]);
  // listener
  useMomentListener({ scrollToTop, refresh });
  return (
    <FlatList
      ref={momentRef}
      data={moments}
      renderItem={({ item }) => <MomentItem {...item} />}
      keyExtractor={(item, index) => `${item.id}_${index}`}
      onEndReached={loadMore}
      onEndReachedThreshold={1}
      onRefresh={async () => {
        setRefreshing(true);
        await refresh();
        setRefreshing(false);
      }}
      refreshing={refreshing}
    />
  );
};

export default HolyMomentContainer;
