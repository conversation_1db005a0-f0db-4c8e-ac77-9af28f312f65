import React, { useContext, useMemo, useRef } from 'react';
import { TouchableOpacity, useWindowDimensions, View } from 'react-native';
import { Image } from 'expo-image';
import { MomentScreenContext } from '@/screens/MomentsScreen/utils';
import { Colors } from '@/styles/colors';
import { isVideo } from '@/utils/helper';
import { ResizeMode, Video } from 'expo-av';

interface MomentImagesProps {
  images: string[];
}

const gapWidth = 4;

const MomentImages: React.FC<MomentImagesProps> = ({ images }) => {
  const { momentItemPadding, avatarMarginRight, avatarSize, toShowPreviewImages } = useContext(MomentScreenContext);
  const { width: windowWidth } = useWindowDimensions();
  const [containerWidth, itemWidth] = useMemo(() => {
    const count = images.length;
    let contentWidth = windowWidth - avatarSize - 2 * momentItemPadding - avatarMarginRight;
    if (count <= 3) {
      const itemWidth = count === 3 ? (contentWidth - gapWidth * 2) / 3 : (contentWidth - gapWidth) / 2;
      return [contentWidth, itemWidth];
    } else {
      contentWidth = ((contentWidth - 2 * gapWidth) * 2) / 3;
      const itemWidth = (contentWidth - 2 * gapWidth) / 3;
      return [contentWidth, itemWidth];
    }
  }, [images, avatarMarginRight, avatarSize, momentItemPadding, windowWidth]);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const videoRef = useRef<any>(null);

  const handleVideoPress = () => {
    // fullscreen
    if (videoRef.current) {
      videoRef.current.presentFullscreenPlayer();
    }
  };

  return (
    <View
      style={{
        width: containerWidth,
        flexWrap: 'wrap',
        gap: 4,
        marginLeft: avatarMarginRight + avatarSize,
        flexDirection: 'row'
      }}>
      {images.map((uri, index) => {
        return isVideo(uri) ? (
          <TouchableOpacity
            key={`${uri}_${index}`}
            onPress={handleVideoPress}
            style={{
              backgroundColor: Colors.lightBlue
            }}>
            <Video
              ref={videoRef}
              source={{ uri }} // 替换为你的视频URL
              rate={1.0}
              volume={1.0}
              isMuted={false}
              resizeMode={ResizeMode.CONTAIN}
              shouldPlay={false}
              isLooping
              style={{ width: itemWidth, height: itemWidth }}
              useNativeControls
            />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            key={`${uri}_${index}`}
            style={{
              backgroundColor: Colors.lightBlue
            }}
            onPress={() => {
              toShowPreviewImages(images, index);
            }}>
            <Image style={{ width: itemWidth, height: itemWidth }} source={{ uri }} />
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default MomentImages;
