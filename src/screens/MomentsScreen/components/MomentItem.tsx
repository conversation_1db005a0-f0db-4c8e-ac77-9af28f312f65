import { View } from 'react-native';
import PublisherInfo from '@/screens/MomentsScreen/components/PublisherInfo';
import MomentContent from '@/screens/MomentsScreen/components/MomentContent';
import MomentImages from '@/screens/MomentsScreen/components/MomentImages';
import React, { useContext } from 'react';
import { MomentScreenContext, MomentItemProps } from '@/screens/MomentsScreen/utils';
import MomentArticle from '@/screens/MomentsScreen/components/MomentArticle';
import MomentTools from '@/screens/MomentsScreen/components/MomentTools';
import { MomentFormTypeEnum } from '@/screens/CreateMomentScreen/utils';

const MomentItem: React.FC<MomentItemProps> = (props) => {
  const { momentItemPadding, contentGap } = useContext(MomentScreenContext);

  return (
    <View style={{ paddingHorizontal: momentItemPadding, marginTop: 10, gap: contentGap }}>
      <PublisherInfo {...props} />
      {props.title && props.type === MomentFormTypeEnum.PERSON && <MomentContent description={props.title} />}
      {props.description && props.type !== MomentFormTypeEnum.PERSON && (
        <MomentContent description={props.description} />
      )}
      {props.type === MomentFormTypeEnum.PERSON && !!props.media?.length && <MomentImages images={props.media} />}
      {props.type !== MomentFormTypeEnum.PERSON && <MomentArticle item={props} />}
      <MomentTools item={props} />
    </View>
  );
};

export default MomentItem;
