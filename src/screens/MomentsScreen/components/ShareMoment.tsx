import { getImage, ModalPopup } from '@/components';
import { i18n2 } from '@/utils/i18n2';
import { Alert, Share } from 'react-native';
import * as Clipboard from 'expo-clipboard';
import * as Linking from 'expo-linking';
import React, { useContext } from 'react';
import { AppContext } from '@/context/AppContext';
import { useNavigation } from '@react-navigation/native';

interface ShareMomentProps {
  showModal: boolean;
  onHidden: () => void;
  url: string;
}

/* eslint-disable @typescript-eslint/no-explicit-any */
const ShareMoment: React.FC<ShareMomentProps> = ({ showModal, onHidden, url }) => {
  const { insets, groups } = useContext(AppContext) as any;

  const navigation = useNavigation<any>();

  const selectGroup = (onSelected: (group: any) => void) => {
    const studyGroups = groups.filter((studyGroup: any) => studyGroup.status);
    navigation.navigate('Select', {
      choices: studyGroups,
      titleControl: getImage('groupSelect'),
      title: i18n2.t('SelectGroup'),
      getDisplayName: (choice: any) => choice.name,
      // classId: this.classId,
      isCurrent: () => false,
      onSelect: (group: any) => {
        setTimeout(() => {
          onSelected(group);
        }, 0);
      }
    });
  };
  const shareUrlAsync = async (group: any) => {
    if (!group) {
      Alert.alert(i18n2.t('Common.Failed'), i18n2.t('InvalidGroup'));
      return;
    }

    const result = await globalThis.dsObject.sendMessage({
      room: group.groupId,
      message: url
    });

    if (result) {
      setTimeout(() => {
        navigation.navigate('Chat', {
          chatId: group.groupId,
          title: group.name,
          group: group,
          showGroupIcon: true,
          enableAudioChat: true
        });
      }, 1000);
    } else {
      Alert.alert(i18n2.t('Common.Failed'), i18n2.t('MsgSendFailed'));
    }
  };
  const items = [
    {
      renderIcon: () => getImage('groupWithName'),
      text: i18n2.t('ShareToGroup'),
      onPress: () => {
        selectGroup((group) => {
          shareUrlAsync(group);
        });
      }
    },
    {
      renderIcon: () => getImage('export'),
      text: i18n2.t('ShareWeb'),
      onPress: () => {
        setTimeout(() => {
          Share.share({ url });
        }, 600);
      }
    },
    {
      renderIcon: () => getImage('copy'),
      text: i18n2.t('Copy'),
      onPress: () => {
        Clipboard.setStringAsync(url);
      }
    },
    {
      renderIcon: () => getImage('webLink'),
      text: i18n2.t('OpenWithBrowser'),
      onPress: () => {
        Linking.openURL(url);
      }
    }
  ];

  return (
    <ModalPopup
      insets={insets}
      visible={showModal}
      showDivider={true}
      setNotVisible={onHidden}
      items={items}
      iconsPerRow={items.length}
      showCancel={true}
      hideOnPress={true}
    />
  );
};

export default ShareMoment;
