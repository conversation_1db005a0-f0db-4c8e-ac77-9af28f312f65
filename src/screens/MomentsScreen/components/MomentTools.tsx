/* eslint-disable @typescript-eslint/no-explicit-any */
import { Alert, Platform, Text, TouchableOpacity, View } from 'react-native';
import { HolyMomentContext, MomentItemProps } from '@/screens/MomentsScreen/utils';
import React, { useContext, useMemo } from 'react';

import { AntDesign } from '@expo/vector-icons';
import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import { RedDot } from '@/components';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { useNavigation } from '@react-navigation/native';

interface MomentTools {
  item: MomentItemProps;
}

const MomentTools: React.FC<MomentTools> = ({ item }) => {
  const { deleteMomentAsync, likeMomentAsync, unlikeMomentAsync, reloadMomentAsync, setMomentDiscussionRead } =
    useContext(AppContext) as any;
  const { deleteMoment, likeMoment, unlikeMoment, reloadMoment } = useContext(HolyMomentContext);
  const navigation = useNavigation<any>();
  const currentUser = useMemo(getCurrentUser, []);
  const openMomentDiscussion = () => {
    navigation.navigate('Chat', {
      chatId: item.studyGroupId,
      title: i18n2.t('Common.Comment'),
      group: {
        groupId: item.studyGroupId,
        isMomentDiscussionGroup: true,
        isGroupLeader: false
      },
      showGroupIcon: false,
      disableTagging: true,
      disableUserInfo: true,
      disableAtPeople: true,
      onExit: () => {
        // when return from discussion, reload the current moment
        reloadMomentAsync(item.id);
        reloadMoment?.(item.id);
      }
    });
    // set local hasNewMessage = false
    setMomentDiscussionRead(item.id);
  };
  return (
    <View
      style={{
        justifyContent: 'flex-end',
        flexDirection: 'row'
      }}>
      {currentUser.getUserId() === item.userId || getCurrentUser().isAdmin() ? (
        <TouchableOpacity
          activeOpacity={1}
          style={{
            backgroundColor: '#ececec',
            borderColor: '#c0c0c0',
            borderWidth: 1,
            borderRadius: 30,
            alignItems: 'center'
          }}
          onPress={() => {
            Alert.alert(i18n2.t('Common.Confirm'), i18n2.t('Delete'), [
              {
                text: i18n2.t('Common.Yes'),
                onPress: () => {
                  deleteMoment ? deleteMoment?.(item.id) : deleteMomentAsync(item.id);
                }
              },
              {
                text: i18n2.t('Common.No')
              }
            ]);
          }}>
          <View style={{ flex: 1, justifyContent: 'center' }}>
            <Text
              style={{
                color: '#202020',
                marginHorizontal: 2,
                fontSize: 16,
                lineHeight: 20,
                paddingHorizontal: 8
              }}
              numberOfLines={1}>
              {i18n2.t('Delete')}
            </Text>
          </View>
        </TouchableOpacity>
      ) : null}
      <TouchableOpacity
        activeOpacity={1}
        style={{
          marginLeft: 7,
          backgroundColor: '#ececec',
          borderColor: '#c0c0c0',
          borderWidth: 1,
          borderRadius: 30,
          alignItems: 'center',
          display: Platform.OS === 'ios' ? 'none' : 'flex'
        }}
        onPress={() => {
          openMomentDiscussion();
        }}>
        <View style={{ flex: 1, justifyContent: 'center' }}>
          <Text
            style={{
              color: '#202020',
              marginHorizontal: 2,
              fontSize: 16,
              lineHeight: 20,
              paddingHorizontal: 8
            }}
            numberOfLines={1}>
            {i18n2.t('NumComments').replace('{count}', `${item.count}`)}
          </Text>
        </View>
        {item.hasNewMessage ? <RedDot top={-3} right={-3} /> : null}
      </TouchableOpacity>
      <TouchableOpacity
        activeOpacity={1}
        style={{
          marginLeft: 7,
          backgroundColor: item.iLiked ? Colors.darkBlue : '#ececec',
          borderColor: '#c0c0c0',
          borderWidth: 1,
          borderRadius: 30,
          alignItems: 'center'
        }}
        onPress={() => {
          if (item.iLiked) {
            unlikeMoment ? unlikeMoment(item.id) : unlikeMomentAsync(item.id);
          } else {
            likeMoment ? likeMoment(item.id) : likeMomentAsync(item.id);
          }
        }}>
        <View style={{ flex: 1, alignItems: 'center', flexDirection: 'row', paddingHorizontal: 9 }}>
          <AntDesign
            name='like2'
            size={getCurrentUser().getSmallFontSize()}
            color={item.iLiked ? Colors.lightBlue : Colors.darkBlue}
          />
          <Text
            style={{
              color: item.iLiked ? Colors.lightBlue : '#202020',
              fontSize: 16,
              marginLeft: 3,
              lineHeight: 20
            }}
            numberOfLines={1}>
            {item.likedCount || 0}
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default MomentTools;
