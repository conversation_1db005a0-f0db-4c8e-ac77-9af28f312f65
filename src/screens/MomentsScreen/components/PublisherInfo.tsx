import { MomentItemProps, MomentScreenContext } from '@/screens/MomentsScreen/utils';
import React, { useContext, useMemo } from 'react';
import { StyleProp, StyleSheet, Text, View, ViewStyle } from 'react-native';

import { Avatar2 } from '@/components/Avatar2';
import { Colors } from '@/styles/colors';
import { getCurrentUser } from '@/utils/user';

interface PublisherInfoProps extends MomentItemProps {
  style?: StyleProp<ViewStyle>;
}
const PublisherInfo: React.FC<PublisherInfoProps> = ({ userId, userDisplayName, createdAt, style }) => {
  const { userTags, avatarSize, avatarMarginRight } = useContext(MomentScreenContext);
  const userTag = userTags?.[userId];
  const { xSmallFontSize, locale, fontSize } = useMemo(() => {
    const current = getCurrentUser();
    return {
      xSmallFontSize: current.getXSmallFontSize(),
      fontSize: current.getSmallFontSize(),
      locale: current.getLocale()
    };
  }, []);
  const timeFormat = useMemo(() => {
    return `${new Date(createdAt).toLocaleString(locale, {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })} ${new Date(createdAt).toLocaleTimeString(locale)}`;
  }, [locale, createdAt]);
  return (
    <View style={style ? [styles.publisher, style] : styles.publisher}>
      <Avatar2 size={avatarSize} userId={userId} />
      <View style={[styles.otherInfo, { marginLeft: avatarMarginRight }]}>
        <Text style={{ fontSize, color: Colors.text, lineHeight: 20 }}>
          {userDisplayName} {!!userTag && `(${userTag})`}
        </Text>

        <Text
          style={{
            color: '#b2b2b2',
            fontSize: xSmallFontSize,
            fontWeight: '600',
            lineHeight: 20
          }}>
          {timeFormat}
        </Text>
      </View>
    </View>
  );
};

export default PublisherInfo;

const styles = StyleSheet.create({
  publisher: {
    flexDirection: 'row'
  },
  otherInfo: {
    flex: 1
  }
});
