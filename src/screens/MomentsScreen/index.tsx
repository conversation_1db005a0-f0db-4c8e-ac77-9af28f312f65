/* eslint-disable @typescript-eslint/no-explicit-any */
import { i18n2 } from '@/utils/i18n2';
import { NavBarButton } from '@/components/navigation/NavigationButtons';
import React, { useCallback, useContext, useState } from 'react';
import HolyMomentContainer from '@/screens/MomentsScreen/components/HolyMomentContainer';
import { HolyMomentContext, MomentScreenContext, useLoadHomeMoments } from '@/screens/MomentsScreen/utils';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { AppContext } from '@/context/AppContext';
import { View } from 'react-native';
import ShareMoment from '@/screens/MomentsScreen/components/ShareMoment';
import FilterGroup from '@/screens/MomentsScreen/components/FilterGroup';
import { LoadingIndicator } from '@/components';
import ImagePreview from '@/components/ImagePreview';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';
import NavTabs from '@/components/navigation/NavTabs';

const MomentScreen = () => {
  const navigation = useNavigation<any>();
  const [page, setPage] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const { setSeenMomentAsync, moments, loadMomentsAsync, userTags } = useContext(AppContext) as any;
  const [previewImages, setPreviewImages] = useState<string[]>();
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(-1);
  const [shareUrl, setShareUrl] = useState<string>('');
  const [selectGroups, setSelectGroups] = useState<string[]>([]);
  const { holyHomeInfo, load: getMomentsIncludeGroups, resetHomeInfoMoments } = useLoadHomeMoments();

  const focusCallback = useCallback(() => {
    setSeenMomentAsync();
  }, [setSeenMomentAsync]);

  useFocusEffect(focusCallback);

  const refreshPage = useCallback(
    async (p: number, loadingVisible?: boolean, groupId?: string) => {
      try {
        if (loadingVisible) {
          setLoading(true);
        }
        if (p === 1) {
          return await getMomentsIncludeGroups(true, {
            groupId: typeof groupId === 'string' ? groupId : selectGroups?.join(',')
          });
        } else {
          return await loadMomentsAsync?.(true);
        }
      } finally {
        if (loadingVisible) {
          setLoading(false);
        }
      }
    },
    [loadMomentsAsync, selectGroups, getMomentsIncludeGroups]
  );
  const deleteHomeMoments = async (id: number) => {
    const mList = holyHomeInfo.moments;
    const index = mList?.findIndex((item) => item.id === id);
    if (index > -1) {
      const result = await globalThis.dsObject.deleteMoment(id);
      if (result) {
        resetHomeInfoMoments(mList.filter((_, i) => i !== index));
      }
    }
  };
  const likeHomeMoments = async (id: number) => {
    const mList = holyHomeInfo.moments;
    const index = mList?.findIndex((item) => item.id === id);
    if (index > -1) {
      const result = await globalThis.dsObject.likeMoment(id);
      if (result) {
        mList[index].iLiked = true;
        mList[index].likedCount += 1;
        resetHomeInfoMoments([...mList]);
      }
    }
  };
  const unlikeHomeMoments = async (id: number) => {
    const mList = holyHomeInfo.moments;
    const index = mList?.findIndex((item) => item.id === id);
    if (index > -1) {
      const result = await globalThis.dsObject.unlikeMoment(id);
      if (result) {
        mList[index].iLiked = false;
        mList[index].likedCount -= 1;
        resetHomeInfoMoments([...mList]);
      }
    }
  };
  const reloadHomeMoments = async (id: number) => {
    const result = await globalThis.dsObject.loadMoment(id);
    if (!result || result.body.length !== 1) {
      return;
    }
    const mList = holyHomeInfo.moments;
    const index = mList?.findIndex((item) => item.id === id);
    if (index > -1) {
      mList[index] = result.body[0];
      resetHomeInfoMoments([...mList]);
    }
  };

  useSetNavigationOptions({
    headerTitle: () => (
      <NavTabs
        current={page}
        onChange={(p) => {
          setPage(p);
          refreshPage(p, true);
        }}
        options={[{ title: i18n2.t('MomentsUI.Title') }, { title: i18n2.t('MomentsUI.HomeTitle') }]}
      />
    ),
    headerRight: () => (
      <NavBarButton
        image='plus'
        onPress={() => {
          navigation.navigate('CreateMoment');
        }}
      />
    )
  });
  return (
    <MomentScreenContext.Provider
      value={{
        userTags,
        momentItemPadding: 10,
        avatarMarginRight: 10,
        avatarSize: 40,
        contentGap: 6,
        toShowPreviewImages: (images, index) => {
          setPreviewImages(images);
          setCurrentImageIndex(index);
        },
        shareMoment: setShareUrl
      }}>
      {page === 0 && (
        <HolyMomentContainer
          moments={moments}
          refresh={() => refreshPage(0, false)}
          loadMore={() => loadMomentsAsync(false)}
        />
      )}
      {page === 1 && (
        <View style={{ flex: 1 }}>
          <FilterGroup
            groups={holyHomeInfo.groups}
            value={selectGroups}
            onChange={(list) => {
              setSelectGroups(list);
              refreshPage(1, true, list?.join(','));
            }}
          />
          <HolyMomentContext.Provider
            value={{
              deleteMoment: deleteHomeMoments,
              likeMoment: likeHomeMoments,
              unlikeMoment: unlikeHomeMoments,
              reloadMoment: reloadHomeMoments
            }}>
            <HolyMomentContainer
              moments={holyHomeInfo.moments}
              refresh={() => refreshPage(1, false)}
              loadMore={() => getMomentsIncludeGroups(false, { groupId: selectGroups?.join(',') })}
            />
          </HolyMomentContext.Provider>
        </View>
      )}
      {loading && <LoadingIndicator />}
      <ShareMoment
        showModal={!!shareUrl}
        url={shareUrl}
        onHidden={() => {
          setShareUrl('');
        }}
      />
      <ImagePreview
        ableSave
        imageIndex={currentImageIndex}
        images={
          previewImages?.map((uri) => {
            return { uri };
          }) || []
        }
        onRequestClose={() => {
          setPreviewImages([]);
          setCurrentImageIndex(-1);
        }}
      />
    </MomentScreenContext.Provider>
  );
};

export default MomentScreen;
