import * as Clipboard from 'expo-clipboard';
import { Button } from '@/components';
import { ActivityIndicator, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useCallback, useState } from 'react';
import { ResizeMode, Video } from 'expo-av';

import { Dialog } from '@rneui/themed';
import { getImage } from '@/components';
import { i18n2 } from '@/utils/i18n2';
import { useFocusEffect } from '@react-navigation/native';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';
import { useNavigation } from '@react-navigation/native';

const url = 'https://idigest.app/#/develop';

const HelpMeScreen = () => {
  const [dialogVisible, setDialogVisible] = useState<boolean>(false);
  const [videoValue, setVideoValue] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);

  useSetNavigationOptions({
    title: i18n2.t('HomeScreen.Create')
  });
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const navigation = useNavigation<any>();

  const toCopy = async () => {
    try {
      await Clipboard.setStringAsync(url);
      setDialogVisible(true); // Show the dialog
      setTimeout(() => {
        setDialogVisible(false); // Hide the dialog after 1.5 seconds
      }, 1500);
    } catch (e) {
      console.log('e', e);
    }
  };

  const loadVideo = useCallback(() => {
    async function getVideo() {
      try {
        const result = await globalThis.dsObject.getClassCreateGuidePreview();
        if (!result) {
          // continue render old page
          return;
        }
        const videoUrl = result?.body?.content?.sessions?.[0]?.content?.find(
          (item: Record<string, string>) => item?.['type'] === 'video'
        )?.value;
        setVideoValue(videoUrl);
      } catch (e) {
        console.log('e', e);
      }
    }

    getVideo();
  }, []);

  useFocusEffect(loadVideo);

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.sectionTitle}>{i18n2.t('HelpMeScreen.HowToGetHelp')}</Text>
      <Text style={styles.topTip}>{i18n2.t('HelpMeScreen.GetHelpInstruction')}:</Text>
      <Button
        title={i18n2.t('HelpMeScreen.Contactsupport')}
        onPress={() => {
          navigation.navigate('SendFeedback');
        }}
        style={styles.contactButton}
      />

      <Text style={styles.sectionTitle}>{i18n2.t('HelpMeScreen.HowToClassCreate')}</Text>
      <View style={styles.topTip}>
        <Text style={styles.topTip}>{i18n2.t('HelpMeScreen.websiteTitle')}</Text>
        <TouchableOpacity style={styles.linkContainer} onPress={toCopy}>
          <Text style={styles.link}>{url}</Text>
          {getImage('copy')}
        </TouchableOpacity>
        <Text style={styles.topTip}>{i18n2.t('HelpMeScreen.openOnComputer')}</Text>
      </View>
      <Text style={styles.subtitle}>{i18n2.t('HelpMeScreen.videoDescription')}</Text>

      {videoValue && (
        <View style={styles.videoContainer}>
          {loading && (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size='large' color='#ffffff' />
            </View>
          )}
          <Video
            onLoad={() => {
              setLoading(false);
            }}
            shouldPlay={false}
            isLooping={false}
            source={{ uri: videoValue }}
            style={styles.video}
            useNativeControls
            resizeMode={ResizeMode.CONTAIN}
            isMuted={false}
          />
        </View>
      )}
      <Dialog isVisible={dialogVisible} onDismiss={() => setDialogVisible(false)} overlayStyle={styles.dialog}>
        <Text>{i18n2.t('HelpMeScreen.copySuccess')}</Text>
      </Dialog>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  sectionTitle: {
    textAlign: 'center',
    fontSize: 20,
    lineHeight: 24,
    color: '#202020',
    marginBottom: 15
  },
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
    alignItems: 'center'
  },
  topTip: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    fontSize: 16
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  link: {
    color: '#2f95dc',
    textDecorationLine: 'underline',
    marginRight: 8
  },
  subtitle: {
    color: '#626262',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
    marginTop: 16
  },
  videoContainer: {
    width: '100%',
    height: 240,
    backgroundColor: '#000'
  },
  loaderContainer: {
    width: '100%',
    height: 240,
    zIndex: 2,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center'
  },
  video: {
    width: '100%',
    height: 240
  },
  dialog: {
    width: 200,
    height: 60,
    borderRadius: 10,
    alignItems: 'center'
  },
  contactButton: {
    marginTop: 15,
    marginBottom: 60
  }
});

export default HelpMeScreen;
