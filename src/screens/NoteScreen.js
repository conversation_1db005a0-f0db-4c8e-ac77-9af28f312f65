import { Keyboard, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { OkButton } from '@/components/navigation/NavigationButtons';
import React from 'react';
import { WebView } from 'react-native-webview';
import { i18n2 } from '@/utils/i18n2';
import { logEvent } from '@/utils/logger';
import { notesHtml } from '@/assets/html/notes';
import { updateHtmlFontSize } from '@/utils/helper';

export default class NoteScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = () => ({
    title: i18n2.t('NoteScreen.Title'),
    headerLeft: () => (
      <OkButton
        onPress={() => {
          globalThis.eventEmitter.emit('NoteSaveAndGoBack');
        }}
      />
    )
  });

  state = {
    keyboardHeight: 0,
    keyboard: false
  };

  constructor(props) {
    super(props);

    this.classId = this.props.route.params.classId;
    this.dataId = `notes,${this.classId}`;
    this.html = notesHtml;
    this.notesChanged = false;
    this.notes = '';

    this.listeners = [];
    this.listeners.push(
      globalThis.eventEmitter.addListener('NoteSaveAndGoBack', () => {
        this.saveAndGoBackAsync();
      })
    );
  }

  componentDidMount() {
    logEvent('showPersonalNote');

    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (event) => {
      this.setState({
        keyboard: true,
        keyboardHeight: event.endCoordinates.height
      });
    });
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', (event) => {
      this.setState({ keyboard: false, keyboardHeight: 0 });
    });

    this.loadAsync();
  }

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
    this.listeners.forEach((listener) => {
      listener.remove();
    });
  }

  async loadAsync() {
    const result = await globalThis.dsObject.getUserData(this.dataId);
    if (!result) {
      this.props.navigation.pop();
      return;
    }

    if (result.status === 200) {
      this.notes = result.body.content;

      // convert non-html to html
      if (!this.notes.startsWith('<')) {
        this.notes = this.notes.replace(/\n/g, '<br>');
      }
      console.log(this.notes);

      this.forceUpdate();
    }
  }

  saveAndGoBackAsync = async () => {
    if (this.notesChanged) {
      const body = {
        content: this.notes
      };
      const result = await globalThis.dsObject.setUserData(this.dataId, body);
      if (!result) {
        return;
      }
    }

    this.props.navigation.pop();
  };

  render() {
    const html = updateHtmlFontSize(this.html);
    return (
      <View
        style={{
          flex: 1,
          marginLeft: this.context.insets.left,
          marginRight: this.context.insets.right
        }}>
        <WebView
          ref={(r) => (this.webView = r)}
          style={{ flex: 1, marginBottom: this.context.insets.bottom }}
          allowsLinkPreview={true}
          originWhitelist={['*']}
          source={{ html: `<script>content=${JSON.stringify({ html: this.notes })}</script>${html}` }}
          scrollEnabled={true}
          allowUniversalAccessFromFileURLs={true}
          allowFileAccessFromFileURLs={true}
          allowFileAccess={true}
          mixedContentMode='always'
          bounces={false}
          showsVerticalScrollIndicator={true}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          allowsFullscreenVideo={true}
          onMessage={async (evt) => {
            const payload = JSON.parse(evt.nativeEvent.data);
            switch (payload.command) {
              case 'setNotes':
                console.log('setNotes:' + payload.data.html);
                this.notesChanged = true;
                this.notes = payload.data.html;
                break;
              default:
                break;
            }
          }}
        />
      </View>
    );
  }
}
