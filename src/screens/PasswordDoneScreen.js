import { Text, View } from 'react-native';

import { Colors } from '@/styles/colors';
import { ExplanationHeader } from '@/fishMeet/components/ExplanationHeader';
import { FmStyles } from '@/fishMeet/styles/fmStyles';
import { OkButton } from '@/components/navigation/NavigationButtons';
import React from 'react';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';

export default class PasswordDoneScreen extends React.Component {
  static navigationOptions = ({ navigation }) => ({
    title: i18n2.t('RestorePwd'),
    headerLeft: () => (
      <OkButton
        onPress={() => {
          navigation.popToTop();
        }}
      />
    )
  });

  render() {
    return isFishMeet ? (
      <View style={FmStyles.screenContainer}>
        <ExplanationHeader
          titleText={i18n2.t('PasswordDoneScreen.HeaderTitle')}
          explanationText={i18n2.t('PasswordDoneScreen.HeaderExplanation')}
        />
      </View>
    ) : (
      <Text
        style={{
          marginTop: 40,
          marginHorizontal: 20,
          color: Colors.darkBlue,
          fontSize: 18
        }}>
        {i18n2.t('PasswordDoneScreen.HeaderTitle')}
        {'\n\n'}
        {i18n2.t('PasswordDoneScreen.HeaderExplanation')}
      </Text>
    );
  }
}
