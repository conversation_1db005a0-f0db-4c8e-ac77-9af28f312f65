import { Alert, Dimensions, Keyboard, StyleSheet, Text, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { Button } from '@/components';
import { Colors } from '@/styles/colors';
import React from 'react';
import { WebView } from 'react-native-webview';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { notesHtml } from '@/assets/html/notes';
import { updateHtmlFontSize } from '@/utils/helper';

export default class SendFeedbackScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = () => ({
    title: i18n2.t('SendFeedbackScreen.Title')
  });

  state = {
    message: '',
    keyboardHeight: 0,
    keyboard: false
  };

  constructor(props) {
    super(props);

    this.html = notesHtml;
    this.message = '';
    this.listeners = [];
  }

  componentDidMount() {
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (event) => {
      this.setState({
        keyboard: true,
        keyboardHeight: event.endCoordinates.height
      });
    });
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', (event) => {
      this.setState({ keyboard: false, keyboardHeight: 0 });
    });
  }

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();

    this.listeners.forEach((listener) => {
      listener.remove();
    });
  }

  onSave = async () => {
    if (this.message.trim().length === 0) {
      this.props.navigation.pop();
      return;
    }

    const body = { message: this.message };
    const result = await globalThis.dsObject.postFeedback(body);
    if (!result) {
      return;
    }

    Alert.alert('', i18n2.t('SendFeedbackScreen.Success'));
    this.props.navigation.pop();
  };

  render() {
    const html = updateHtmlFontSize(this.html);
    return (
      <View
        style={{
          height: Dimensions.get('window').height * 0.5,
          marginLeft: this.context.insets.left,
          marginRight: this.context.insets.right,
          marginBottom: this.context.insets.bottom
        }}>
        <Text
          style={{
            fontSize: getCurrentUser().getMediumFontSize(),
            margin: 10,
            color: Colors.darkBlue
          }}>
          {i18n2.t('SendFeedbackScreen.ContactUs')}
        </Text>
        <WebView
          ref={(r) => (this.webView = r)}
          style={{ flex: 1 }}
          allowsLinkPreview={true}
          originWhitelist={['*']}
          source={{ html: `<script>content=${JSON.stringify({ html: this.message })}</script>${html}` }}
          scrollEnabled={true}
          allowUniversalAccessFromFileURLs={true}
          allowFileAccessFromFileURLs={true}
          allowFileAccess={true}
          mixedContentMode='always'
          bounces={false}
          showsVerticalScrollIndicator={true}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          allowsFullscreenVideo={true}
          onMessage={async (evt) => {
            const payload = JSON.parse(evt.nativeEvent.data);
            switch (payload.command) {
              case 'setNotes':
                console.log('setNotes:' + payload.data.html);
                this.message = payload.data.html;
                break;
              default:
                break;
            }
          }}
        />
        <View style={styles.submitButtonContainer}>
          <Button title={i18n2.t('SendFeedbackScreen.Submit')} onPress={this.onSave} style={styles.submitButton} />
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  submitButtonContainer: {
    alignItems: 'center'
  },
  submitButton: {
    marginTop: 20
  }
});
