import { Button, getImage } from '@/components';
import { ScrollView, StyleSheet, Text, TextInput, View } from 'react-native';

import { Colors } from '@/styles/colors';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { useNavigation } from '@react-navigation/native';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';
import { useState } from 'react';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const AcceptContactCardScreen = ({ route }: any) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const navigation = useNavigation<any>();
  useSetNavigationOptions({
    title: i18n2.t('AcceptContactCardScreen.Title')
  });

  const [greetings, setGreetings] = useState('');

  const { name, uniqueId } = route.params;
  const fontSize = getCurrentUser().getMediumFontSize();
  const lineHeight = 1.3 * fontSize;

  const connectWithContact = async () => {
    const inviteResult = await globalThis.dsObject.inviteUser(uniqueId, greetings);
    if (!inviteResult) {
      return;
    }

    // create one-on-one group
    const createGroupResult = await globalThis.dsObject.createOneOnOne(inviteResult.body.userId);
    if (!createGroupResult) {
      return;
    }

    // on friend added, send greetings if provided
    if (greetings) {
      globalThis.dsObject.sendMessage({
        room: createGroupResult.body.studyGroupId,
        message: greetings
      });
    }

    navigation.pop();
  };

  const leftStyle = [styles.left, { fontSize, lineHeight }];
  const rightStyle = [styles.right, { fontSize, lineHeight }];

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={{ margin: 10 }}>{getImage('defaultAvatar', { width: 150, height: 150 })}</View>
      <View style={styles.contentContainer}>
        <View style={styles.row}>
          <Text style={leftStyle}>{i18n2.t('MemberScreen.ID')}:</Text>
          <Text style={{ fontSize }}>{uniqueId}</Text>
        </View>
        <View style={styles.row}>
          <Text style={leftStyle}>{i18n2.t('DisplayName')}:</Text>
          <Text style={rightStyle}>{name}</Text>
        </View>
      </View>
      <View>
        <Text
          style={{
            fontSize: getCurrentUser().getSmallFontSize(),
            color: Colors.darkBlue,
            textAlign: 'left',
            margin: 20
          }}>
          {i18n2.t('AcceptContactCardScreen.GreetingInput')}
        </Text>

        <TextInput
          style={{
            borderColor: '#cccccc',
            fontSize: getCurrentUser().getSmallFontSize(),
            marginHorizontal: 20,
            paddingHorizontal: 20,
            borderWidth: 1,
            textAlignVertical: 'top',
            height: 90
          }}
          autoCapitalize='none'
          multiline={true}
          value={greetings}
          onChangeText={(text) => {
            setGreetings(text.trim());
          }}
        />
      </View>
      <Button
        title={i18n2.t('AcceptContactCardScreen.Connect')}
        onPress={connectWithContact}
        style={styles.chatButton}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center'
  },
  contentContainer: {
    width: '100%',
    alignItems: 'center'
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginHorizontal: 10,
    marginTop: 10
  },
  left: {
    width: '50%',
    textAlign: 'right',
    right: 5
  },
  right: {
    width: '50%',
    left: 5
  },
  chatButton: {
    marginTop: 20
  }
});

export default AcceptContactCardScreen;
