import * as FileSystem from 'expo-file-system';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';

import { ScrollView, StyleSheet, Text, View } from 'react-native';

import { Button } from '@/components';
import { CheckBox } from '@rneui/themed';
import { Colors } from '@/styles/colors';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { updateHtmlFontSize } from '@/utils/helper';

export default class ExportScreen extends React.Component {
  static navigationOptions = ({ route }) => ({
    title: route.params.title ?? i18n2.t('ExportScreen.Title')
  });

  constructor(props) {
    super(props);

    const { params } = props.route;
    const title = `${params.className} - ${params.weekName}`;
    this.printTitle = `iDigest ${i18n2.t('ExportScreen.Lesson')}：${title}`;
    this.answers = params.answers;
    this.content = params.content;
    this.printMargin = {
      left: 20,
      top: 20,
      right: 20,
      bottom: 20
    };

    this.state = {
      includeAnswers: true,
      format: 0
    };

    this.props.navigation.setParams({ title });
  }

  getHtml = ({ fontSize = getCurrentUser().getMediumFontSize(), includeAnswers = true }) => {
    let html = `
      <style>
      body {
        margin: 1.5em;
      }
      * {
        font-size: 18px;
        font-family: -apple-system, Segoe UI, Helvetica, Arial, sans-serif;
        color: #505050;
      }
      .question {
        background-color: #ecf0f1;
        border-radius: 7px;
        border: 1px solid #626262;
        padding: 0.5em;
        min-height: 3em;
      }
      .button {
        padding-left: 40px;
        padding-right: 40px;
        padding-top: 3px;
        padding-bottom: 3px;
        color: #202020;
        background-color: #ececec;
        border-radius: 32px;
        font-size: 18px;
        border: 2px solid #c0c0c0;
      }
      </style>
      <h3>${this.printTitle}</h3>`;

    this.content.forEach((day) => {
      html += '<p>';
      day.session.content.forEach((item) => {
        switch (item.type.toLowerCase()) {
          case 'title':
            html += `<p><b>${item.value}</b></p>`;
            break;
          case 'subtitle':
            html += `<p><b>${item.value}</b></p>`;
            break;
          case 'text':
            html += `<p>${item.value}</p>`;
            break;
          case 'question':
            {
              if (item.title) {
                html += `<p>${item.title}`;
              }
              const answer = includeAnswers ? this.answers[item.value] || '' : '';
              html += `<div class='question'>${answer}&nbsp</div>`;
              if (item.title) {
                html += '</p>';
              }
            }
            break;
          case 'link':
            html += `<p><button class='button'>${item.value}</button></p>`;
            break;
          case 'audio':
          case 'video':
            html += `<p>[${i18n2.t('Common.File')}]</p>`;
            break;
        }
      });
      html += '</p>';
    });

    html = updateHtmlFontSize(html);
    return html;
  };

  exportPDF = async () => {
    const { includeAnswers } = this.state;
    const html = this.getHtml({
      fontSize: getCurrentUser().getMediumFontSize(),
      includeAnswers
    });

    const { uri } = await Print.printToFileAsync({ html, margins: this.printMargin });
    const filename = encodeURIComponent(this.printTitle);
    const newUri = `${FileSystem.cacheDirectory}${filename}.pdf`;
    await FileSystem.copyAsync({ from: uri, to: newUri });
    await Sharing.shareAsync(newUri, { UTI: '.pdf', mimeType: 'application/pdf' });
  };

  printLesson = async () => {
    const { includeAnswers } = this.state;
    const html = this.getHtml({
      fontSize: getCurrentUser().getMediumFontSize(),
      includeAnswers
    });

    try {
      await Print.printAsync({ html, margins: this.printMargin });
    } catch (error) {
      // iOS throws exception when printing isn't completed (e.g. user cancels)
      console.log(error);
    }
  };

  render() {
    const { includeAnswers } = this.state;
    const fontSize = getCurrentUser().getMediumFontSize();
    return (
      <ScrollView>
        <View style={styles.container}>
          <Text
            style={{
              fontSize,
              color: Colors.darkBlue,
              textAlign: 'center',
              marginVertical: 30
            }}>
            {i18n2.t('ExportScreen.ContentExport')}
          </Text>
          <CheckBox
            textStyle={{ fontSize }}
            checkedColor={Colors.darkBlue}
            title={i18n2.t('ExportScreen.IncludeAnswers')}
            checked={includeAnswers}
            onPress={() => {
              this.setState({ includeAnswers: !includeAnswers });
            }}
          />

          <View style={{ flexDirection: 'row', marginTop: 80 }}>
            <Button
              style={{ width: 150, margin: 7 }}
              title={i18n2.t('ExportScreen.Print')}
              onPress={() => {
                this.printLesson();
              }}
            />

            <Button
              style={{ width: 150, margin: 7 }}
              title={i18n2.t('ExportScreen.ExportPDF')}
              onPress={() => {
                this.exportPDF();
              }}
            />
          </View>
        </View>
      </ScrollView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center'
  }
});
