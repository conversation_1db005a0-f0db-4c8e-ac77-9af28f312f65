import * as Clipboard from 'expo-clipboard';
import * as WebBrowser from 'expo-web-browser';

import { Alert, Dimensions, Share, Text, TouchableOpacity, View } from 'react-native';
import { BackButton, NavBarButton } from '@/components/navigation/NavigationButtons';
import { Button, KeyboardView, ModalPopup, getImage } from '@/components';
import { Styles, getInputContainerStyle } from '@/styles/styles';
import { cloneDeep, debounce } from 'lodash-es';
import { getDisappearingMessagesChoicesName, getDisappearingMessagesText } from '@/utils/disappearingMessages';

import { AppContext } from '@/context/AppContext';
import { Avatar2 } from '@/components/Avatar2';
import { BorderlessButton } from 'react-native-gesture-handler';
import { Card } from '@/components/Card';
import { ClassCover } from '@/components/ClassCover';
import { Colors } from '@/styles/colors';
import { Entypo } from '@expo/vector-icons';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { FmText } from '@/fishMeet/components/FmText';
import GroupSettingTitle from '@/components/chat/GroupSettingTitle';
import { Image } from 'expo-image';
import { Input } from '@rneui/themed';
import { InputField } from '@/fishMeet/components/InputField';
import { LoadingIndicator } from '@/components/LoadingIndicator';
import ParsedText from 'react-native-parsed-text';
import QR from 'qrcode-base64';
import React from 'react';
import SettingsList from '@/components/SettingsList';
import { getCurrentUser } from '@/utils/user';
import { getFmGroupScreenStyles } from '@/fishMeet/styles/screens/groupScreenStyle';
import { goToStudyProgressAsync } from '@/utils/goToScreen';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { launchMeetingAsync } from '@/utils/meeting';
import { showMessage } from '@/components/MessageBox';
import { sortByPinYin } from '@/utils/pinyin';

export default class GroupScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ navigation, route }) => {
    const { fromCreateGroupScreen, lesson, group } = route.params;
    let goBack = false;
    let toLessonScreen = false;
    if (fromCreateGroupScreen) {
      toLessonScreen = group.classId > 0 && lesson !== null;
    } else {
      goBack = true;
    }

    return {
      gestureEnabled: goBack ? true : false,
      title: i18n2.t('Common.Management'),
      headerLeft: () => (goBack ? <BackButton /> : <></>),
      headerRight: () =>
        goBack ? (
          <></>
        ) : (
          <NavBarButton
            image='close'
            right={true}
            onPress={() => {
              if (toLessonScreen) {
                navigation.navigate('LessonTableOfContent', { lesson: route.params.lesson });
              } else {
                navigation.popToTop();
              }
            }}
          />
        )
    };
  };

  constructor(props) {
    super(props);

    this.userId = getCurrentUser().getUserId();
    this.loginId = getCurrentUser().getLoginId();
    this.group = props.route.params.group;
    this.title = props.route.params.title;
    this.isOrgGroup = props.route.params.isOrgGroup;
    this.isGroupLeader = !!this.group.isGroupLeader;
    this.classId = this.group.classId || props.route.params.classId;
    this.state = {
      name: '',
      notice: '',
      users: [],
      tags: [],
      classes: [],
      showModal: false,
      showModalTitle: '',
      showModalItems: [],
      joinLink: '',
      joinCode: '',
      memberExpanded: false,
      disappearingMessages: 0,
      hideMessages: 0,
      muteMsgNotification: 0,
      onlyLeaderCanPost: this.group.onlyLeaderCanPost ? 1 : 0,
      disableMemberShowList: this.group.disableMemberShowList ? 1 : 0,
      meetingLink: '',
      meetingPassword: '',
      busy: true,
      joinRequireLeaderApproval: 0
    };
  }

  componentDidMount() {
    if (!this.group || !this.group.groupId) {
      this.props.navigation.pop();
      return;
    }

    this.props.navigation.addListener('focus', () => {
      this.loadAsync();
      this.context.loadTagsAsync();
    });
  }

  loadAsync = async () => {
    if (this.state.users.length === 0) {
      // only show busy when users is empty
      this.setState({ busy: true });
    }

    const result = await globalThis.dsObject.getGroup(this.group.groupId);
    console.log('result', JSON.stringify(result));
    if (!result) {
      this.props.navigate.pop();
      return;
    }

    this.setState({ busy: false });

    const users = sortByPinYin(result.body.users, (item) => (item.isGroupLeader ? '0' : '1') + item.name);
    const { joinLink, joinCode } = result.body;
    const qrCode = joinLink
      ? QR.drawImg(result.body.joinLink, {
          typeNumber: 4,
          errorCorrectLevel: 'M',
          size: 300
        })
      : '';

    this.setState({
      users,
      name: result.body.name || '',
      notice: result.body.notice || '',
      tags: result.body.tags,
      classes: Array.isArray(result.body.classes) ? result.body.classes : [],
      joinLink,
      joinCode: joinCode ?? '',
      qrCode,
      meetingLink: result.body.meetingLink,
      meetingPassword: result.body.meetingPassword,
      disappearingMessages: result.body.disappearingMessages,
      hideMessages: result.body.hideMessages,
      muteMsgNotification: !result.body.muteMsgNotification ? 0 : 1,
      onlyLeaderCanPost: !result.body.onlyLeaderCanPost ? 0 : 1,
      disableMemberShowList: !result.body.disableMemberShowList ? 0 : 1,
      joinRequireLeaderApproval: !result.body.joinRequireLeaderApproval ? 0 : 1,
      busy: false
    });

    this.props.navigation.setParams({ title: result.body.name });
  };

  async updateGroupInfoAsync({
    name,
    notice,
    disappearingMessages,
    meetingPassword,
    muteMsgNotification,
    joinRequireLeaderApproval,
    disableMemberShowList,
    onlyLeaderCanPost
  }) {
    const result = await globalThis.dsObject.updateGroup(this.group.groupId, {
      name: name.trim(),
      notice: notice.trim(),
      disappearingMessages,
      meetingPassword,
      muteMsgNotification,
      joinRequireLeaderApproval,
      disableMemberShowList,
      onlyLeaderCanPost
    });
    if (result) {
      this.setState({
        name: name.trim(),
        notice: notice.trim(),
        disappearingMessages,
        meetingPassword,
        muteMsgNotification,
        joinRequireLeaderApproval,
        disableMemberShowList,
        onlyLeaderCanPost
      });
      globalThis.eventEmitter.emit('currentGroupUpdated', {
        groupId: this.group.groupId,
        title: name,
        notice,
        disappearingMessages,
        muteMsgNotification
      });
    }

    return result ? true : false;
  }

  updateUserHideMessagesAsync = async (hideMessages) => {
    const result = await globalThis.dsObject.userHideMessages(this.group.groupId, hideMessages);
    if (!result) {
      return;
    }

    this.setState({ hideMessages });
    globalThis.eventEmitter.emit('currentGroupUpdated', { groupId: this.group.groupId, hideMessages });
  };

  async updateGroupNoticeAsync() {
    const succeed = await this.updateGroupInfoAsync(this.state);
    if (!succeed) {
      return false;
    }

    const message = this.state.notice.trim();
    if (message) {
      await globalThis.dsObject.sendMessage({
        room: this.group.groupId,
        message,
        tags: '#Announcement '
      });
    }

    return true;
  }

  debouncedUpdateMeetingPassword = debounce(async (name, notice, meetingPassword) => {
    await globalThis.dsObject.updateGroup(this.group.groupId, {
      name,
      notice,
      meetingPassword
    });
  }, 1000);

  removeUserAsync = async (userId, onCompleted) => {
    const result = await globalThis.dsObject.removeUserFromGroup(this.group.groupId, userId, true);

    if (result) {
      onCompleted();
      await this.loadAsync();
    }
  };

  leaveGroup = (onCompleted) => {
    Alert.alert(i18n2.t('Common.Warning'), i18n2.t('GroupsScreen.LeaveGroupWarning').replace('{0}', this.group.name), [
      {
        text: i18n2.t('Common.Confirm'),
        onPress: async () => {
          const result = await globalThis.dsObject.leaveGroup(this.group.groupId, true, () => onCompleted());
          if (result) {
            // leaves group, will navigate back to top screen since the group screen layout will be changed
            this.props.navigation.popToTop();
          }
        }
      },
      {
        text: i18n2.t('Common.Cancel'),
        onPress: () => onCompleted()
      }
    ]);
  };

  promoteAsync = async (user, onCompleted) => {
    const result = await globalThis.dsObject.groupUserPromote(this.group.groupId, user.userId, true, () =>
      onCompleted()
    );
    if (result) {
      await this.loadAsync();
    }
  };

  demoteAsync = async (user, onCompleted) => {
    const result = await globalThis.dsObject.groupUserDemote(this.group.groupId, user.userId, true, () =>
      onCompleted()
    );
    if (result) {
      if (user.userId === this.userId) {
        // leaves group, will navigate back to top screen since the group screen layout will be changed
        this.props.navigation.popToTop();
      } else {
        await this.loadAsync();
      }
    }
  };

  approveRequestAsync = async (user, onCompleted) => {
    const result = await globalThis.dsObject.respondToRequest(this.group.groupId, user.userId, true);
    if (result) {
      user.accepted = true;
      const newUsers = [user, ...this.state.users.filter((uu) => uu.userId !== user.userId)];
      this.setState({ users: newUsers });
    }
    onCompleted();
  };

  rejectRequestAsync = async (user, onCompleted) => {
    const result = await globalThis.dsObject.respondToRequest(this.group.groupId, user.userId, false);
    if (result) {
      const newUsers = this.state.users.filter((uu) => uu.userId !== user.userId);
      this.setState({ users: newUsers });
    }
    onCompleted();
  };

  onLongPress(user) {
    const items = [];
    if (!user.accepted) {
      items.push({
        renderIcon: () => getImage('Approve'),
        text: i18n2.t('GroupScreen.Approve'),
        onPress: () => this.approveRequestAsync(user, () => this.setState({ showModal: false }))
      });
      items.push({
        renderIcon: () => getImage('Reject'),
        text: i18n2.t('GroupScreen.Reject'),
        onPress: () => this.rejectRequestAsync(user, () => this.setState({ showModal: false }))
      });
    } else if (user.userId === this.userId) {
      items.push({
        renderIcon: () => getImage('exitGroup'),
        text: i18n2.t('GroupsScreen.LeaveGroup'),
        onPress: () => this.leaveGroup(() => this.setState({ showModal: false }))
      });
      if (user.isGroupLeader) {
        items.push({
          renderIcon: () => getImage('demoteLeader'),
          text: i18n2.t('GroupsScreen.DemoteToMember'),
          onPress: () => this.demoteAsync(user, () => this.setState({ showModal: false }))
        });
      }
    } else {
      if (!this.group.isGroupLeader) {
        return;
      }

      if (user.accepted) {
        if (user.isGroupLeader) {
          items.push({
            renderIcon: () => getImage('demoteLeader'),
            text: i18n2.t('GroupsScreen.DemoteToMember'),
            onPress: () => this.demoteAsync(user, () => this.setState({ showModal: false }))
          });
        } else {
          items.push({
            renderIcon: () => getImage('promoteLeader'),
            text: i18n2.t('GroupsScreen.PromoteToLeader'),
            onPress: () => this.promoteAsync(user, () => this.setState({ showModal: false }))
          });
        }
      }

      items.push({
        renderIcon: () => getImage('exitGroup'),
        text: i18n2.t('GroupsScreen.RemoveUser'),
        onPress: () =>
          this.removeUserAsync(user.userId, () => {
            this.setState({ showModal: false });
          })
      });
    }

    this.setState({
      showModal: true,
      showModalTitle: user.name,
      showModalItems: items
    });
  }

  dealSwitchSetting = async ({ settingKey, updateValue }) => {
    const lastValue = this.state[settingKey];
    this.setState({ [settingKey]: updateValue ? 1 : 0 });
    const result = await this.updateGroupInfoAsync({
      ...this.state,
      [settingKey]: updateValue ? 1 : 0
    });
    console.log('result', result);
    if (!result) {
      showMessage({
        message: i18n2.t('Errors.Error'),
        description: i18n2.t('Errors.CheckNetwork'),
        type: 'danger'
      });
      this.setState({ [settingKey]: lastValue });
    }
  };

  renderUserGroup = (userList, width, avatarSize, paddingLeft, needInvite = false) => {
    const fontSize = getCurrentUser().getXXSmallFontSize();
    const x3fontSize = getCurrentUser().getX3SmallFontSize();
    const itemsPerRow = parseInt(width / avatarSize);
    let shortenList = false;
    if (userList.length > 0 && !userList[0].accepted) {
      if (userList.length > itemsPerRow) {
        userList = userList.slice(0, itemsPerRow - 1);
        shortenList = true;
      }
    }
    return (
      <View style={{ marginTop: 7, flexDirection: 'row', flexWrap: 'wrap', width, paddingLeft }}>
        {userList.map((user) => {
          const hasUserTag = this.context.userTags[user.userId];
          return (
            <View
              key={user.userId}
              style={{
                width: avatarSize,
                height: 100,
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 5
              }}>
              <Avatar2
                size={50}
                userId={user.userId}
                userName={user.name}
                userNameStyles={{
                  fontSize,
                  marginTop: 2
                }}
                onPress={() => {
                  if (!this.group.isOneOnOneGroup) {
                    this.props.navigation.navigate('Member', {
                      id: user.userId,
                      uniqueId: user.uniqueId,
                      name: user.name,
                      email: user.loginId,
                      userTag: this.context.userTags[user.userId] || '',
                      isUserBlocked: this.context.blocks[user.userId] || false,
                      isCurrentUserBlocking: this.context.blockedBy.includes(user.userId)
                    });
                  }
                }}
                onLongPress={() => {
                  if (!this.group.isOneOnOneGroup) {
                    this.onLongPress(user);
                  }
                }}
              />
              <Text
                style={{
                  fontSize: x3fontSize,
                  paddingBottom: 10,
                  color: hasUserTag ? 'black' : 'transparent',
                  marginTop: 10,
                  flexShrink: 1
                }}
                numberOfLines={1}
                ellipsizeMode='tail'>
                {hasUserTag ? `(${this.context.userTags[user.userId]})` : ' '}
              </Text>
            </View>
          );
        })}
        {shortenList && (
          <View
            style={{
              top: 7,
              width: avatarSize,
              alignItems: 'center'
            }}>
            <View
              style={{
                width: 50,
                height: 50,
                borderRadius: 25,
                alignItems: 'center',
                justifyContent: 'center',
                borderColor: this.props.borderColor || Colors.darkBlue,
                backgroundColor: this.props.backgroundColor || Colors.lightBlue,
                borderWidth: 0.5
              }}>
              <Entypo name='dots-three-horizontal' color={Colors.darkBlue} size={35} />
            </View>
          </View>
        )}
        {needInvite && (
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
              this.props.navigation.navigate('MemberInvite', {
                groupId: this.group.groupId,
                isOrgInvite: this.isOrgGroup,
                existingUsers: this.state.users
              });
            }}>
            <View
              style={{
                top: 7,
                width: avatarSize,
                alignItems: 'center'
              }}>
              <View
                style={{
                  width: 50,
                  height: 50,
                  borderRadius: 25,
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderColor: this.props.borderColor || Colors.darkBlue,
                  backgroundColor: this.props.backgroundColor || Colors.lightBlue,
                  borderWidth: 0.5
                }}>
                <Entypo name='plus' color={Colors.darkBlue} size={35} />
              </View>
            </View>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  getLeadersAndMembersData = () => {
    const avatarSize = 70;
    const { users, memberExpanded } = this.state;
    const width = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right - 25;
    const leaders = cloneDeep(users.filter((x) => x.isGroupLeader));
    const members = users.filter((x) => x.accepted && !x.isGroupLeader);
    const membersNeedApproval = users.filter((x) => !x.accepted);
    const itemsPerRow = parseInt(width / avatarSize);
    const rowsToShow = 1;
    const showExpandButton = !memberExpanded && members.length >= itemsPerRow * rowsToShow;
    let i = 0;
    const usersToShow = memberExpanded
      ? members
      : cloneDeep(members.filter(() => i++ < itemsPerRow * rowsToShow - (this.isGroupLeader ? 1 : 0)));
    const smallFontSize = getCurrentUser().getSmallFontSize();
    const paddingLeft = (width % avatarSize) / 2;

    let showMoreLess = false;
    let textMoreLess = '';
    if (members.length >= itemsPerRow * 2 && memberExpanded) {
      showMoreLess = true;
      textMoreLess = i18n2.t('GroupsScreen.ShowLess');
    }
    if (showExpandButton && members.length >= itemsPerRow * rowsToShow && !memberExpanded) {
      showMoreLess = true;
      textMoreLess = i18n2.t('GroupsScreen.ShowMore');
    }

    const cardTitle =
      this.isGroupLeader && membersNeedApproval.length
        ? `${i18n2.t('GroupsScreen.WaitingForApproval')} (${membersNeedApproval.length})`
        : `${i18n2.t('GroupsScreen.Leaders')} (${leaders.length})`;

    return {
      avatarSize,
      width,
      leaders,
      members,
      membersNeedApproval,
      itemsPerRow,
      rowsToShow,
      showExpandButton,
      usersToShow,
      smallFontSize,
      paddingLeft,
      showMoreLess,
      textMoreLess,
      cardTitle
    };
  };

  renderLeadersAndMembers = () => {
    const {
      avatarSize,
      width,
      leaders,
      members,
      membersNeedApproval,
      showMoreLess,
      textMoreLess,
      usersToShow,
      paddingLeft,
      smallFontSize,
      cardTitle
    } = this.getLeadersAndMembersData();

    return (
      <Card title={cardTitle}>
        {this.isGroupLeader &&
          membersNeedApproval.length !== 0 &&
          this.renderUserGroup(membersNeedApproval, width, avatarSize, paddingLeft)}
        {this.isGroupLeader && membersNeedApproval.length !== 0 && (
          <Text style={Styles.sectionHead}>{`${i18n2.t('GroupsScreen.Leaders')} (${leaders.length})`}</Text>
        )}
        {this.renderUserGroup(leaders, width, avatarSize, paddingLeft)}
        <Text style={Styles.sectionHead}>{`${i18n2.t('GroupsScreen.Members')} (${members.length})`}</Text>
        {this.isGroupLeader && (
          <View style={{ display: 'flex', flexDirection: 'row' }}>
            <SettingsList underlayColor='#FFFFFF' borderColor='#FFFFFF'>
              <SettingsList.Item
                title={
                  <GroupSettingTitle
                    title={i18n2.t('GroupsScreen.HiddenMembersTitle')}
                    tip={i18n2.t('GroupsScreen.HiddenMembersTip')}
                  />
                }
                switchState={this.state.disableMemberShowList === 1}
                switchOnValueChange={(updateValue) => {
                  this.dealSwitchSetting({
                    settingKey: 'disableMemberShowList',
                    updateValue
                  });
                }}
                hasNavArrow={false}
                hasSwitch={true}
              />
            </SettingsList>
          </View>
        )}
        {!this.state.disableMemberShowList || this.isGroupLeader ? (
          <>
            {this.renderUserGroup(usersToShow, width, avatarSize, paddingLeft, this.isGroupLeader)}
            {showMoreLess && (
              <TouchableOpacity
                onPress={() => {
                  this.setState({ memberExpanded: !this.state.memberExpanded });
                }}>
                <Text
                  style={{
                    fontSize: smallFontSize,
                    color: '#202020',
                    textDecorationLine: 'underline'
                  }}>
                  {textMoreLess}
                </Text>
              </TouchableOpacity>
            )}
          </>
        ) : (
          <Text style={{ color: 'red', fontSize: smallFontSize }}>{i18n2.t('GroupsScreen.HiddenMembersMsg')}</Text>
        )}
      </Card>
    );
  };

  fmRenderLeadersAndMembers = (fmStyles) => {
    const {
      avatarSize,
      width,
      leaders,
      members,
      membersNeedApproval,
      showMoreLess,
      textMoreLess,
      usersToShow,
      paddingLeft,
      cardTitle
    } = this.getLeadersAndMembersData();

    return (
      <Card title={cardTitle} titleStyle={fmStyles.cardTitleLeft}>
        {this.isGroupLeader &&
          membersNeedApproval.length !== 0 &&
          this.renderUserGroup(membersNeedApproval, width, avatarSize, paddingLeft)}
        {this.isGroupLeader && membersNeedApproval.length !== 0 && (
          <FmText style={fmStyles.sectionHeadLeft}>
            {i18n2.t('GroupsScreen.Leaders') + ' (' + leaders.length + ')'}
          </FmText>
        )}
        {this.renderUserGroup(leaders, width, avatarSize, paddingLeft)}
        <View style={fmStyles.divider} />
        <FmText style={fmStyles.sectionHeadLeft}>
          {i18n2.t('GroupsScreen.Members') + ' (' + members.length + ')'}
        </FmText>
        {!this.state.disableMemberShowList || this.isGroupLeader ? (
          <>
            {this.renderUserGroup(usersToShow, width, avatarSize, paddingLeft, this.isGroupLeader)}
            {showMoreLess && (
              <BorderlessButton
                borderless={false}
                onPress={() => {
                  this.setState({ memberExpanded: !this.state.memberExpanded });
                }}>
                <FmText style={fmStyles.showMoreLessText}>{textMoreLess}</FmText>
              </BorderlessButton>
            )}
          </>
        ) : (
          <FmText style={fmStyles.hiddenMembersMessage}>{i18n2.t('GroupsScreen.HiddenMembersMsg')}</FmText>
        )}
      </Card>
    );
  };

  renderLeaderTools = () => {
    const fontSize = getCurrentUser().getSmallFontSize();
    const { users } = this.state;
    return (
      <Card title={i18n2.t('GroupsScreen.LeaderTools')}>
        <View style={{ justifyContent: 'center', flexDirection: 'row' }}>
          <View style={{ flex: 1 }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => {
                goToStudyProgressAsync(this.props.navigation, this.group.groupId, this.group.name);
              }}>
              <View style={{ alignItems: 'center' }}>
                {getImage('checkProgress')}
                <Text style={{ marginTop: 3, color: Colors.darkBlue, fontSize, textAlign: 'center' }}>
                  {i18n2.t('Common.StudyProgress')}
                </Text>
              </View>
            </TouchableOpacity>
          </View>

          <View style={{ flex: 1 }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => {
                this.props.navigation.navigate('AttendanceHome', {
                  group: this.group,
                  groupName: this.group.name,
                  allUsers: users.filter((user) => user.accepted)
                });
              }}>
              <View style={{ alignItems: 'center' }}>
                {getImage('attendance')}
                <Text style={{ marginTop: 3, color: Colors.darkBlue, fontSize, textAlign: 'center' }}>
                  {i18n2.t('Common.Attendance')}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </Card>
    );
  };

  renderEditGroupName = () => {
    const { name } = this.state;
    const width = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right - 40;
    return (
      <Card title={i18n2.t('GroupsScreen.GroupName')}>
        <Input
          inputContainerStyle={[getInputContainerStyle(Dimensions.get('window').width).inputContainerStyle, { width }]}
          defaultValue={name}
          onChangeText={(text) => {
            this.setState({ name: text });
          }}
          onSubmitEditing={() => {
            if (name.trim().length > 0) {
              this.updateGroupInfoAsync(this.state);
            } else {
              // invalid group name, revert to original one
              this.setState({ name: this.group.name });
            }
          }}
        />
        <Button
          width={Dimensions.get('window').width / 4}
          title={i18n2.t('Common.Update')}
          disabled={name.trim().length <= 0}
          onPress={async () => {
            if (await this.updateGroupInfoAsync(this.state)) {
              Alert.alert(i18n2.t('Common.Succeeded'));
            }
          }}
        />
      </Card>
    );
  };

  renderMessageSettings = () => {
    const { disappearingMessages, hideMessages } = this.state;
    const fontSize = getCurrentUser().getSmallFontSize();
    const userTextUI = (
      <GroupSettingTitle
        title={i18n2.t('GroupsScreen.LocalMsgAutoDisappear')}
        warn={i18n2.t('GroupsScreen.MsgDisappearHint')}
        tip={i18n2.t('GroupsScreen.TagMsg')}
      />
    );

    const leaderTextUI = (
      <GroupSettingTitle
        title={i18n2.t('GroupsScreen.GroupMsgAutoDelete')}
        warn={i18n2.t('GroupsScreen.MsgDeleteHint')}
        tip={i18n2.t(
          this.group.isOneOnOneGroup ? 'GroupsScreen.DeletedMsgHintOneOnOne' : 'GroupsScreen.DeletedMsgHint'
        )}
      />
    );

    return (
      <Card title={i18n2.t('GroupsScreen.MsgSettings')}>
        {userTextUI}
        <Text style={{ fontSize: getCurrentUser().getMediumFontSize(), fontWeight: 'bold', marginTop: 15 }}>
          {getDisappearingMessagesText(hideMessages)}
        </Text>
        <Button
          width={Dimensions.get('window').width / 4}
          title={i18n2.t('Common.Update')}
          onPress={() => {
            const value = [0, 4 * 7 * 24 * 60, 7 * 24 * 60, 24 * 60, 8 * 60, 60, 5, 1];
            this.props.navigation.navigate('Select', {
              choices: [0, 1, 2, 3, 4, 5, 6, 7],
              title: i18n2.t('GroupsScreen.MsgSettings'),
              titleControl: <View style={{ margin: 10 }}>{userTextUI}</View>,
              getDisplayName: (choice) => getDisappearingMessagesChoicesName(choice),
              isCurrent: (choice) => choice === value.indexOf(hideMessages),
              onSelect: (choice) => {
                if (value[choice] === 0 || (hideMessages !== 0 && value[choice] > hideMessages)) {
                  this.updateUserHideMessagesAsync(value[choice]);
                } else {
                  Alert.alert(
                    i18n2.t('Common.Warning'),
                    i18n2.t('GroupsScreen.MsgDisappearWarning') + '\n\n' + getDisappearingMessagesText(value[choice]),
                    [
                      {
                        text: i18n2.t('Common.Confirm'),
                        onPress: async () => {
                          this.updateUserHideMessagesAsync(value[choice]);
                        }
                      },
                      {
                        text: i18n2.t('Common.Cancel'),
                        onPress: () => {}
                      }
                    ]
                  );
                }
              }
            });
          }}
        />
        {(this.group.isOneOnOneGroup || this.isGroupLeader) && (
          <>
            <View style={{ height: 20 }} />
            {leaderTextUI}
            <Text style={{ fontSize: getCurrentUser().getMediumFontSize(), fontWeight: 'bold', marginTop: 15 }}>
              {getDisappearingMessagesText(disappearingMessages)}
            </Text>
            <Button
              width={Dimensions.get('window').width / 4}
              title={i18n2.t('Common.Update')}
              onPress={() => {
                const value = [0, 4 * 7 * 24 * 60, 7 * 24 * 60, 24 * 60, 8 * 60, 60, 5, 1];
                this.props.navigation.navigate('Select', {
                  choices: [0, 1, 2, 3, 4, 5, 6, 7],
                  title: i18n2.t('GroupsScreen.MsgSettings'),
                  titleControl: <View style={{ margin: 10 }}>{leaderTextUI}</View>,
                  getDisplayName: (choice) => getDisappearingMessagesChoicesName(choice),
                  isCurrent: (choice) => choice === value.indexOf(disappearingMessages),
                  onSelect: (choice) => {
                    if (value[choice] === 0 || (disappearingMessages !== 0 && value[choice] > disappearingMessages)) {
                      this.updateGroupInfoAsync({ ...this.state, disappearingMessages: value[choice] });
                    } else {
                      Alert.alert(
                        i18n2.t('Common.Warning'),
                        i18n2.t('GroupsScreen.MsgDisappearWarning') +
                          '\n\n' +
                          getDisappearingMessagesText(value[choice]),
                        [
                          {
                            text: i18n2.t('Common.Confirm'),
                            onPress: async () => {
                              this.updateGroupInfoAsync({ ...this.state, disappearingMessages: value[choice] });
                            }
                          },
                          {
                            text: i18n2.t('Common.Cancel'),
                            onPress: () => {}
                          }
                        ]
                      );
                    }
                  }
                });
              }}
            />
          </>
        )}
        <View style={{ display: 'flex', marginTop: 15, flexDirection: 'row' }}>
          <SettingsList underlayColor='#FFFFFF' borderColor='#FFFFFF' itemMarginTop={10}>
            <SettingsList.Item
              title={
                <GroupSettingTitle
                  title={i18n2.t('GroupsScreen.MuteNewMsg')}
                  tip={i18n2.t('GroupsScreen.MuteDescription')}
                />
              }
              hasNavArrow={false}
              switchState={this.state.muteMsgNotification === 1}
              switchOnValueChange={(updateValue) => {
                this.dealSwitchSetting({
                  settingKey: 'muteMsgNotification',
                  updateValue
                });
              }}
              hasSwitch={true}
            />
            {this.isGroupLeader && (
              <SettingsList.Item
                title={
                  <GroupSettingTitle
                    title={i18n2.t('GroupsScreen.DisableMemberPost')}
                    tip={i18n2.t('GroupsScreen.DisableMemberPostTip')}
                  />
                }
                hasNavArrow={false}
                switchState={this.state.onlyLeaderCanPost === 1}
                switchOnValueChange={(updateValue) => {
                  this.dealSwitchSetting({
                    settingKey: 'onlyLeaderCanPost',
                    updateValue
                  });
                }}
                hasSwitch={true}
              />
            )}
          </SettingsList>
          {!this.isGroupLeader && !!this.state.onlyLeaderCanPost && (
            <Text style={{ color: 'red', fontSize }}>{i18n2.t('GroupsScreen.DisableMemberPostTip')}</Text>
          )}
        </View>
      </Card>
    );
  };

  fmRenderMessageSettings = (fmStyles) => {
    const { disappearingMessages, hideMessages } = this.state;

    const hideMessagesDisabled = hideMessages === 0;
    const disappearingMessagesDisabled = disappearingMessages === 0;

    const userTextUI = (
      <GroupSettingTitle
        style={fmStyles.groupSettingTitleLeft}
        title={i18n2.t('GroupsScreen.LocalMsgAutoDisappear')}
        warn={i18n2.t('GroupsScreen.MsgDisappearHint')}
        tip={i18n2.t('GroupsScreen.TagMsg')}
      />
    );

    const leaderTextUI = (
      <GroupSettingTitle
        style={fmStyles.groupSettingTitleLeft}
        title={i18n2.t('GroupsScreen.GroupMsgAutoDelete')}
        warn={i18n2.t('GroupsScreen.MsgDeleteHint')}
        tip={i18n2.t(
          this.group.isOneOnOneGroup ? 'GroupsScreen.DeletedMsgHintOneOnOne' : 'GroupsScreen.DeletedMsgHint'
        )}
      />
    );

    return (
      <Card title={i18n2.t('GroupsScreen.MsgSettings')} titleStyle={fmStyles.cardTitleLeft}>
        {userTextUI}
        <Button
          width='100%'
          title={getDisappearingMessagesText(hideMessages)}
          style={
            hideMessagesDisabled
              ? { backgroundColor: 'transparent', borderColor: FmColors.mediumGray100, borderWidth: 0.5 }
              : {}
          }
          onPress={() => {
            const value = [0, 4 * 7 * 24 * 60, 7 * 24 * 60, 24 * 60, 8 * 60, 60, 5, 1];
            this.props.navigation.navigate('Select', {
              choices: [0, 1, 2, 3, 4, 5, 6, 7],
              title: i18n2.t('GroupsScreen.MsgSettings'),
              titleControl: <View style={{ margin: 10 }}>{userTextUI}</View>,
              getDisplayName: (choice) => getDisappearingMessagesChoicesName(choice),
              isCurrent: (choice) => choice === value.indexOf(hideMessages),
              onSelect: (choice) => {
                if (value[choice] === 0 || (hideMessages !== 0 && value[choice] > hideMessages)) {
                  this.updateUserHideMessagesAsync(value[choice]);
                } else {
                  Alert.alert(
                    i18n2.t('Common.Warning'),
                    i18n2.t('GroupsScreen.MsgDisappearWarning') + '\n\n' + getDisappearingMessagesText(value[choice]),
                    [
                      {
                        text: i18n2.t('Common.Confirm'),
                        onPress: async () => {
                          this.updateUserHideMessagesAsync(value[choice]);
                        }
                      },
                      {
                        text: i18n2.t('Common.Cancel'),
                        onPress: () => {}
                      }
                    ]
                  );
                }
              }
            });
          }}
        />
        {(this.group.isOneOnOneGroup || this.isGroupLeader) && (
          <>
            <View style={{ height: 20 }} />
            {leaderTextUI}
            <Button
              width='100%'
              title={getDisappearingMessagesText(disappearingMessages)}
              style={
                disappearingMessagesDisabled
                  ? { backgroundColor: 'transparent', borderColor: FmColors.mediumGray100, borderWidth: 0.5 }
                  : {}
              }
              onPress={() => {
                const value = [0, 4 * 7 * 24 * 60, 7 * 24 * 60, 24 * 60, 8 * 60, 60, 5, 1];
                this.props.navigation.navigate('Select', {
                  choices: [0, 1, 2, 3, 4, 5, 6, 7],
                  title: i18n2.t('GroupsScreen.MsgSettings'),
                  titleControl: <View style={{ margin: 10 }}>{leaderTextUI}</View>,
                  getDisplayName: (choice) => getDisappearingMessagesChoicesName(choice),
                  isCurrent: (choice) => choice === value.indexOf(disappearingMessages),
                  onSelect: (choice) => {
                    if (value[choice] === 0 || (disappearingMessages !== 0 && value[choice] > disappearingMessages)) {
                      this.updateGroupInfoAsync({ ...this.state, disappearingMessages: value[choice] });
                    } else {
                      Alert.alert(
                        i18n2.t('Common.Warning'),
                        i18n2.t('GroupsScreen.MsgDisappearWarning') +
                          '\n\n' +
                          getDisappearingMessagesText(value[choice]),
                        [
                          {
                            text: i18n2.t('Common.Confirm'),
                            onPress: async () => {
                              this.updateGroupInfoAsync({ ...this.state, disappearingMessages: value[choice] });
                            }
                          },
                          {
                            text: i18n2.t('Common.Cancel'),
                            onPress: () => {}
                          }
                        ]
                      );
                    }
                  }
                });
              }}
            />
          </>
        )}
      </Card>
    );
  };

  renderGroupNotice = () => {
    const { notice } = this.state;
    if (!notice && !this.isGroupLeader) {
      return null;
    }

    const fontSize = getCurrentUser().getSmallFontSize();
    const width = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right - 40;
    return (
      <Card title={i18n2.t('Common.Announcement')}>
        {!this.isGroupLeader ? (
          <ParsedText
            style={{
              margin: 7,
              color: '#202020',
              fontSize,
              lineHeight: fontSize * 1.25
            }}
            parse={[
              {
                type: 'url',
                style: { textDecorationLine: 'underline' },
                onPress: (url) => {
                  WebBrowser.openBrowserAsync(url);
                }
              }
            ]}
            childrenProps={{ allowFontScaling: false }}>
            {notice}
          </ParsedText>
        ) : null}
        {this.isGroupLeader ? (
          <>
            <Input
              inputContainerStyle={[
                getInputContainerStyle(Dimensions.get('window').width).inputContainerStyle,
                { width }
              ]}
              multiline
              defaultValue={notice}
              onChangeText={(text) => {
                this.setState({ notice: text });
              }}
            />
            <Button
              width={Dimensions.get('window').width / 4}
              title={i18n2.t('Common.Post')}
              onPress={async () => {
                if (await this.updateGroupNoticeAsync()) {
                  Alert.alert(i18n2.t('Common.Succeeded'));
                }
              }}
            />
          </>
        ) : null}
      </Card>
    );
  };

  renderAssociatedClasses = () => {
    const { classes } = this.state;
    if (classes.length === 0) {
      return null;
    }

    const imageMargin = 20;
    const imageSize =
      (Dimensions.get('window').width - this.context.insets.left - this.context.insets.right - 50) / 4 -
      imageMargin * 2;
    return (
      <Card title={`${i18n2.t('GroupsScreen.Classes')} (${classes.length})`}>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            flexWrap: 'wrap',
            justifyContent: 'flex-start'
          }}>
          {classes.map((item) => (
            <ClassCover
              key={item.classId}
              size={imageSize}
              margin={imageMargin}
              classId={item.classId}
              text={item.name}
              fontSize={9}
            />
          ))}
        </View>
      </Card>
    );
  };

  renderSharing = () => {
    const { joinLink, qrCode, joinCode } = this.state;
    const fontSize = getCurrentUser().getSmallFontSize();
    const qrCodeSize = Math.min(Dimensions.get('window').width / 3, 100);
    const safeJoinLink = joinLink.replace(/\/\/.+\//, '//idigest.app/');
    const joinLinkText = `${i18n2.t('GroupsScreen.InvitationLink')}${safeJoinLink}`;

    // a blank is added between the first 4 digits and the last 4 digits
    const joinCodeText = `${i18n2.t('GroupsScreen.InvitationCode')}${joinCode.slice(0, 4)} ${joinCode.slice(4)}`;
    const invitationMessage = i18n2
      .t('GroupsScreen.NotifyInvitation')
      .replace('{User}', this.context.userProfile.displayName)
      .replace('{Email}', this.loginId)
      .replace('{Code}', joinCode)
      .replace('{Url}', joinLink);

    return (
      <Card title={i18n2.t('GroupsScreen.ShareGroup')}>
        <Text style={{ marginTop: 3, color: Colors.darkBlue, fontSize, textAlign: 'center' }}>
          {i18n2.t('GroupsScreen.ShareGroupHint')}
        </Text>

        <TouchableOpacity
          style={{ alignItems: 'center' }}
          onPress={() => {
            Clipboard.setStringAsync(invitationMessage);
            const safeText = invitationMessage.replace(/\/\/.+\//, '//idigest.app/');
            Alert.alert(i18n2.t('GroupsScreen.CopyToClipboard'), safeText);
          }}>
          <Text
            style={{
              marginTop: 5,
              color: Colors.darkBlue,
              fontSize,
              textAlign: 'center'
            }}>
            {joinCodeText}
          </Text>
          <Image style={{ width: qrCodeSize, height: qrCodeSize, marginTop: 10 }} source={{ uri: qrCode }} />
          <Text
            numberOfLines={1}
            style={{
              marginTop: 5,
              color: Colors.darkBlue,
              fontSize,
              textAlign: 'center'
            }}>
            {joinLinkText}
          </Text>
        </TouchableOpacity>

        <Button
          width={Dimensions.get('window').width / 2}
          title={i18n2.t('Common.Share')}
          onPress={() => {
            Share.share({
              title: i18n2.t('GroupsScreen.InvitationFromIDigest'),
              message: invitationMessage
            });
          }}
        />
        <View style={{ display: 'flex', marginTop: 0, flexDirection: 'row' }}>
          <SettingsList underlayColor='#FFFFFF' borderColor='#FFFFFF'>
            <SettingsList.Item
              title={i18n2.t('GroupsScreen.RequireLeaderApproval')}
              titleStyle={{ fontSize }}
              titleInfoStyle={{ fontSize }}
              hasNavArrow={false}
              switchState={this.state.joinRequireLeaderApproval === 1}
              switchOnValueChange={async () => {
                const currStatus = this.state.joinRequireLeaderApproval;
                const result = await this.updateGroupInfoAsync({
                  ...this.state,
                  joinRequireLeaderApproval: currStatus == 0 ? 1 : 0
                });
                if (!result) {
                  showMessage({
                    message: i18n2.t('Errors.Error'),
                    description: i18n2.t('Errors.CheckNetwork'),
                    type: 'danger'
                  });
                }
              }}
              hasSwitch={true}
            />
          </SettingsList>
        </View>
      </Card>
    );
  };

  fmRenderSharing = (fmStyles) => {
    const { joinLink, qrCode, joinCode } = this.state;

    const qrCodeSize = Math.min(Dimensions.get('window').width / 3, 100);

    const invitationMessage = i18n2
      .t('GroupsScreen.NotifyInvitation')
      .replace('{User}', this.context.userProfile.displayName)
      .replace('{Email}', this.loginId)
      .replace('{Code}', joinCode)
      .replace('{Url}', joinLink);

    return (
      <Card title={i18n2.t('GroupsScreen.ShareGroup')} titleStyle={fmStyles.cardTitleLeft}>
        <FmText style={fmStyles.hintTextLeft}>{i18n2.t('GroupsScreen.ShareGroupHint')}</FmText>

        <View style={fmStyles.shareRowContainer}>
          <BorderlessButton
            borderless={false}
            style={fmStyles.shareContainer}
            onPress={() => {
              Clipboard.setStringAsync(invitationMessage);
              const safeText = invitationMessage.replace(/\/\/.+\//, '//idigest.app/');
              Alert.alert(i18n2.t('GroupsScreen.CopyToClipboard'), safeText);
            }}>
            <Image style={{ width: qrCodeSize, height: qrCodeSize, marginTop: 10 }} source={{ uri: qrCode }} />
          </BorderlessButton>

          <Button
            width={Dimensions.get('window').width / 2}
            title={i18n2.t('Common.Share')}
            onPress={() => {
              Share.share({
                title: i18n2.t('GroupsScreen.InvitationFromIDigest'),
                message: invitationMessage
              });
            }}
          />
        </View>
      </Card>
    );
  };

  renderJoinMeeting = () => {
    const { meetingLink, meetingPassword, name, notice } = this.state;
    const fontSize = getCurrentUser().getSmallFontSize();
    return (
      <Card title={i18n2.t('GroupsScreen.OnlineMeeting')}>
        {!this.isGroupLeader && (
          <Button
            width={Dimensions.get('window').width / 3}
            title={i18n2.t('Common.Join')}
            onPress={() => {
              launchMeetingAsync(this.group.groupId, this.context.canOpenJitsi, this.context.canOpenFishMeet);
            }}
          />
        )}

        {this.isGroupLeader && (
          <>
            <Text
              style={{
                marginBottom: 12,
                color: Colors.darkBlue,
                fontSize,
                textAlign: 'center'
              }}>
              {i18n2.t('GroupsScreen.InviteToMeeting')}
            </Text>

            <Input
              inputContainerStyle={[
                getInputContainerStyle(Dimensions.get('window').width).inputContainerStyle,
                { width: Dimensions.get('window').width / 2 }
              ]}
              defaultValue={meetingPassword}
              label={i18n2.t('Common.Pwd')}
              labelStyle={{ textAlign: 'center' }}
              autoCorrect={false}
              placeholder={meetingPassword.length > 0 ? '' : i18n2.t('Common.NoPwd')}
              onChangeText={(text) => {
                const value = text.trim();
                this.setState({ meetingPassword: value });
                this.debouncedUpdateMeetingPassword(name, notice, value);
              }}
            />

            <Button
              width={Dimensions.get('window').width / 2}
              title={i18n2.t('GroupsScreen.CopyMeetingInfo')}
              onPress={() => {
                const password = meetingPassword.trim();
                const text =
                  password.length > 0
                    ? `${i18n2.t('GroupsScreen.MeetingLink')}${meetingLink}\n${i18n2.t(
                        'GroupsScreen.MeetingPwd'
                      )}${password}`
                    : `${i18n2.t('GroupsScreen.MeetingLink')}${meetingLink}`;

                Clipboard.setStringAsync(text);

                const safeText = text.replace(/\/\/.+\//, '//idigest.app/');
                Alert.alert(i18n2.t('GroupsScreen.CopyToClipboard'), safeText);
              }}
            />

            <Button
              width={Dimensions.get('window').width / 2}
              title={i18n2.t('Common.Join')}
              onPress={() => {
                launchMeetingAsync(this.group.groupId, this.context.canOpenJitsi, this.context.canOpenFishMeet);
              }}
            />
          </>
        )}
      </Card>
    );
  };

  fmRenderJoinMeeting = (fmStyles) => {
    const { meetingLink, meetingPassword, name, notice } = this.state;

    return (
      <Card title={i18n2.t('GroupsScreen.OnlineMeeting')} titleStyle={fmStyles.cardTitleLeft}>
        {!this.isGroupLeader && (
          <Button
            width={Dimensions.get('window').width / 3}
            title={i18n2.t('Common.Join')}
            onPress={() => {
              launchMeetingAsync(this.group.groupId, this.context.canOpenJitsi, this.context.canOpenFishMeet);
            }}
          />
        )}

        {this.isGroupLeader && (
          <>
            <FmText style={fmStyles.hintTextLeft}>{i18n2.t('GroupsScreen.InviteToMeeting')}</FmText>

            <InputField
              defaultValue={meetingPassword}
              autoCorrect={false}
              placeholder={meetingPassword.length > 0 ? '' : i18n2.t('Common.NoPwd')}
              onChangeText={(text) => {
                const value = text.trim();
                this.setState({ meetingPassword: value });
                this.debouncedUpdateMeetingPassword(name, notice, value);
              }}
            />

            <Button
              width='100%'
              title={i18n2.t('GroupsScreen.CopyMeetingInfo')}
              onPress={() => {
                const password = meetingPassword.trim();
                const text =
                  password.length > 0
                    ? `${i18n2.t('GroupsScreen.MeetingLink')}${meetingLink}\n${i18n2.t(
                        'GroupsScreen.MeetingPwd'
                      )}${password}`
                    : `${i18n2.t('GroupsScreen.MeetingLink')}${meetingLink}`;
                Clipboard.setStringAsync(text);
                const safeText = text.replace(/\/\/.+\//, '//idigest.app/');
                Alert.alert(i18n2.t('GroupsScreen.CopyToClipboard'), safeText);
              }}
            />
          </>
        )}
      </Card>
    );
  };

  renderModal = () => {
    const { showModal, showModalItems, showModalTitle } = this.state;
    return (
      <ModalPopup
        insets={this.context.insets}
        visible={showModal}
        setNotVisible={() => this.setState({ showModal: false })}
        title={showModalTitle}
        items={showModalItems}
        iconsPerRow={showModalItems.length}
        showCancel={true}
        showDivider={true}
        hideOnPress={false}
      />
    );
  };

  fmRenderGroupName = (fmStyles) => {
    return <Card title={this.state.name} titleStyle={fmStyles.groupNameCardTitleLeft} />;
  };

  iDigestRender = () => {
    const { busy } = this.state;
    return (
      <KeyboardView>
        <View
          style={{
            flex: 1,
            paddingLeft: this.context.insets.left,
            paddingRight: this.context.insets.right,
            paddingBottom: this.context.insets.bottom,
            backgroundColor: '#F4F4F4'
          }}>
          {!this.group.isOneOnOneGroup && this.renderLeadersAndMembers()}
          {this.isGroupLeader && this.renderSharing()}
          {this.renderJoinMeeting()}
          {this.isGroupLeader && this.renderLeaderTools()}
          {this.renderGroupNotice()}
          {this.renderMessageSettings()}
          {this.isGroupLeader && this.renderEditGroupName()}
          {this.renderAssociatedClasses()}
          {this.renderModal()}
        </View>
        {busy ? <LoadingIndicator /> : null}
      </KeyboardView>
    );
  };

  fmRender = () => {
    const { busy } = this.state;
    const fmStyles = getFmGroupScreenStyles();

    return (
      <KeyboardView>
        <View
          style={{
            flex: 1,
            paddingLeft: this.context.insets.left,
            paddingRight: this.context.insets.right,
            paddingBottom: this.context.insets.bottom,
            backgroundColor: FmColors.background
          }}>
          {!this.group.isOneOnOneGroup && this.fmRenderGroupName(fmStyles)}
          {!this.group.isOneOnOneGroup && this.fmRenderLeadersAndMembers(fmStyles)}
          {this.isGroupLeader && this.fmRenderSharing(fmStyles)}
          {this.fmRenderJoinMeeting(fmStyles)}
          {this.fmRenderMessageSettings(fmStyles)}
        </View>
        {busy ? <LoadingIndicator /> : null}
      </KeyboardView>
    );
  };

  render() {
    return isFishMeet ? this.fmRender() : this.iDigestRender();
  }
}
