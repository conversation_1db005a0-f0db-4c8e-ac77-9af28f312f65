import { Alert, StyleSheet, Text, View, useWindowDimensions } from 'react-native';
import { InputField, InputFieldRef } from '@/fishMeet/components/InputField';
import React, { useRef, useState } from 'react';

import { Button } from '@/components';
import { Colors } from '@/styles/colors';
import { ExplanationHeader } from '@/fishMeet/components/ExplanationHeader';
import { FmStyles } from '@/fishMeet/styles/fmStyles';
import { Input } from '@rneui/themed';
import { KeyboardView } from '@/components';
import { fmStyles } from '@/fishMeet/styles/screens/joinStudyGroupScreenStyles';
import { getCurrentUser } from '@/utils/user';
import { getInputContainerStyle } from '@/styles/styles';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function JoinStudyGroupScreen({ navigation }: any) {
  const [invitationCode, setInvitationCode] = useState('');
  const [busy, setBusy] = useState(false);
  const invitationCodeInput = useRef<InputFieldRef>(null);

  useSetNavigationOptions({
    title: i18n2.t('GroupsScreen.JoinGroup')
  });

  const { width } = useWindowDimensions();

  async function joinGroupAsync() {
    if (!invitationCode || invitationCode.length < 1) {
      invitationCodeInput.current?.shake();
      invitationCodeInput.current?.focus();
      return;
    }

    if (busy) {
      return;
    }

    try {
      setBusy(true);
      const result = await globalThis.dsObject.joinGroupViaCode(invitationCode);
      console.log(result);
      if (!result) {
        return;
      }
      // there are two cases: join success, or join but in waiting
      const msg = result?.body?.status ? 'Common.JoinSuccess' : 'Common.JoinWaiting';
      Alert.alert(i18n2.t(msg));

      navigation.pop();
    } finally {
      setBusy(false);
    }
  }

  function iDigestRender() {
    return (
      <KeyboardView>
        <View style={styles.container}>
          <Text style={styles.inputCode}>{i18n2.t('JoinStudyGroupScreen.PleaseInputCode')}</Text>
          <Text style={styles.inputCodeHint}>{i18n2.t('JoinStudyGroupScreen.InputCodeHint')}</Text>

          <Input
            keyboardType='numeric'
            autoFocus={true}
            maxLength={8}
            inputContainerStyle={getInputContainerStyle(width).inputContainerStyle}
            inputStyle={styles.inputStyle}
            ref={invitationCodeInput}
            onChangeText={(text) => {
              setInvitationCode(text);
            }}
          />

          <Button style={styles.button} title={i18n2.t('Common.Submit')} onPress={joinGroupAsync} />
        </View>
      </KeyboardView>
    );
  }

  function fmRender() {
    return (
      <KeyboardView>
        <View style={FmStyles.screenContainer}>
          <ExplanationHeader
            titleText={i18n2.t('JoinStudyGroupScreen.PleaseInputCode')}
            explanationText={i18n2.t('JoinStudyGroupScreen.InputCodeHint')}
          />

          <View style={FmStyles.sectionSpacer}></View>
          <InputField
            keyboardType='numeric'
            autoFocus={true}
            maxLength={8}
            inputStyle={fmStyles.inputStyle}
            ref={invitationCodeInput}
            onChangeText={(text) => {
              setInvitationCode(text);
            }}
          />
          <View style={FmStyles.sectionSpacer}></View>

          <Button title={i18n2.t('Common.Submit')} onPress={joinGroupAsync} />
        </View>
      </KeyboardView>
    );
  }

  return isFishMeet ? fmRender() : iDigestRender();
}

export default JoinStudyGroupScreen;

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  inputCode: {
    marginTop: 20,
    color: Colors.darkBlue,
    fontSize: getCurrentUser().getMediumFontSize()
  },
  inputCodeHint: {
    color: Colors.darkBlue,
    fontSize: getCurrentUser().getSmallFontSize()
  },
  inputStyle: {
    textAlign: 'center'
  },
  button: {
    marginTop: 60
  }
});
