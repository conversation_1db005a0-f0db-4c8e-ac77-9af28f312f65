import { But<PERSON>, KeyboardView } from '@/components';
import { Dimensions, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useRef } from 'react';

import { BorderlessButton } from 'react-native-gesture-handler';
import { Colors } from '@/styles/colors';
import Constants from 'expo-constants';
import { ExplanationHeader } from '@/fishMeet/components/ExplanationHeader';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { FmStyles } from '@/fishMeet/styles/fmStyles';
import { FmText } from '@/fishMeet/components/FmText';
import { GoogleSigninButton } from '@/utils/GoogleSignInHelper';
import { Input } from '@rneui/themed';
import { InputField } from '@/fishMeet/components/InputField';
import { appVersion } from '@/dataStorage/storage';
import { fmStyles } from '@/fishMeet/styles/screens/userLoginScreenStyles';
import { getInputContainerStyle } from '@/styles/styles';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { useUserLoginViewModel } from '@/viewModels/UserLoginViewModel';

const UserLoginScreen = () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const emailInputRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const passwordInputRef = useRef<any>(null);

  const {
    email,
    setEmail,
    password,
    setPassword,
    loginUser,
    emailError,
    passwordError,
    navigateToForgot,
    navigateToCreateAccount,
    navigateToLanguageSelect,
    fontSize,
    handleGoogleSignIn
  } = useUserLoginViewModel();

  const handleLogin = async () => {
    if (emailError && emailInputRef.current) {
      emailInputRef.current.shake();
      emailInputRef.current.focus();
    } else if (passwordError && passwordInputRef.current) {
      passwordInputRef.current.shake();
      passwordInputRef.current.focus();
    }

    await loginUser();
  };

  const iDigestRender = () => {
    const inputContainerStyle = getInputContainerStyle(Dimensions.get('window').width).inputContainerStyle;

    return (
      <KeyboardView>
        <View style={styles.container}>
          <Text style={{ ...styles.emailText, fontSize }}>{i18n2.t('Email')}</Text>
          <Input
            testID='EmailInputBox'
            inputContainerStyle={inputContainerStyle}
            placeholder={i18n2.t('EmailExample')}
            underlineColorAndroid='transparent'
            defaultValue={email}
            ref={emailInputRef}
            autoFocus={true}
            autoCorrect={false}
            autoCapitalize='none'
            returnKeyType={'next'}
            submitBehavior='submit'
            errorStyle={styles.emailError}
            onChangeText={setEmail}
            errorMessage={emailError ? i18n2.t('EmailError') : ''}
            onSubmitEditing={() => passwordInputRef.current?.focus()}
          />
          <View style={styles.sectionSpacer} />

          <Text style={{ ...styles.pwdText, fontSize }}>{i18n2.t('Common.Pwd')}</Text>
          <Input
            placeholder={i18n2.t('Common.Pwd')}
            inputContainerStyle={inputContainerStyle}
            //value={password}
            ref={passwordInputRef}
            secureTextEntry={true}
            defaultValue={password}
            submitBehavior='submit'
            returnKeyType={'join'}
            errorStyle={styles.pwdError}
            onChangeText={setPassword}
            errorMessage={passwordError ? i18n2.t('PasswordError') : ''}
            onSubmitEditing={handleLogin}
          />

          <TouchableOpacity activeOpacity={1} onPress={navigateToForgot} style={styles.forgotPasswordOpacity}>
            <Text style={{ ...styles.forgotPasswordText, fontSize }}>{i18n2.t('LoginScreen.ForgotPwd')}</Text>
          </TouchableOpacity>

          <View style={styles.buttonContainer}>
            <Button testID='LoginButton' title={i18n2.t('Login')} onPress={handleLogin} />

            <Button title={i18n2.t('LoginScreen.CreateNew')} onPress={navigateToCreateAccount} />

            <View style={styles.googleButtonWrapper}>
              <GoogleSigninButton
                style={styles.googleSignInButton}
                size={GoogleSigninButton.Size.Wide}
                color={GoogleSigninButton.Color.Dark}
                onPress={handleGoogleSignIn}
              />
            </View>
          </View>
          <TouchableOpacity onPress={() => navigateToLanguageSelect()}>
            <Text
              style={{
                ...styles.changeLanguageText,
                fontSize
              }}>
              Change language/更改语言
            </Text>
          </TouchableOpacity>
        </View>
        <View style={styles.sdkVersionContainer}>
          <Text style={styles.sdkVersionText}>
            v{appVersion}({Constants.expoConfig ? Constants.expoConfig.sdkVersion : 'unknown'})
          </Text>
        </View>
      </KeyboardView>
    );
  };

  const fmRender = () => (
    <KeyboardView>
      <View style={FmStyles.screenContainer}>
        <ExplanationHeader
          titleText={i18n2.t('LoginScreen.HeaderTitle')}
          explanationText={i18n2.t('LoginScreen.HeaderExplanation')}
        />

        <InputField
          inputType='email'
          testID='EmailInputBox'
          ref={emailInputRef}
          label={i18n2.t('Email')}
          defaultValue={email}
          onChangeText={setEmail}
          onSubmitEditing={() => passwordInputRef.current?.focus()}
          errorMessage={emailError ? i18n2.t('EmailError') : ''}
        />
        <View style={FmStyles.sectionSpacer} />

        <InputField
          inputType='password'
          ref={passwordInputRef}
          label={i18n2.t('Common.Pwd')}
          defaultValue={password}
          onChangeText={setPassword}
          onSubmitEditing={handleLogin}
          errorMessage={passwordError ? i18n2.t('PasswordError') : ''}
        />

        <BorderlessButton borderless={false} onPress={navigateToForgot} style={fmStyles.forgotPwdOrCreateNewOpacity}>
          <FmText style={{ fontSize }}>{i18n2.t('LoginScreen.ForgotPwd')}</FmText>
        </BorderlessButton>

        <View style={FmStyles.buttonContainer}>
          <Button testID='LoginButton' title={i18n2.t('Login')} onPress={handleLogin} />
        </View>

        <BorderlessButton
          borderless={false}
          onPress={navigateToCreateAccount}
          style={fmStyles.forgotPwdOrCreateNewOpacity}>
          <FmText style={{ fontSize }}>{i18n2.t('LoginScreen.CreateNew')}</FmText>
        </BorderlessButton>

        <View style={FmStyles.buttonContainer}>
          <Button
            testID='ChangeLanguageButton'
            title='Change language/更改语言'
            onPress={() =>
              navigateToLanguageSelect(
                <ExplanationHeader
                  titleText={i18n2.t('SelectScreen.ChangeLanguageHeaderTitle')}
                  explanationText={i18n2.t('SelectScreen.ChangeLanguageHeaderExplanation')}
                />
              )
            }
            style={{ backgroundColor: undefined, borderWidth: 2, borderColor: FmColors.mediumGray50 }}
            textStyle={{
              color: FmColors.mediumGray100,
              fontSize,
              fontWeight: 'normal',
              textTransform: 'none'
            }}
          />
        </View>

        <View style={fmStyles.otherLoginContainer}>
          <FmText style={{ ...fmStyles.otherLoginText, fontSize }}>{i18n2.t('LoginScreen.OtherLogin')}</FmText>
          <GoogleSigninButton
            style={fmStyles.googleSignInButton}
            size={GoogleSigninButton.Size.Wide}
            color={GoogleSigninButton.Color.Dark}
            onPress={handleGoogleSignIn}
          />
        </View>
      </View>
    </KeyboardView>
  );

  return isFishMeet ? fmRender() : iDigestRender();
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff'
  },
  emailText: {
    marginTop: 10,
    color: Colors.darkBlue
  },
  emailError: {
    height: 0
  },
  pwdText: {
    color: Colors.darkBlue
  },
  pwdError: {
    height: 0
  },
  forgotPasswordOpacity: {
    marginTop: 10
  },
  forgotPasswordText: {
    color: Colors.darkBlue,
    fontWeight: 'bold',
    textDecorationLine: 'underline'
  },
  changeLanguageText: {
    color: Colors.darkBlue,
    fontWeight: 'bold',
    marginTop: 7,
    textDecorationLine: 'underline'
  },
  sdkVersionContainer: {
    position: 'absolute',
    top: 7,
    right: 2
  },
  sdkVersionText: {
    fontSize: 5
  },
  buttonContainer: {
    marginTop: 7
  },
  sectionSpacer: {
    marginTop: 17
  },
  googleButtonWrapper: {
    alignItems: 'center',
    marginVertical: 10
  },
  googleSignInButton: {
    width: 192,
    height: 48
  }
});

export default UserLoginScreen;
