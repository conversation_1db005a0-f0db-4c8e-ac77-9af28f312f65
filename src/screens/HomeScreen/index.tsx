import React, { ReactNode, useContext, useEffect, useMemo, useState } from 'react';
import { View, useWindowDimensions } from 'react-native';

import { AppContext } from '@/context/AppContext';
import BibleGuide from '@/screens/HomeScreen/components/BibleGuide';
import LessonList from '@/screens/HomeScreen/components/LessonList';
import LogoBlock from '@/screens/HomeScreen/components/LogoBlock';
import MyStudyList from '@/screens/HomeScreen/components/MyStudyList';
import NavTabs from '@/components/navigation/NavTabs';
import ProgramList from '@/screens/HomeScreen/components/ProgramList';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';

// Define tab interface
interface Tab {
  id: string;
  title: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  component: (props: any) => ReactNode;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  props?: any;
}

const HomeScreen = () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { insets, setUserLastSeenClassAsync } = useContext(AppContext) as any;
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [page, setPage] = useState<number>(0);
  const [bibleModeEnabled, setBibleModeEnabled] = useState<boolean>(
    !!getCurrentUser().getProperty('setting.bibleMode')
  );
  const [channelModeEnabled, setChannelModeEnabled] = useState<boolean>(
    !!getCurrentUser().getProperty('setting.channelMode')
  );
  const [languageChangeTime, setLanguageChangeTime] = useState<number>(Date.now());
  const { width: windowWidth } = useWindowDimensions();

  useEffect(() => {
    const updateChangeTime = () => {
      setLanguageChangeTime(Date.now());
    };
    global.eventEmitter.addListener('languageChanged', updateChangeTime);
    return () => {
      global.eventEmitter.removeListener('languageChanged', updateChangeTime);
    };
  }, []);

  // Define all possible tabs
  const allTabs: Record<string, Tab> = useMemo(
    () => ({
      bible: {
        id: 'bible',
        title: i18n2.t('DiscoverUI.Bible'),
        component: BibleGuide
      },
      programs: {
        id: 'programs',
        title: i18n2.t('DiscoverUI.Programs'),
        component: LessonList,
        props: { mode: 0, isSearching, setIsSearching }
      },
      channels: {
        id: 'channels',
        title: i18n2.t('DiscoverUI.Channels'),
        component: ProgramList,
        props: { mode: 'recommend', languageChangeTime, isSearching, setIsSearching }
      },
      my: {
        id: 'my',
        title: i18n2.t('DiscoverUI.My'),
        component: MyStudyList
      }
    }),
    [isSearching, setIsSearching, languageChangeTime]
  );

  // Configure active tabs based on feature flags
  const activeTabs = useMemo(() => {
    const tabs = [];

    if (bibleModeEnabled) {
      tabs.push(allTabs['bible']);
    }

    tabs.push(allTabs['programs']);

    if (channelModeEnabled) {
      tabs.push(allTabs['channels']);
    }

    tabs.push(allTabs['my']);

    return tabs;
  }, [allTabs, bibleModeEnabled, channelModeEnabled]);

  // Ensure page is valid when tabs change
  useEffect(() => {
    if (page >= activeTabs.length) {
      setPage(0);
    }
  }, [activeTabs.length, page]);

  useEffect(() => {
    const onHomeTabClicked = () => {
      setPage((prevPage) => (prevPage + 1) % activeTabs.length);
    };

    const onBibleModeChanged = (enabled: boolean) => {
      setBibleModeEnabled(enabled);
    };

    const onChannelModeChanged = (enabled: boolean) => {
      setChannelModeEnabled(enabled);
    };

    globalThis.eventEmitter.addListener('homeTabClicked', onHomeTabClicked);
    globalThis.eventEmitter.addListener('bibleModeChanged', onBibleModeChanged);
    globalThis.eventEmitter.addListener('channelModeChanged', onChannelModeChanged);

    return () => {
      globalThis.eventEmitter.removeListener('homeTabClicked', onHomeTabClicked);
      globalThis.eventEmitter.removeListener('bibleModeChanged', onBibleModeChanged);
      globalThis.eventEmitter.removeListener('channelModeChanged', onChannelModeChanged);
    };
  }, [activeTabs.length]);
  // Get current tab component
  const CurrentTabComponent = activeTabs[page]?.component;
  const currentTabProps = activeTabs[page]?.props || {};

  // Check if current tab is channels to update last seen class
  useEffect(() => {
    if (activeTabs[page]?.id === 'channels') {
      setUserLastSeenClassAsync();
    }
  }, [activeTabs, page, setUserLastSeenClassAsync]);

  return (
    <View style={{ flex: 1, paddingLeft: insets.left, paddingRight: insets.right }}>
      <LogoBlock visible={!isSearching} />
      <NavTabs
        style={{
          position: 'absolute',
          top: 60,
          display: isSearching ? 'none' : 'flex',
          marginTop: insets.top + 7,
          width: '100%',
          zIndex: 2,
          height: 50,
          gap: 4
        }}
        itemWidth={activeTabs.length ? (windowWidth - (activeTabs.length + 1) * 4) / activeTabs.length : 92}
        current={page}
        onChange={(p) => setPage(p)}
        options={activeTabs.map((tab) => ({ title: tab?.title || '' }))}
      />
      {CurrentTabComponent && <CurrentTabComponent {...currentTabProps} />}
    </View>
  );
};

export default HomeScreen;
