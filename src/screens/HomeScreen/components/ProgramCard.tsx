import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { IProgram } from '@/utils/channel/IChannel';
import React from 'react';
import moment from 'moment';

interface IProgramCardProps extends IProgram {
  imageHeight: number;
  width: number;
  itemMargin: number;
  index: number;
  onClick?: () => void;
  getPreviewUrl: (channelId: number, v: string, programId?: number) => string;
}

const ProgramCard: React.FC<IProgramCardProps> = ({
  imageHeight = 140,
  width,
  title,
  createTime,
  getPreviewUrl,
  onClick,
  cover,
  itemMargin,
  channelId,
  programId,
  index,
  views,
  likeCount,
  collect,
  isLiked,
  isCollected
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.card,
        {
          width,
          marginLeft: index % 2 ? itemMargin / 2 : itemMargin,
          marginBottom: itemMargin
        }
      ]}
      onPress={onClick}
      activeOpacity={0.7}>
      <Image
        source={{
          uri: getPreviewUrl(channelId, cover || '', programId)
        }}
        style={[styles.image, { height: imageHeight }]}
      />
      <View style={styles.info}>
        <Text style={styles.title} numberOfLines={2}>
          {title}
        </Text>
        <View style={styles.bottomContainer}>
          <View style={styles.stats}>
            <View style={styles.statItem}>
              <Image source={require('@/assets/images/channel/icon_view.png')} style={styles.icon} />
              <Text style={styles.statText}>{views || 0}</Text>
            </View>
            <View style={styles.statItem}>
              <Image
                source={
                  isLiked
                    ? require('@/assets/images/channel/icon-Like2.png')
                    : require('@/assets/images/channel/icon-Like.png')
                }
                style={styles.icon}
              />
              <Text style={styles.statText}>{likeCount || 0}</Text>
            </View>
            <View style={styles.statItem}>
              <Image
                source={
                  isCollected
                    ? require('@/assets/images/channel/icon-MyCollections2.png')
                    : require('@/assets/images/channel/icon-MyCollections.png')
                }
                style={styles.icon}
              />
              <Text style={styles.statText}>{collect || 0}</Text>
            </View>
          </View>
          <Text style={styles.time}>{moment(createTime * 1000).format('YYYY-MM-DD')}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#ffffff',
    borderRadius: 10
  },
  image: {
    width: '100%',
    resizeMode: 'cover'
  },
  info: {
    padding: 10
  },
  title: {
    paddingHorizontal: 4,
    fontSize: 16,
    lineHeight: 18,
    marginTop: 4,
    fontWeight: 'bold'
  },
  bottomContainer: {
    paddingHorizontal: 4,
    marginTop: 4
  },
  stats: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4 // 加点间距
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10
  },
  statText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 2
  },
  time: {
    fontSize: 12,
    color: '#999'
  },
  icon: {
    width: 16,
    height: 16,
    resizeMode: 'contain',
    marginRight: 2
  }
});

export default ProgramCard;
