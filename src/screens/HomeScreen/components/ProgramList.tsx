import { ActivityIndicator, Keyboard, View } from 'react-native';
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';

import { AppContext } from '@/context/AppContext';
import { IProgram } from '@/utils/channel/IChannel';
import MasonryList from '@react-native-seoul/masonry-list';
import ProgramCard from '@/screens/HomeScreen/components/ProgramCard';
import SearchBar from '@/components/SearchBar';
import { getChannelImageUrl } from '@/utils/url';
import { useColumn } from '@/utils/lesson/Column';
import { useNavigation } from '@react-navigation/native';

type TMode = 'recommend' | 'like' | 'collect';

interface IProgramList {
  mode: TMode;
  count?: number;
  ListHeaderComponent?: React.ReactNode;
  isSearching?: boolean;
  setIsSearching?: (v: boolean) => void;
}

const ProgramList: React.FC<IProgramList> = ({
  ListHeaderComponent,
  count,
  mode = 'recommend',
  isSearching,
  setIsSearching
}) => {
  const { itemMargin, itemWidth, columns } = useColumn();
  const { navigate } = useNavigation<any>();
  const context = useContext(AppContext) as any;

  const [programs, setPrograms] = useState<IProgram[]>([]);
  const [initialLoading, setInitialLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const searchTextRef = useRef('');
  const nextTokenRef = useRef<string | null>(null);
  const loadingRef = useRef(false);
  const isEndRef = useRef(false);

  const getPrograms = useCallback(
    async (isRefresh = false) => {
      if ((!isRefresh && loadingRef.current) || (!isRefresh && isEndRef.current)) {
        return;
      }

      loadingRef.current = true;

      const requestObj: Record<TMode, any> = {
        recommend: (nextToken: string | null) =>
          globalThis.dsObject.getProgramsRecommend(nextToken, searchTextRef.current),
        like: (nextToken: string | null) =>
          globalThis.dsObject.getUserProgramsByType('like', nextToken, searchTextRef.current),
        collect: (nextToken: string | null) =>
          globalThis.dsObject.getUserProgramsByType('collect', nextToken, searchTextRef.current)
      };

      try {
        // Reset nextToken and programs if refreshing
        if (isRefresh) {
          nextTokenRef.current = null;
          isEndRef.current = false;
        }

        const resp = await requestObj[mode](nextTokenRef.current);
        if (resp?.body) {
          const { programs: newPrograms, nextToken } = resp.body;
          nextTokenRef.current = nextToken;
          if (!nextToken) {
            isEndRef.current = true;
          }

          setPrograms((prev) => {
            const updatedPrograms =
              newPrograms?.map(({ imageHeight, imageWidth, ...item }: IProgram) => ({
                imageHeight: imageHeight || 300,
                imageWidth: imageWidth || 300,
                ...item
              })) || [];

            return isRefresh ? updatedPrograms : [...prev, ...updatedPrograms];
          });
        }
      } catch (error) {
        console.error('getPrograms error:', error);
      } finally {
        loadingRef.current = false;
        setInitialLoading(false);
        if (isRefresh) {
          setRefreshing(false);
        }
      }
    },
    [mode]
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await getPrograms(true);
  };

  useEffect(() => {
    getPrograms();
  }, [getPrograms]);

  if (initialLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size='large' color='#999' />
      </View>
    );
  }

  return (
    <MasonryList
      data={count ? programs.slice(0, count) : programs}
      renderItem={({ item, i }: any) => {
        const itemHeight = (item.imageHeight / item.imageWidth) * itemWidth;
        return (
          <ProgramCard
            getPreviewUrl={getChannelImageUrl}
            onClick={() => {
              navigate('ChannelProgram', {
                channelId: item.channelId,
                programId: item.programId
              });
            }}
            {...item}
            imageHeight={itemHeight}
            width={itemWidth}
            itemMargin={itemMargin}
            index={i}
          />
        );
      }}
      onRefresh={onRefresh}
      refreshing={refreshing}
      containerStyle={{
        paddingHorizontal: context.insets.left,
        backgroundColor: '#f8f8f8'
      }}
      ListHeaderComponent={
        <>
          {setIsSearching && (
            <View
              style={{
                paddingTop: isSearching ? context.insets.top + 20 : context.insets.top + 85 + 40,
                paddingBottom: 10
              }}>
              <SearchBar
                autoFocus={false}
                value={isSearching ? undefined : ''}
                isCloseVisible={!!isSearching}
                onChangeText={(text) => {
                  searchTextRef.current = text;
                }}
                onSubmitEditing={() => {
                  loadingRef.current = false;
                  isEndRef.current = false;
                  nextTokenRef.current = null;
                  getPrograms(true);
                }}
                onFocus={() => {
                  setIsSearching?.(true);
                }}
                onClose={() => {
                  Keyboard.dismiss();
                  searchTextRef.current = '';
                  setIsSearching?.(false);
                  loadingRef.current = false;
                  isEndRef.current = false;
                  nextTokenRef.current = null;
                  getPrograms(true);
                }}
              />
            </View>
          )}

          {ListHeaderComponent ? ListHeaderComponent : <View style={{ height: 0 }} />}
        </>
      }
      ListFooterComponent={<View style={{ height: 20 }} />}
      keyExtractor={(item) => item.id}
      numColumns={columns}
      onEndReached={() => {
        if (!count) {
          getPrograms();
        }
      }}
      onEndReachedThreshold={0.2}
    />
  );
};

export default ProgramList;
