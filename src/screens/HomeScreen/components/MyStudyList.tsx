/* eslint-disable @typescript-eslint/no-explicit-any */
import { ScrollView, Text, TouchableOpacity, View, StyleSheet } from 'react-native';
import Title from '@/screens/HomeScreen/components/Title';
import { ClassCover } from '@/components/ClassCover';
import { goToLesson } from '@/utils/lesson/GoToLesson';
import React, { useContext, useEffect, useState } from 'react';
import { useColumn } from '@/utils/lesson/Column';
import { useNavigation } from '@react-navigation/native';
import { cloneDeep } from 'lodash-es';
import { AppContext } from '@/context/AppContext';
import { getCurrentUser } from '@/utils/user';
import { IChannel } from '@/utils/channel/IChannel';
import { Image } from 'expo-image';
import { getFontSize } from '@/utils/getFontSize';
import { getChannelImageUrl } from '@/utils/url';
import { Ionicons } from '@expo/vector-icons';
import { i18n2 } from '@/utils/i18n2';

const MyStudyList = () => {
  const { itemMargin, itemWidth } = useColumn();
  const navigation = useNavigation<any>();
  const context = useContext(AppContext) as any;
  const classes = cloneDeep(context.classes) as any[];
  const classAccessTime = getCurrentUser().getProperty('classOrder') || {};
  const myClasses = classes
    .filter((it) => it.isMyClass)
    .sort((a, b) => (classAccessTime[b.name] || 0) - (classAccessTime[a.name] || 0));

  const [channels, setChannels] = useState<IChannel[]>([]);

  const initData = async () => {
    const [ownedResp, subscribesResp] = await Promise.all([
      global.dsObject.getChannelOwned(),
      global.dsObject.getChannelSubscribes()
    ]);
    const owned = ownedResp?.body || [];
    const subscribes = subscribesResp?.body || [];

    const allChannelsMap = new Map<string, IChannel>();
    [...owned, ...subscribes].forEach((channel: IChannel) => {
      allChannelsMap.set(channel.channelId.toString(), channel);
    });
    setChannels(Array.from(allChannelsMap.values()));
  };

  useEffect(() => {
    initData();
  }, []);

  return (
    <ScrollView>
      <View style={[styles.container, { paddingTop: context.insets.top + 85 + 40 }]}>
        <View
          style={{
            paddingHorizontal: itemMargin,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: itemMargin
          }}>
          <Title title={i18n2.t('HomeScreen.MyStudyTitle')} />
          {myClasses.length > 4 && (
            <TouchableOpacity
              onPress={() => navigation.navigate('MyStudyClassList')}
              style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={{ fontSize: getFontSize().smallFontSize, color: '#999' }}>{i18n2.t('HomeScreen.More')}</Text>
              <Ionicons name='chevron-forward' size={16} color='#999' />
            </TouchableOpacity>
          )}
        </View>

        <View style={{ flexDirection: 'row', flexWrap: 'wrap', paddingHorizontal: itemMargin }}>
          {myClasses.slice(0, 4).map((item) => (
            <View
              key={item.classId}
              style={{ width: itemWidth, paddingHorizontal: itemMargin / 2, marginBottom: itemMargin }}>
              <ClassCover
                size={itemWidth - itemMargin}
                margin={0}
                classId={item.classId}
                text={item.classNameChs}
                unreadCount={item.newMessageCount}
                onPress={() => goToLesson(item, navigation.navigate)}
              />
            </View>
          ))}
        </View>

        {/* 我的频道 */}
        <View
          style={{
            paddingHorizontal: itemMargin,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: itemMargin,
            marginBottom: itemMargin
          }}>
          <Title title={i18n2.t('HomeScreen.MyChannelsTitle')} />
          {channels?.length > 4 && (
            <TouchableOpacity
              onPress={() => navigation.navigate('MyChannelList')}
              style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={{ fontSize: getFontSize().smallFontSize, color: '#999' }}>{i18n2.t('HomeScreen.More')}</Text>
              <Ionicons name='chevron-forward' size={16} color='#999' />
            </TouchableOpacity>
          )}
        </View>

        <View style={{ flexDirection: 'row', flexWrap: 'wrap', paddingHorizontal: itemMargin }}>
          {channels.slice(0, 4).map((channel) => (
            <TouchableOpacity
              key={channel.channelId}
              style={{
                width: itemWidth,
                paddingHorizontal: itemMargin / 2,
                marginBottom: itemMargin,
                alignItems: 'center'
              }}
              onPress={() =>
                navigation.navigate('ChannelDetail', {
                  channelId: channel.channelId,
                  channelName: channel.name
                })
              }>
              <Image
                source={{ uri: getChannelImageUrl(channel.channelId, 'cover.jpg') }}
                style={{
                  width: itemWidth - itemMargin * 2,
                  height: itemWidth - itemMargin * 2,
                  borderRadius: (itemWidth - itemMargin) / 2,
                  marginBottom: 6,
                  backgroundColor: '#eee'
                }}
              />
              <Text style={styles.channelName} numberOfLines={1}>
                {channel.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </ScrollView>
  );
};

export default MyStudyList;

const styles = StyleSheet.create({
  container: {
    paddingBottom: 20
  },
  channelName: {
    fontSize: getFontSize().mediumFontSize,
    color: '#333'
  }
});
