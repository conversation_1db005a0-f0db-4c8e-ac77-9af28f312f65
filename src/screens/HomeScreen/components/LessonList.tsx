import { cloneDeep } from 'lodash-es';
import { getCurrentUser } from '@/utils/user';
import * as Device from 'expo-device';
import { FlatList, Keyboard, Platform, RefreshControl, Text, View } from 'react-native';
import { i18n2 } from '@/utils/i18n2';
import SearchBar from '@/components/SearchBar';
import { ClassCover } from '@/components/ClassCover';
import { Button } from '@/components';
import React, { useContext, useMemo, useState } from 'react';
import { AppContext } from '@/context/AppContext';
import { useNavigation } from '@react-navigation/native';
import Title from '@/screens/HomeScreen/components/Title';
import { useColumn } from '@/utils/lesson/Column';
import { goToLesson } from '@/utils/lesson/GoToLesson';

interface ILessonList {
  mode: number;
  canShowReload?: boolean;
  isSearching: boolean;
  setIsSearching: (v: boolean) => void;
}
const LessonList: React.FC<ILessonList> = ({ mode, canShowReload, isSearching, setIsSearching }) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const context = useContext(AppContext) as any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const classes = cloneDeep(context.classes) as any[];
  const [searchText, setSearchText] = useState<string>('');
  const [refreshing, setRefreshing] = useState<boolean>(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { navigate } = useNavigation<any>();
  const { columns, itemWidth: bookWidth, itemMargin: bookMargin } = useColumn();

  // sort classes
  const [sortedClasses, lastClasses] = useMemo(() => {
    if (searchText?.trim()) {
      return [
        classes.filter((item) => {
          return item.classNameChs.toLowerCase().includes(searchText.toLowerCase());
        })
      ];
    } else {
      switch (mode) {
        case 0: {
          // sort by popularity
          // sort by latest first (only show top 6)
          return [classes, [...classes].sort((a, b) => b.lastModified - a.lastModified).slice(0, 6)];
        }
        case 1:
          return [classes];
        case 2: {
          // sort by my-popularity
          const classAccessTime = getCurrentUser().getProperty('classOrder') || {};
          return [
            classes
              .filter((it) => it.isMyClass)
              .sort((a, b) => (classAccessTime[b.name] || 0) - (classAccessTime[a.name] || 0))
          ];
        }
        default:
          return [];
      }
    }
  }, [classes, searchText, mode]);

  return classes.length ? (
    <View
      style={{
        flex: 1,
        paddingLeft: context.insets.left,
        paddingRight: context.insets.right,
        zIndex: 1
      }}>
      {Platform.OS === 'android' && Device.platformApiLevel && Device.platformApiLevel <= 23 ? (
        <Text
          style={{
            textAlign: 'center',
            fontSize: getCurrentUser().getMediumFontSize(),
            color: 'red',
            margin: 7
          }}>
          {i18n2.t('Common.AndroidVerTooLow')}
        </Text>
      ) : null}
      {Array.isArray(sortedClasses) ? (
        <FlatList
          numColumns={columns}
          ListHeaderComponent={
            <>
              <View
                style={{
                  paddingTop: isSearching ? context.insets.top + 20 : context.insets.top + 85 + 40,
                  paddingBottom: 10
                }}>
                <SearchBar
                  autoFocus={false}
                  value={searchText}
                  isCloseVisible={isSearching}
                  onChangeText={(text) => setSearchText(text)}
                  onFocus={() => {
                    setIsSearching(true);
                  }}
                  onClose={() => {
                    Keyboard.dismiss();
                    setSearchText('');
                    setIsSearching(false);
                  }}
                />
              </View>
              {lastClasses?.length && (
                <>
                  <Title title={i18n2.t('HomeScreen.LatestReleases')} style={{ margin: bookMargin }} />
                  <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                    {lastClasses.map((item) => (
                      <ClassCover
                        key={`last_${item.classId}`}
                        size={bookWidth}
                        margin={bookMargin}
                        classId={item.classId}
                        text={item.classNameChs}
                        unreadCount={item.newMessageCount}
                        onPress={() => goToLesson(item, navigate)}
                      />
                    ))}
                  </View>
                  <Title title={i18n2.t('HomeScreen.MoreExciting')} style={{ margin: bookMargin }} />
                </>
              )}
            </>
          }
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              title='Pull to refresh'
              titleColor='#000'
              tintColor='#000'
              onRefresh={async () => {
                setRefreshing(true);
                await context.checkUserUpdateAsync();
                setRefreshing(false);
              }}
            />
          }
          scrollEventThrottle={16}
          data={sortedClasses}
          renderItem={({ item }) => {
            return (
              <ClassCover
                key={item.classId}
                size={bookWidth}
                margin={bookMargin}
                classId={item.classId}
                text={item.classNameChs}
                unreadCount={item.newMessageCount}
                onPress={() => goToLesson(item, navigate)}
              />
            );
          }}
        />
      ) : null}
      {canShowReload && (!Array.isArray(sortedClasses) || sortedClasses.length === 0) && !isSearching ? (
        <View style={{ flex: 1, alignItems: 'center' }}>
          <Button
            title={i18n2.t('Common.Reload')}
            onPress={() => {
              context.checkUserUpdateAsync();
            }}
          />
        </View>
      ) : null}
    </View>
  ) : null;
};

export default LessonList;
