import { StyleSheet, Text, TouchableOpacity, View, Alert, ImageBackground, Dimensions } from 'react-native';
import React, { useCallback, useContext, useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { getObjectAsync, setObjectAsync } from '@/dataStorage/localStorage';
import {
  getVerseInfoByVerseId,
  IVerseInfo,
  getDefaultTranslation,
  downloadAndStoreBible,
  isBibleVersionDownloaded
} from '@/utils/bibleUtils';
import { AppContext } from '@/context/AppContext';
import { i18n2 } from '@/utils/i18n2';

const BibleGuide: React.FC = () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { navigate } = useNavigation<any>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const context = useContext<any>(AppContext);
  const [currentVerseInfo, setCurrentVerseInfo] = useState<IVerseInfo>();
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);

  // Wrap initBibleConfig in useCallback to avoid recreation on every render
  const checkAndDownloadDefaultBible = useCallback(async () => {
    try {
      const defaultTranslation = getDefaultTranslation();

      const isDownloaded = await isBibleVersionDownloaded(defaultTranslation);
      if (!isDownloaded) {
        setIsDownloading(true);
        setDownloadProgress(0);

        const progressInterval = setInterval(() => {
          setDownloadProgress((prev) => {
            const newProgress = prev + 0.05;
            return newProgress >= 0.95 ? 0.95 : newProgress;
          });
        }, 300);

        const success = await downloadAndStoreBible(defaultTranslation);

        clearInterval(progressInterval);
        setDownloadProgress(1);

        if (!success) {
          Alert.alert(
            i18n2.t('HomeScreen.DownloadFailed'),
            i18n2.tValue?.('HomeScreen.DownloadFailedMsg', defaultTranslation)
          );
        }
        // 延迟一下再隐藏下载状态
        setTimeout(() => {
          setIsDownloading(false);
          setDownloadProgress(0);
        }, 1000);
      }
    } catch (error) {
      console.error('Error checking/downloading Bible:', error);
      setIsDownloading(false);
      setDownloadProgress(0);
      Alert.alert(i18n2.t('HomeScreen.Error'), i18n2.t('HomeScreen.ErrorCheckingBible'));
    }
  }, []);

  const initBibleConfig = useCallback(async () => {
    const verseStorage = await getObjectAsync('bible.currentVerse');
    const settingsStorage = await getObjectAsync('bible.settings');

    const defaultTranslation = getDefaultTranslation();
    if (!verseStorage) {
      await setObjectAsync('bible.settings', { versions: [defaultTranslation] });
      await setObjectAsync('bible.currentVerse', { verseId: 1001001 });
      setCurrentVerseInfo(getVerseInfoByVerseId(1001001, defaultTranslation));
    } else {
      const translations = Array.isArray(settingsStorage)
        ? settingsStorage
        : settingsStorage?.versions || [defaultTranslation];
      setCurrentVerseInfo(getVerseInfoByVerseId(verseStorage.verseId, translations[0]));
    }

    await checkAndDownloadDefaultBible();
  }, [checkAndDownloadDefaultBible]);

  // Use the wrapped function directly in useFocusEffect
  const initBibleConfigEffect = useCallback(() => {
    initBibleConfig();
  }, [initBibleConfig]);

  // 使用useFocusEffect监听页面回到homeScreen时更新currentVerseInfo
  useFocusEffect(initBibleConfigEffect);

  return (
    <>
      {isDownloading && (
        <View style={styles.downloadOverlay}>
          <View style={styles.downloadContainer}>
            <Text style={styles.downloadText}>{i18n2.t('HomeScreen.DownloadingBible')}</Text>
            <View style={styles.progressBarContainer}>
              <View style={[styles.progressBar, { width: `${downloadProgress * 100}%` }]} />
            </View>
            <Text style={styles.downloadPercentage}>{Math.round(downloadProgress * 100)}%</Text>
          </View>
        </View>
      )}
      <View style={[styles.container, { paddingTop: context.insets.top + 85 + 40 }]}>
        <ImageBackground
          source={require('@/assets/images/cover-Bible.jpg')}
          style={styles.backgroundContainer}
          resizeMode='cover'>
          {/* 标题 */}
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{i18n2.t('HomeScreen.MyBible')}</Text>
          </View>
          <View style={styles.buttonsContainer}>
            <TouchableOpacity style={styles.circleButton} onPress={() => navigate('BibleBooks')}>
              <Text style={styles.circleButtonText}>{i18n2.t('HomeScreen.BibleBooks')}</Text>
              <Text style={styles.circleButtonSubtext}>{i18n2.t('HomeScreen.BibleCatalog')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.circleButton}
              onPress={() =>
                navigate('BibleChapter', {
                  bookId: currentVerseInfo?.bookId,
                  bookName: currentVerseInfo?.bookName,
                  chapter: currentVerseInfo?.chapter
                })
              }>
              <Text style={styles.circleButtonText}>{i18n2.t('HomeScreen.CurrentLocation')}</Text>
              <Text style={styles.circleButtonSubtext}>
                {currentVerseInfo?.bookName
                  ? `${currentVerseInfo?.bookName}${currentVerseInfo?.chapter}:${currentVerseInfo?.verse}`
                  : '--'}
              </Text>
            </TouchableOpacity>
          </View>
        </ImageBackground>
      </View>
    </>
  );
};

export default BibleGuide;

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  backgroundContainer: {
    width: '100%',
    height: Dimensions.get('window').width / 2
  },
  titleContainer: {
    alignItems: 'center',
    marginTop: 40
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    paddingHorizontal: 20
  },
  circleButton: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.85)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5
  },
  circleButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center'
  },
  circleButtonSubtext: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 5,
    color: '#666'
  },
  downloadOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000
  },
  downloadContainer: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center'
  },
  downloadText: {
    fontSize: 16,
    marginBottom: 15,
    fontWeight: 'bold'
  },
  progressBarContainer: {
    width: '100%',
    height: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 5,
    overflow: 'hidden'
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#4CAF50'
  },
  downloadPercentage: {
    marginTop: 10,
    fontSize: 14
  }
});
