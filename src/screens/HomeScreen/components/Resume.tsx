import { Dimensions, Text, TouchableOpacity, View } from 'react-native';
import { Colors } from '@/styles/colors';
import { getCurrentUser } from '@/utils/user';
import { AntDesign } from '@expo/vector-icons';
import React, { useContext } from 'react';
import { CommonActions, useNavigation } from '@react-navigation/native';
import { AppContext } from '@/context/AppContext';

const Resume = () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { dispatch } = useNavigation<any>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const context = useContext(AppContext) as any;
  const { currentProgress } = context;
  const bookTitle = currentProgress?.LessonScreen?.bookTitle;
  const canResumeLesson = !!bookTitle;
  if (!canResumeLesson) {
    return <></>;
  }

  const screenWidth = Dimensions.get('window').width - context.insets.left - context.insets.right;
  const buttonWidth = screenWidth / 3;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const resumeLesson = (lessonScreenParams: any) => {
    const lesson = lessonScreenParams.lesson;
    if (!lesson) {
      alert('Error lesson');
      return;
    }

    // replace navigation stack
    dispatch(
      CommonActions.reset({
        index: 2,
        routes: [
          { name: 'BottomTab' },
          {
            name: 'LessonTableOfContent',
            params: { lesson }
          },
          {
            name: 'Lesson',
            params: lessonScreenParams
          }
        ]
      })
    );
  };

  return (
    <View
      style={{
        position: 'absolute',
        width: Dimensions.get('window').width,
        alignItems: 'center',
        justifyContent: 'center',
        bottom: 9
      }}>
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => {
          resumeLesson(currentProgress.LessonScreen);
        }}>
        <View
          style={{
            width: buttonWidth,
            borderRadius: 20,
            borderColor: Colors.buttonBorder,
            borderWidth: 1,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: Colors.lightBlue,
            flexDirection: 'row'
          }}>
          <View style={{ width: buttonWidth - 38, alignItems: 'center' }}>
            <Text
              style={{
                fontSize: getCurrentUser().getXSmallFontSize()
              }}
              numberOfLines={1}>
              {bookTitle}
            </Text>
          </View>
          <AntDesign name={'playcircleo'} size={22} color={'black'} />
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default Resume;
