import { Text, View, ViewStyle } from 'react-native';
import React from 'react';
import { Colors } from '@/styles/colors';
import { getFontSize } from '@/utils/getFontSize';

interface ITitle {
  title: string;
  style?: ViewStyle;
}

const Title: React.FC<ITitle> = ({ title, style = {} }) => {
  const { largeFontSize } = getFontSize();
  return (
    <View style={{ flexDirection: 'row', gap: 10, alignItems: 'center', ...style }}>
      <View style={{ width: 14, height: 14, backgroundColor: Colors.text }} />
      <Text style={{ fontSize: largeFontSize, color: Colors.text, fontWeight: 'bold' }}>{title}</Text>
    </View>
  );
};

export default Title;
