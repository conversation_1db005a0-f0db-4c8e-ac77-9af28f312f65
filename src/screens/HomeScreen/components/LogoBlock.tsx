import { View } from 'react-native';
import { Image } from 'expo-image';
import { NavBarButton } from '@/components/navigation/NavigationButtons';
import React, { useContext } from 'react';
import { AppContext } from '@/context/AppContext';
import { useNavigation } from '@react-navigation/native';

interface ILogoBlock {
  visible?: boolean;
}

const LogoBlock: React.FC<ILogoBlock> = ({ visible }) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { insets } = useContext(AppContext) as any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { navigate } = useNavigation<any>();
  return (
    <View
      style={{
        position: 'absolute',
        top: 0,
        display: visible ? 'flex' : 'none',
        width: '100%',
        overflow: 'hidden',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: insets.top + 7,
        zIndex: 2
      }}>
      <Image
        style={{ height: 56, width: '40%' }}
        contentFit={'contain'}
        source={require('@/assets/images/splash.png')}
      />
      <View
        style={{
          position: 'absolute',
          height: 56,
          right: 0,
          top: 0,
          justifyContent: 'center'
        }}>
        <NavBarButton
          backgroundColor='transparent'
          image='helpme'
          onPress={() => {
            navigate('HelpMe');
          }}
        />
      </View>
    </View>
  );
};

export default LogoBlock;
