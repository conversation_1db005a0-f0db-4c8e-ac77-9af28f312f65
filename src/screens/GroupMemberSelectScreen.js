import { AppContext } from '@/context/AppContext';
import { Avatar2 } from '@/components/Avatar2';
import { Dimensions } from 'react-native';
import { KeyboardView } from '@/components';
import { LoadingIndicator } from '@/components/LoadingIndicator';
import React from 'react';
import { View } from 'react-native';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { sortByPinYin } from '@/utils/pinyin';

class GroupMemberSelectScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ navigation, route }) => ({
    title: i18n2.t('GroupMemberSelectScreen.Title')
  });

  constructor(props) {
    super(props);

    const params = props.route.params;
    this.skipUserIds = Array.isArray(params.skipUserIds) ? params.skipUserIds : [];
    this.AddUsers = Array.isArray(params.AddUsers) ? params.AddUsers : [];
    this.group = params.group;
    this.onSelect = params.onSelect;
    this.onCancel = params.onCancel;
    this.canceled = true;
    this.groupUsers = [];

    this.state = {
      loading: true,
      users: []
    };
  }

  componentDidMount() {
    if (!this.group || !this.group.groupId) {
      this.props.navigation.pop();
      return;
    }

    this.loadAsync();
  }

  componentWillUnmount() {
    if (this.canceled) {
      this.onCancel();
    }
  }

  async loadAsync() {
    const result = await globalThis.dsObject.getGroup(this.group.groupId);
    if (!result) {
      this.props.navigate.pop();
      return;
    }

    this.groupUsers = sortByPinYin(result.body.users, (item) => (item.isGroupLeader ? '0' : '1') + item.name);
    this.setState({ users: [...this.AddUsers, ...this.groupUsers], loading: false });
  }

  render() {
    const { users, loading } = this.state;
    if (loading) {
      return (
        <View
          style={{
            flex: 1,
            alignItems: 'center'
          }}>
          <LoadingIndicator />
        </View>
      );
    }

    let keyIndex = 0;
    const avatarSize = 70;
    const width = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right - 25;
    return (
      <KeyboardView>
        <View
          style={{
            flex: 1,
            paddingLeft: this.context.insets.left,
            paddingRight: this.context.insets.right,
            paddingBottom: this.context.insets.bottom,
            alignItems: 'center'
          }}>
          <View
            style={{
              marginTop: 7,
              flexDirection: 'row',
              flexWrap: 'wrap',
              width,
              paddingLeft: (width % avatarSize) / 2
            }}>
            {users.map((user) =>
              this.skipUserIds.indexOf(user.userId) !== -1 ? (
                <></>
              ) : (
                <View
                  key={keyIndex++}
                  style={{
                    width: avatarSize,
                    marginTop: 5,
                    marginBottom: 25,
                    alignItems: 'center'
                  }}>
                  <Avatar2
                    userId={user.userId}
                    userName={user.name}
                    userNameStyles={{
                      fontSize: getCurrentUser().getXSmallFontSize(),
                      marginTop: 2,
                      textDecorationLine: user.isGroupLeader ? 'underline' : 'none'
                    }}
                    size={50}
                    onPress={() => {
                      if (user.isAllUser) {
                        user.users = this.groupUsers;
                      }
                      this.onSelect(user);
                      this.canceled = false;
                      this.props.navigation.pop();
                    }}
                  />
                </View>
              )
            )}
          </View>
        </View>
      </KeyboardView>
    );
  }
}

export default GroupMemberSelectScreen;
