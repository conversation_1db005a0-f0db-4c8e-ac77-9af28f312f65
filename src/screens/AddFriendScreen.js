import { Alert, Dimensions, Text, TextInput, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import { Input } from '@rneui/themed';
import { KeyboardView } from '@/components';
import { OkButton } from '@/components/navigation/NavigationButtons';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { getInputContainerStyle } from '@/styles/styles';
import { i18n2 } from '@/utils/i18n2';
import { isEmailOrUserUniqueIdValid } from '@/utils/helper';

export default class AddFriendScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ route }) => ({
    title: i18n2.t('AddFriend'),
    headerRight: () =>
      typeof route.params.onSave === 'function' ? (
        <OkButton
          right={true}
          onPress={() => {
            route.params.onSave();
          }}
        />
      ) : null
  });

  constructor(props) {
    super(props);

    this.state = {
      email: '',
      greetings: ''
    };
  }

  onSave = async () => {
    const greetings = this.state.greetings.trim();
    const email = this.state.email.trim();
    const inviteResult = await globalThis.dsObject.inviteUser(email, greetings);
    if (!inviteResult) {
      return;
    }

    if (inviteResult.body.emailSent) {
      Alert.alert(i18n2.t('Common.Information'), i18n2.t('AddFriendScreen.EmailNotUser').replace('{email}', email), [
        {
          text: i18n2.t('Common.Ok'),
          onPress: () => {
            this.props.navigation.pop();
          }
        }
      ]);
      return;
    }

    // create one-on-one group
    const createGroupResult = await globalThis.dsObject.createOneOnOne(inviteResult.body.userId);
    if (!createGroupResult) {
      return;
    }

    // on friend added, send greetings if provided
    if (greetings) {
      globalThis.dsObject.sendMessage({
        room: createGroupResult.body.studyGroupId,
        message: greetings
      });
    }

    const onUpdated = this.props.route.params?.onUpdated;
    if (typeof onUpdated === 'function') {
      onUpdated();
    }

    // navigate to one-on-one chat
    setTimeout(() => {
      this.props.navigation.navigate('Chat', {
        chatId: createGroupResult.body.studyGroupId,
        title: createGroupResult.body.studyGroupName,
        subTitle: email,
        group: {
          groupId: createGroupResult.body.studyGroupId,
          isOneOnOneGroup: true,
          isGroupLeader: false
        },
        showGroupIcon: false,
        disableTagging: true,
        disableUserInfo: true,
        disableAtPeople: true,
        enableAudioChat: true
      });
    }, 300);

    this.props.navigation.pop();
  };

  render() {
    const { email, greetings } = this.state;
    const windowWidth = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right;
    return (
      <KeyboardView>
        <View style={{ flex: 1, alignItems: 'center' }}>
          <Text
            style={{
              fontSize: getCurrentUser().getSmallFontSize(),
              color: Colors.darkBlue,
              textAlign: 'center',
              margin: 20
            }}>
            {i18n2.t('AddFriendScreen.EnterFriendEmail')}
          </Text>

          <Input
            ref={(input) => {
              if (input) {
                this.emailInput = input;
              }
            }}
            inputContainerStyle={[
              getInputContainerStyle(Dimensions.get('window').width).inputContainerStyle,
              { borderColor: '#cccccc', width: windowWidth - 50 }
            ]}
            placeholder={i18n2.t('Common.Email')}
            autoCapitalize='none'
            autoCorrect={false}
            autoFocus={true}
            value={email}
            onChangeText={(text) => {
              this.setState({ email: text });
              this.props.navigation.setParams({
                onSave: isEmailOrUserUniqueIdValid(text.trim()) ? this.onSave : null
              });
            }}
            submitBehavior='submit'
            onSubmitEditing={() => {
              if (isEmailOrUserUniqueIdValid(email.trim())) {
                this.greetingsInput.focus();
              } else {
                this.emailInput.shake();
              }
            }}
          />

          <Text
            style={{
              fontSize: getCurrentUser().getSmallFontSize(),
              color: Colors.darkBlue,
              textAlign: 'center',
              margin: 20
            }}>
            {i18n2.t('AddFriendScreen.SelfIntro')}
          </Text>

          <TextInput
            ref={(input) => {
              if (input) {
                this.greetingsInput = input;
              }
            }}
            style={{
              borderColor: '#cccccc',
              width: windowWidth - 50,
              fontSize: getCurrentUser().getSmallFontSize(),
              marginHorizontal: 7,
              paddingHorizontal: 7,
              borderWidth: 1,
              textAlignVertical: 'top',
              height: 90
            }}
            autoCapitalize='none'
            multiline={true}
            value={greetings}
            onChangeText={(text) => {
              this.setState({ greetings: text });
            }}
          />
        </View>
      </KeyboardView>
    );
  }
}
