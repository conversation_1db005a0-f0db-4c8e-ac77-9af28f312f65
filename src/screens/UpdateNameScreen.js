import { Dimensions, Text, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import { Input } from '@rneui/themed';
import { KeyboardView } from '@/components';
import { OkButton } from '@/components/navigation/NavigationButtons';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { getInputContainerStyle } from '@/styles/styles';
import { i18n2 } from '@/utils/i18n2';

export default class UpdateNameScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = () => ({
    title: i18n2.t('UpdateNameScreen.Title'),
    headerRight: () => (
      <OkButton
        right={true}
        onPress={() => {
          globalThis.eventEmitter.emit('updateUserDisplayName');
        }}
      />
    )
  });

  constructor(props) {
    super(props);

    this.state = {
      displayName: getCurrentUser().getNickName()
    };

    this.listeners = [];
  }

  async componentDidMount() {
    this.listeners.push(globalThis.eventEmitter.addListener('updateUserDisplayName', this.setUserDisplayNameAsync));

    await this.context.loadUserProfileAsync();
    this.setState({ displayName: this.context.userProfile.displayName });
    this.displayNameInput.focus();
  }

  componentWillUnmount() {
    this.listeners.forEach((listener) => {
      listener.remove();
    });
  }

  setUserDisplayNameAsync = async () => {
    if (!this.state.displayName || this.state.displayName.length < 1) {
      this.displayNameInput.shake();
      this.displayNameInput.focus();
      return;
    }

    try {
      this.setState({ busy: true });

      await this.context.setUserProfileAsync({ displayName: this.state.displayName });

      this.props.navigation.pop();
    } finally {
      this.setState({ busy: false });
    }
  };

  render() {
    return (
      <KeyboardView>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center'
          }}>
          <Text
            style={{
              marginTop: 20,
              color: Colors.darkBlue,
              fontSize: getCurrentUser().getMediumFontSize()
            }}>
            {i18n2.t('DisplayName')}
          </Text>

          <Input
            inputContainerStyle={getInputContainerStyle(Dimensions.get('window').width).inputContainerStyle}
            contentContainerStyle={{ alignItems: 'center' }}
            ref={(input) => (this.displayNameInput = input)}
            defaultValue={this.state.displayName}
            onChangeText={(text) => {
              this.setState({ displayName: text });
            }}
          />
        </View>
      </KeyboardView>
    );
  }
}
