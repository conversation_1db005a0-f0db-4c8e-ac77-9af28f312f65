// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<UserLoginScreen /> English UI renders correctly 1`] = `
<RCTScrollView
  keyboardShouldPersistTaps="handled"
  showsVerticalScrollIndicator={false}
>
  <View>
    <View
      style={
        {
          "alignItems": "center",
          "backgroundColor": "#fff",
          "flex": 1,
          "justifyContent": "center",
        }
      }
    >
      <Text
        style={
          {
            "color": "#626262",
            "fontSize": 18,
            "marginTop": 10,
          }
        }
      >
        Email
      </Text>
      <View
        style={
          {
            "paddingHorizontal": 10,
            "width": "100%",
          }
        }
        testID="RNE__Input__view-wrapper"
      >
        <View
          collapsable={false}
          style={
            {
              "alignItems": "center",
              "alignSelf": "center",
              "borderBottomWidth": 1,
              "borderColor": "#626262",
              "borderWidth": 1,
              "flexDirection": "row",
              "marginTop": 7,
              "padding": 3,
              "transform": [
                {
                  "translateX": 0,
                },
              ],
              "width": 500,
            }
          }
        >
          <TextInput
            autoCapitalize="none"
            autoCorrect={false}
            autoFocus={true}
            defaultValue=""
            editable={true}
            onChangeText={[Function]}
            onSubmitEditing={[Function]}
            placeholder="Ex: <EMAIL>"
            placeholderTextColor="#86939e"
            returnKeyType="next"
            style={
              {
                "color": "#242424",
                "flex": 1,
                "fontSize": 18,
                "minHeight": 40,
              }
            }
            submitBehavior="submit"
            testID="EmailInputBox"
            underlineColorAndroid="transparent"
          />
        </View>
        <Text
          style={
            {
              "color": "#ff190c",
              "fontSize": 12,
              "height": 0,
              "margin": 5,
            }
          }
        >
          EmailError
        </Text>
      </View>
      <View
        style={
          {
            "marginTop": 17,
          }
        }
      />
      <Text
        style={
          {
            "color": "#626262",
            "fontSize": 18,
          }
        }
      >
        Password
      </Text>
      <View
        style={
          {
            "paddingHorizontal": 10,
            "width": "100%",
          }
        }
        testID="RNE__Input__view-wrapper"
      >
        <View
          collapsable={false}
          style={
            {
              "alignItems": "center",
              "alignSelf": "center",
              "borderBottomWidth": 1,
              "borderColor": "#626262",
              "borderWidth": 1,
              "flexDirection": "row",
              "marginTop": 7,
              "padding": 3,
              "transform": [
                {
                  "translateX": 0,
                },
              ],
              "width": 500,
            }
          }
        >
          <TextInput
            defaultValue=""
            editable={true}
            onChangeText={[Function]}
            onSubmitEditing={[Function]}
            placeholder="Password"
            placeholderTextColor="#86939e"
            returnKeyType="join"
            secureTextEntry={true}
            style={
              {
                "color": "#242424",
                "flex": 1,
                "fontSize": 18,
                "minHeight": 40,
              }
            }
            submitBehavior="submit"
            testID="RNE__Input__text-input"
            underlineColorAndroid="transparent"
          />
        </View>
        <Text
          style={
            {
              "color": "#ff190c",
              "fontSize": 12,
              "height": 0,
              "margin": 5,
            }
          }
        >
          PasswordError
        </Text>
      </View>
      <View
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessibilityValue={
          {
            "max": undefined,
            "min": undefined,
            "now": undefined,
            "text": undefined,
          }
        }
        accessible={true}
        collapsable={false}
        focusable={true}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "marginTop": 10,
            "opacity": 1,
          }
        }
      >
        <Text
          style={
            {
              "color": "#626262",
              "fontSize": 18,
              "fontWeight": "bold",
              "textDecorationLine": "underline",
            }
          }
        >
          Forgot Password?
        </Text>
      </View>
      <View
        style={
          {
            "marginTop": 7,
          }
        }
      >
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "backgroundColor": "#EFEFEF",
              "borderColor": "#707070",
              "borderRadius": 24,
              "borderWidth": 1,
              "marginVertical": 10,
              "opacity": 1,
              "padding": 10,
              "width": 375,
            }
          }
          testID="LoginButton"
        >
          <View>
            <Text
              numberOfLines={1}
              style={
                {
                  "color": "#505050",
                  "fontSize": 18,
                  "textAlign": "center",
                }
              }
            >
              Login
            </Text>
          </View>
        </View>
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "backgroundColor": "#EFEFEF",
              "borderColor": "#707070",
              "borderRadius": 24,
              "borderWidth": 1,
              "marginVertical": 10,
              "opacity": 1,
              "padding": 10,
              "width": 375,
            }
          }
          testID=""
        >
          <View>
            <Text
              numberOfLines={1}
              style={
                {
                  "color": "#505050",
                  "fontSize": 18,
                  "textAlign": "center",
                }
              }
            >
              Create New
            </Text>
          </View>
        </View>
        <View
          style={
            {
              "alignItems": "center",
              "marginVertical": 10,
            }
          }
        />
      </View>
      <View
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessibilityValue={
          {
            "max": undefined,
            "min": undefined,
            "now": undefined,
            "text": undefined,
          }
        }
        accessible={true}
        collapsable={false}
        focusable={true}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "opacity": 1,
          }
        }
      >
        <Text
          style={
            {
              "color": "#626262",
              "fontSize": 18,
              "fontWeight": "bold",
              "marginTop": 7,
              "textDecorationLine": "underline",
            }
          }
        >
          Change language/更改语言
        </Text>
      </View>
    </View>
    <View
      style={
        {
          "position": "absolute",
          "right": 2,
          "top": 7,
        }
      }
    >
      <Text
        style={
          {
            "fontSize": 5,
          }
        }
      >
        v
        (
        )
      </Text>
    </View>
  </View>
</RCTScrollView>
`;
