// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<MemberScreen /> renders correctly based on default states 1`] = `
<RCTScrollView
  contentContainerStyle={
    {
      "alignItems": "center",
      "justifyContent": "center",
      "width": "100%",
    }
  }
>
  <View>
    <View
      style={
        {
          "margin": 10,
        }
      }
    >
      <div>
        Avatar2
      </div>
    </View>
    <View
      style={
        {
          "alignItems": "center",
          "width": "100%",
        }
      }
    >
      <View
        style={
          {
            "alignItems": "center",
            "flexDirection": "row",
            "marginHorizontal": 10,
            "marginTop": 10,
            "width": "100%",
          }
        }
      >
        <Text
          style={
            [
              {
                "right": 5,
                "textAlign": "right",
                "width": "50%",
              },
              {
                "fontSize": 18,
                "lineHeight": 23.400000000000002,
              },
            ]
          }
        >
          ID
          :
        </Text>
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "left": 5,
              "opacity": 1,
              "width": "50%",
            }
          }
        >
          <Text
            style={
              {
                "fontSize": 18,
              }
            }
          >
            <EMAIL>
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "alignItems": "center",
            "flexDirection": "row",
            "marginHorizontal": 10,
            "marginTop": 10,
            "width": "100%",
          }
        }
      >
        <Text
          style={
            [
              {
                "right": 5,
                "textAlign": "right",
                "width": "50%",
              },
              {
                "fontSize": 18,
                "lineHeight": 23.400000000000002,
              },
            ]
          }
        >
          Display Name
          :
        </Text>
        <Text
          style={
            [
              {
                "left": 5,
                "width": "50%",
              },
              {
                "fontSize": 18,
                "lineHeight": 23.400000000000002,
              },
            ]
          }
        >
          Mock name
        </Text>
      </View>
      <View
        style={
          {
            "alignItems": "center",
            "flexDirection": "row",
            "marginHorizontal": 10,
            "marginTop": 10,
            "width": "100%",
          }
        }
      >
        <Text
          style={
            [
              {
                "right": 5,
                "textAlign": "right",
                "width": "50%",
              },
              {
                "fontSize": 18,
                "lineHeight": 23.400000000000002,
              },
            ]
          }
        >
          User tag
          :
        </Text>
        <TextInput
          maxLength={12}
          onBlur={[Function]}
          placeholder="No tag"
          placeholderTextColor="white"
          style={
            [
              [
                {
                  "left": 5,
                  "width": "50%",
                },
                {
                  "fontSize": 18,
                  "lineHeight": 23.400000000000002,
                },
              ],
              {
                "backgroundColor": "#DDDDDD",
                "borderColor": "#888888",
                "borderWidth": 0.5,
                "color": "#626262",
                "fontSize": 18,
                "paddingHorizontal": 10,
                "width": 146,
              },
            ]
          }
          value=""
        />
      </View>
      <View
        style={
          {
            "alignItems": "center",
            "flexDirection": "row",
            "marginHorizontal": 10,
            "marginTop": 10,
            "width": "100%",
          }
        }
      >
        <Text
          style={
            [
              {
                "right": 5,
                "textAlign": "right",
                "width": "50%",
              },
              {
                "fontSize": 18,
                "lineHeight": 23.400000000000002,
              },
            ]
          }
        >
          Block this user
          :
        </Text>
        <View
          style={
            {
              "left": 5,
              "width": "50%",
            }
          }
        >
          <RCTSwitch
            accessibilityRole="switch"
            onChange={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
            style={
              [
                {
                  "height": 31,
                  "width": 51,
                },
                {
                  "alignSelf": "flex-start",
                  "height": 32,
                },
              ]
            }
            value={false}
          />
        </View>
      </View>
      <View
        style={
          {
            "width": "70%",
          }
        }
      >
        <Text
          style={
            {
              "fontSize": 12,
              "paddingVertical": 10,
              "width": "50%",
            }
          }
        >
          You will not have 1:1 communciation with this user, nor see the user info.
        </Text>
      </View>
    </View>
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "backgroundColor": "#EFEFEF",
          "borderColor": "#707070",
          "borderRadius": 24,
          "borderWidth": 1,
          "marginTop": 20,
          "marginVertical": 10,
          "opacity": 1,
          "padding": 10,
          "width": 375,
        }
      }
      testID=""
    >
      <View>
        <Text
          numberOfLines={1}
          style={
            {
              "color": "#505050",
              "fontSize": 18,
              "textAlign": "center",
            }
          }
        >
          Chat
        </Text>
      </View>
    </View>
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "backgroundColor": "#EFEFEF",
          "borderColor": "#707070",
          "borderRadius": 24,
          "borderWidth": 1,
          "marginTop": 20,
          "marginVertical": 10,
          "opacity": 1,
          "padding": 10,
          "width": 375,
        }
      }
      testID=""
    >
      <View>
        <Text
          numberOfLines={1}
          style={
            {
              "color": "#505050",
              "fontSize": 18,
              "textAlign": "center",
            }
          }
        >
          Share
        </Text>
      </View>
    </View>
  </View>
</RCTScrollView>
`;
