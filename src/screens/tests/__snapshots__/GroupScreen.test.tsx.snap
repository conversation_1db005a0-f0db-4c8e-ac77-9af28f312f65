// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<GroupScreen /> UI renders correctly 1`] = `
<RCTScrollView
  keyboardShouldPersistTaps="handled"
  showsVerticalScrollIndicator={false}
>
  <View>
    <View
      style={
        {
          "backgroundColor": "#F4F4F4",
          "flex": 1,
          "paddingBottom": 0,
          "paddingLeft": 0,
          "paddingRight": 0,
        }
      }
    >
      <View
        style={
          {
            "alignItems": "center",
            "backgroundColor": "white",
            "borderColor": "#EFEFEF",
            "borderRadius": 0,
            "borderWidth": 2,
            "flex": 1,
            "margin": 10,
            "padding": 10,
          }
        }
      >
        <Text
          style={
            {
              "alignSelf": undefined,
              "color": "#202020",
              "fontSize": 20,
              "marginBottom": 10,
              "paddingHorizontal": undefined,
              "textTransform": "none",
            }
          }
        >
          Leaders (0)
        </Text>
        <View
          style={
            {
              "flexDirection": "row",
              "flexWrap": "wrap",
              "marginTop": 7,
              "paddingLeft": 12.5,
              "width": 725,
            }
          }
        />
        <Text
          style={
            {
              "color": "#202020",
              "fontSize": 20,
              "marginBottom": 10,
              "marginTop": -14,
            }
          }
        >
          Members (0)
        </Text>
        <View
          style={
            {
              "flexDirection": "row",
              "flexWrap": "wrap",
              "marginTop": 7,
              "paddingLeft": 12.5,
              "width": 725,
            }
          }
        />
      </View>
      <View
        style={
          {
            "alignItems": "center",
            "backgroundColor": "white",
            "borderColor": "#EFEFEF",
            "borderRadius": 0,
            "borderWidth": 2,
            "flex": 1,
            "margin": 10,
            "padding": 10,
          }
        }
      >
        <Text
          style={
            {
              "alignSelf": undefined,
              "color": "#202020",
              "fontSize": 20,
              "marginBottom": 10,
              "paddingHorizontal": undefined,
              "textTransform": "none",
            }
          }
        >
          Online meeting
        </Text>
        <Button
          onPress={[Function]}
          title="Join"
          width={250}
        />
      </View>
      <View
        style={
          {
            "alignItems": "center",
            "backgroundColor": "white",
            "borderColor": "#EFEFEF",
            "borderRadius": 0,
            "borderWidth": 2,
            "flex": 1,
            "margin": 10,
            "padding": 10,
          }
        }
      >
        <Text
          style={
            {
              "alignSelf": undefined,
              "color": "#202020",
              "fontSize": 20,
              "marginBottom": 10,
              "paddingHorizontal": undefined,
              "textTransform": "none",
            }
          }
        >
          Message settings
        </Text>
        <Text>
          <Text
            style={
              {
                "fontSize": 16,
                "fontWeight": "bold",
              }
            }
          >
            Local message auto disappear: 
          </Text>
          <Text
            style={
              {
                "color": "red",
                "fontSize": 16,
              }
            }
          >
            New messages sent to you in this group will disappear after below time. 
          </Text>
          <Text
            style={
              {
                "fontSize": 16,
              }
            }
          >
            If you want to keep certain message visible, you can tag "my favorite" to the message.
          </Text>
        </Text>
        <Text
          style={
            {
              "fontSize": 18,
              "fontWeight": "bold",
              "marginTop": 15,
            }
          }
        >
          Disabled
        </Text>
        <Button
          onPress={[Function]}
          title="Update"
          width={187.5}
        />
        <View
          style={
            {
              "display": "flex",
              "flexDirection": "row",
              "marginTop": 15,
            }
          }
        >
          <RCTScrollView>
            <View>
              <View>
                <View
                  style={
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#FFFFFF",
                      "borderTopWidth": 1,
                    }
                  }
                >
                  <View
                    accessibilityValue={
                      {
                        "max": undefined,
                        "min": undefined,
                        "now": undefined,
                        "text": undefined,
                      }
                    }
                    accessible={false}
                    focusable={false}
                    onClick={[Function]}
                    onResponderGrant={[Function]}
                    onResponderMove={[Function]}
                    onResponderRelease={[Function]}
                    onResponderTerminate={[Function]}
                    onResponderTerminationRequest={[Function]}
                    onStartShouldSetResponder={[Function]}
                  >
                    <View
                      style={
                        [
                          {
                            "flex": 1,
                            "flexDirection": "row",
                            "justifyContent": "center",
                          },
                          {
                            "backgroundColor": "white",
                            "marginTop": 0,
                          },
                        ]
                      }
                    >
                      <View
                        style={
                          [
                            {
                              "flex": 1,
                              "flexDirection": "row",
                              "marginLeft": 15,
                            },
                            {
                              "borderWidth": 0,
                            },
                            {
                              "minHeight": 50,
                            },
                          ]
                        }
                      >
                        <Text
                          style={
                            [
                              {
                                "fontSize": 16,
                              },
                              {
                                "alignSelf": "center",
                                "flex": 1,
                              },
                            ]
                          }
                        >
                          <Text>
                            <Text
                              style={
                                {
                                  "fontSize": 16,
                                  "fontWeight": "bold",
                                }
                              }
                            >
                              Mute new message notification: 
                            </Text>
                            <Text
                              style={
                                {
                                  "fontSize": 16,
                                }
                              }
                            >
                              This is a personal setting, and for this group only. Other members in this group are not affected.
                            </Text>
                          </Text>
                        </Text>
                        <RCTSwitch
                          accessibilityRole="switch"
                          onChange={[Function]}
                          onResponderTerminationRequest={[Function]}
                          onStartShouldSetResponder={[Function]}
                          style={
                            [
                              {
                                "height": 31,
                                "width": 51,
                              },
                              {
                                "alignSelf": "center",
                                "marginRight": 15,
                              },
                            ]
                          }
                          value={false}
                        />
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </RCTScrollView>
        </View>
      </View>
    </View>
    <View
      style={
        {
          "alignContent": "center",
          "backgroundColor": "#FFFFFF90",
          "bottom": 0,
          "justifyContent": "center",
          "left": 0,
          "position": "absolute",
          "right": 0,
          "top": 0,
        }
      }
    >
      <ActivityIndicator
        color="#626262"
        size="large"
      />
    </View>
  </View>
</RCTScrollView>
`;
