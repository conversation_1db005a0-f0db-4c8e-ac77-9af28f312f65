// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SettingsScreen /> UI renders correctly 1`] = `
<View
  style={
    {
      "flex": 1,
      "paddingLeft": 0,
      "paddingRight": 0,
    }
  }
>
  <RCTScrollView>
    <View>
      <View>
        <View
          style={
            {
              "borderBottomWidth": 1,
              "borderColor": "#DCDCDC",
              "borderTopWidth": 1,
            }
          }
        >
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderBottomWidth": 1,
                      "borderColor": "#DCDCDC",
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
          <View
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
          >
            <View
              style={
                [
                  {
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  },
                  {
                    "backgroundColor": "white",
                    "marginTop": 0,
                  },
                ]
              }
            >
              <View
                style={
                  [
                    {
                      "flex": 1,
                      "flexDirection": "row",
                      "marginLeft": 15,
                    },
                    {
                      "borderWidth": 0,
                    },
                    {
                      "minHeight": 50,
                    },
                  ]
                }
              >
                <Text
                  style={
                    [
                      {
                        "fontSize": 16,
                      },
                      {
                        "alignSelf": "center",
                        "flex": 1,
                      },
                    ]
                  }
                />
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  </RCTScrollView>
</View>
`;
