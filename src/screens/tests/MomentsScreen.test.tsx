import { cleanup, render } from '@testing-library/react-native';

import { AppContext } from '@/context/AppContext';
import MomentsScreen from '@/screens/MomentsScreen';
import React from 'react';

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  useFocusEffect: jest.fn()
}));

jest.mock('@/utils/i18n2', () => ({
  i18n2: {
    t: jest.fn((key) => key) // Mock translation function
  }
}));

jest.mock('@/components/navigation/NavigationButtons', () => ({
  NavBarButton: () => 'NavBarButton'
}));

jest.mock('@/components/navigation/NavChip', () => ({
  NavChip: () => 'NavChip'
}));

jest.mock('@/components', () => ({
  LoadingIndicator: () => 'LoadingIndicator'
}));

jest.mock('@/screens/MomentsScreen/components/HolyMomentContainer', () => {
  return () => 'HolyMomentContainer';
});

jest.mock('@/screens/MomentsScreen/components/ShareMoment', () => {
  return () => 'ShareMoment';
});

jest.mock('@/screens/MomentsScreen/components/FilterGroup', () => ({
  FilterGroup: () => 'FilterGroup'
}));

jest.mock('@/components/ImagePreview', () => {
  return () => 'ImagePreview';
});

jest.mock('@/screens/MomentsScreen/utils', () => {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const React = require('react');
  return {
    useLoadHomeMoments: jest.fn(() => ({
      holyHomeInfo: {
        moments: [],
        groups: []
      },
      load: jest.fn(),
      resetHomeInfoMoments: jest.fn()
    })),
    HolyMomentContext: React.createContext({}),
    MomentScreenContext: React.createContext({})
  };
});

jest.mock('@/context/AppContext', () => {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const React = require('react');
  return {
    AppContext: React.createContext({
      setSeenMomentAsync: jest.fn(),
      moments: [],
      loadMomentsAsync: jest.fn().mockResolvedValue({
        data: []
      })
    })
  };
});

describe('<MomentsScreen />', () => {
  beforeAll(() => {
    globalThis.dsObject = {
      ...jest.requireActual('@/ioc/interfaces'),
      getAllContactAsync: jest.fn().mockResolvedValue({
        me: [],
        blockedBy: []
      })
    };
  });

  afterEach(cleanup);

  it('UI renders correctly', async () => {
    const appContextValue = {
      setSeenMomentAsync: jest.fn(),
      loadMomentsAsync: jest.fn(),
      moments: [],
      insets: { top: 0, bottom: 0, left: 0, right: 0 },
      userProfile: {
        displayName: 'Mock User'
      },
      canOpenJitsi: true
    };

    const { toJSON } = render(
      <AppContext.Provider value={appContextValue}>
        <MomentsScreen />
      </AppContext.Provider>
    );

    toJSON && expect(toJSON()).toMatchSnapshot();
  });
});
