import MemberScreen from '@/screens/MemberScreen';
import React from 'react';
import { render } from '@testing-library/react-native';

jest.mock('@rneui/themed', () => ({
  Overlay: 'Overlay'
}));

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn()
}));

jest.mock('@/components/Avatar2', () => ({
  // eslint-disable-next-line react-native/no-raw-text
  Avatar2: () => <div>Avatar2</div>
}));

jest.mock('@/viewModels/MemberViewModel', () => ({
  useMemberViewModel: () => ({
    showFullScreenImage: false,
    isUserBlocked: false,
    toggleFullScreenImage: jest.fn(),
    toggleUserBlock: jest.fn(),
    initiateChat: jest.fn(),
    showChatButton: true,
    copyEmailToClipboard: jest.fn(),
    insets: { top: 0, bottom: 0, left: 0, right: 0 },
    setTag: jest.fn(),
    initialTag: '',
    userTag: '',
    setUserTag: jest.fn()
  })
}));

describe('<MemberScreen />', () => {
  it('renders correctly based on default states', () => {
    const route = {
      params: {
        id: '123',
        name: 'Mock name',
        email: '<EMAIL>'
      }
    };
    const tree = render(<MemberScreen route={route} />).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
