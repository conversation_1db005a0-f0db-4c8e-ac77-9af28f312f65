import { AppContext } from '@/context/AppContext';
import GroupScreen from '@/screens/GroupScreen';
import React from 'react';
import { render } from '@testing-library/react-native';

jest.mock('@/components/Button', () => ({ Button: 'Button' }));

describe('<GroupScreen />', () => {
  beforeAll(() => {
    globalThis.dsObject = {
      ...jest.requireActual('@/ioc/interfaces'),
      getGroup: jest.fn()
    };
  });
  it('UI renders correctly', () => {
    const mockNavigation = {
      navigate: jest.fn(),
      popToTop: jest.fn(),
      pop: jest.fn(),
      addListener: jest.fn((_event, callback) => callback())
    };
    const route = {
      params: {
        group: { name: 'Test Group', classId: 0 },
        fromCreateGroupScreen: false,
        title: 'Test Screen'
      }
    };
    const appContextValue = {
      insets: { top: 0, bottom: 0, left: 0, right: 0 },
      userProfile: {
        displayName: 'Mock User'
      },
      canOpenJitsi: true
    };

    const tree = render(
      <AppContext.Provider value={appContextValue}>
        <GroupScreen navigation={mockNavigation} route={route} />
      </AppContext.Provider>
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
