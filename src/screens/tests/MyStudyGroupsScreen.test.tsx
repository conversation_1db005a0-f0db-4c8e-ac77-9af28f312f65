import { AppContext } from '@/context/AppContext';
import MyStudyGroups from '@/screens/MyStudyGroupsScreen/MyStudyGroupsScreen';
import React from 'react';
import { render } from '@testing-library/react-native';

describe('<MyStudyGroups />', () => {
  beforeAll(() => {
    globalThis.dsObject = {
      ...jest.requireActual('@/ioc/interfaces'),
      getAllContactAsync: jest.fn().mockResolvedValue({
        me: [],
        blockedBy: []
      })
    };
  });
  it('UI renders correctly', async () => {
    const mockNavigation = {
      navigate: jest.fn(),
      addListener: jest.fn().mockImplementation((_event, callback) => callback()),
      setParams: jest.fn()
    };
    const route = {
      params: {}
    };
    const appContextValue = {
      loadGroupsAsync: jest.fn(),
      loadTagsAsync: jest.fn(),
      checkUserUpdateAsync: jest.fn(),
      groups: [],
      insets: { top: 0, bottom: 0, left: 0, right: 0 },
      friends: [],
      userProfile: {
        displayName: 'Mock User'
      }
    };

    const tree = render(
      <AppContext.Provider value={appContextValue}>
        <MyStudyGroups navigation={mockNavigation} route={route} />
      </AppContext.Provider>
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
