import { AppContext } from '@/context/AppContext';
import React from 'react';
import SettingsScreen from '@/screens/SettingsScreen';
import { render } from '@testing-library/react-native';

jest.mock('@/components/SettingsList', () => {
  const ActualSettingsList = jest.requireActual('@/components/SettingsList');

  // eslint-disable-next-line @typescript-eslint/no-explicit-any, react-native/no-raw-text
  const MockedItem = (props: any) => <div>Title: {props.title}</div>;

  ActualSettingsList.Item = MockedItem;

  return ActualSettingsList;
});

describe('<SettingsScreen />', () => {
  it('UI renders correctly', () => {
    const appContextValue = {
      insets: { top: 0, bottom: 0, left: 0, right: 0 },
      userProfile: {
        displayName: 'Mock User Name'
      }
    };

    const mockNavigation = {
      setParams: jest.fn()
    };

    const tree = render(
      <AppContext.Provider value={appContextValue}>
        <SettingsScreen navigation={mockNavigation} />
      </AppContext.Provider>
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
