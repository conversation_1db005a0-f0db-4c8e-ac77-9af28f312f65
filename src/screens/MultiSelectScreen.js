import { Dimensions, ScrollView, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { CheckBox } from '@rneui/themed';
import { Colors } from '@/styles/colors';
import { OkButton } from '@/components/navigation/NavigationButtons';
import React from 'react';
import { cloneDeep } from 'lodash-es';
import { getCurrentUser } from '@/utils/user';

export default class MultiSelectScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ route }) => {
    const { title, onSave } = route.params;
    return {
      title,
      gestureEnabled: true,
      headerRight: () => (
        <OkButton
          onPress={() => {
            onSave();
          }}
        />
      )
    };
  };

  constructor(props) {
    super(props);

    const params = props.route.params;
    const choices = cloneDeep(params.choices);

    this.state = {
      choices
    };

    this.props.navigation.setParams({ onSave: this.onSave });
  }

  onSave = () => {
    const { choices } = this.state;
    const params = this.props.route.params;
    if (typeof params.onComplete === 'function') {
      params.onComplete(choices);
    }
    this.props.navigation.pop();
  };

  onCheck = (choice) => {
    const { choices } = this.state;
    for (let i in choices) {
      if (choices[i].id === choice.id) {
        choices[i].isSelected = !choices[i].isSelected;
        this.setState({ choices });
        return;
      }
    }
  };

  render() {
    const { choices } = this.state;
    const windowWidth = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right;
    return (
      <ScrollView
        style={{
          flex: 1,
          paddingLeft: this.context.insets.left,
          paddingRight: this.context.insets.right
        }}
        scrollIndicatorInsets={{ right: 1 }}>
        {choices.map((choice) => {
          return choice.hide ? null : (
            <CheckBox
              keyIndex={choice.id}
              containerStyle={{ width: windowWidth - 20 }}
              textStyle={{ fontSize: getCurrentUser().getSmallFontSize() }}
              checkedColor={Colors.darkBlue}
              title={choice.text}
              checked={!!choice.isSelected}
              onPress={() => {
                this.onCheck(choice);
              }}
            />
          );
        })}
        <View style={{ height: this.context.insets.bottom }} />
      </ScrollView>
    );
  }
}
