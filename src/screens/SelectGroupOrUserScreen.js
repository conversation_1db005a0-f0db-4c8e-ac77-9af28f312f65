import { Dimensions, Keyboard, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { Avatar2 } from '@/components/Avatar2';
import { Colors } from '@/styles/colors';
import { ContactsPage } from '@/components/ContactsPage';
import { FmChip } from '@/fishMeet/components/FmChip';
import { FmStyles } from '@/fishMeet/styles/fmStyles';
import { FmText } from '@/fishMeet/components/FmText';
import { KeyboardFlatList } from '@/components';
import { NavChip } from '@/components/navigation/NavChip';
import React from 'react';
import SearchBar from '@/components/SearchBar';
import { getCurrentUser } from '@/utils/user';
import { getGroupIcon } from '@/components/Images';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';

export default class SelectGroupOrUserScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ navigation, route }) => {
    const page = route.params?.page ?? 1;

    // headerTitleAlign and headerTitle styles should be consistent with with MyStudyGroupsScreen

    const iDigestNavigationOptions = {
      headerTitle: () => {
        return (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
            <NavChip
              page={page}
              pageIndex={0}
              title={i18n2.t('Common.Contacts')}
              width={100}
              onPress={() => {
                globalThis.eventEmitter.emit('selectGroupOrUserPage', 0);
              }}
            />
            <View style={{ width: 7 }} />
            <NavChip
              page={page}
              pageIndex={1}
              title={i18n2.t('Group')}
              width={100}
              onPress={() => {
                globalThis.eventEmitter.emit('selectGroupOrUserPage', 1);
              }}
            />
          </View>
        );
      }
    };

    const fmNavigationOptions = {
      headerTitleAlign: 'center',
      headerTitle: () => {
        return (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
            <FmChip
              page={page}
              pageIndex={0}
              title={i18n2.t('Common.Contacts')}
              width={120}
              onPress={() => {
                globalThis.eventEmitter.emit('selectGroupOrUserPage', 0);
              }}
            />
            <View style={{ width: 7 }} />
            <FmChip
              page={page}
              pageIndex={1}
              title={i18n2.t('Group')}
              width={120}
              onPress={() => {
                globalThis.eventEmitter.emit('selectGroupOrUserPage', 1);
              }}
            />
          </View>
        );
      }
    };

    return isFishMeet ? fmNavigationOptions : iDigestNavigationOptions;
  };

  constructor(props) {
    super(props);

    this.onSelect = props.route.params.onSelect;
    this.listeners = [];

    this.state = {
      page: 1,
      users: props.route.params.users ?? [],
      groups: props.route.params.groups ?? [],
      isSearching: false,
      searchText: ''
    };
  }

  loadAsync = async () => {
    /*
    This overwrites the users passed in the props.
    getInviteUsers will return the contact list, with the display for each user to include the 
    tags and uniqueID. So that you can search, based on the tag and/or the uniqueid.
    
    The users passed in params is from the global list, that does not include the tag and unique id. 
    Since we introduced tag and use uniqueid everywhere else to refer user, 
    the global list is missing these two pieces of information.
    
    TODO:
    There might be a need to refactor code to get the global list to become aware of tags and uniqueids. 
    That would need a separate PR. The tag/uniqueID reference is spread in a a few places right now.
    */
    const users = await globalThis.dsObject.getInviteUsers(getCurrentUser().getUserId());
    if (!users) {
      this.props.navigation.pop();
      return;
    }

    // if no groups passed in, use the groups in the context
    this.setState(this.props.route.params.groups ? { users } : { users, groups: this.context.groups });
  };

  componentDidMount() {
    this.listeners.push(
      globalThis.eventEmitter.addListener('selectGroupOrUserPage', (page) => {
        this.onSelectPage(page);
      })
    );
    this.loadAsync();
  }

  componentWillUnmount() {
    this.listeners?.forEach((listener) => {
      listener.remove();
    });
  }

  onSelectPage = (page) => {
    this.setState({ page });
    this.props.navigation.setParams({ page });
  };

  renderListItem = ({ item }) => {
    const fontSize = getCurrentUser().getMediumFontSize();
    const name = item.value?.substring(1) || item.text;
    //todo: put userTag into context. it's currently loaded in MyStudyGroupScreen.js
    //const userTag = this.state.userTags[item.userId] || '';

    return (
      <TouchableOpacity
        key={item.userId}
        onPress={() => {
          this.onSelect({ userId: item.userId, uniqueId: item.uniqueId });
          this.props.navigation.pop();
        }}>
        <View style={{ height: 60, flexDirection: 'row', alignItems: 'center', paddingLeft: 7 }}>
          <Avatar2 userId={item.userId} />
          <Text style={{ fontSize, marginLeft: 7 }} numberOfLines={1}>
            {name} {/*todo: userTag && `(${userTag})`*/}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  renderSectionHeader = (section) => {
    const fontSize = getCurrentUser().getLargeFontSize();
    return (
      <View style={{ height: 40, backgroundColor: Colors.lightBlue, paddingLeft: 10, justifyContent: 'center' }}>
        <Text style={{ fontSize, fontWeight: 'bold' }}>{section.title}</Text>
      </View>
    );
  };

  handleSearchTextChange = (text) => {
    this.setState({ searchText: text });
  };

  handleSearchTextFocus = () => {
    this.setState({ isSearching: true });
  };

  handleSearchTextClose = () => {
    Keyboard.dismiss();
    this.setState({ isSearching: false, searchText: '' });
  };

  renderFriends = () => {
    const { users } = this.state;
    const fontSize = getCurrentUser().getMediumFontSize();
    if (users.length === 0) {
      return (
        <View
          style={{
            flex: 1,
            paddingLeft: this.context.insets.left,
            paddingRight: this.context.insets.right,
            alignContent: 'center',
            justifyContent: 'center'
          }}>
          <Text style={[styles.titleText, { fontSize }]}>{i18n2.t('SelectGroupOrUserScreen.NoFriend')}</Text>
        </View>
      );
    }

    const { searchText, isSearching } = this.state;
    const searchTextLowerCase = searchText.trim().toLowerCase();
    const showff = searchText.trim()
      ? users.filter(
          (it) =>
            it.value?.toLowerCase().includes(searchTextLowerCase) || it.text.toLowerCase().includes(searchTextLowerCase)
        )
      : users;

    return (
      <View style={{ flex: 1, paddingLeft: this.context.insets.left, paddingRight: this.context.insets.right }}>
        <View style={{ marginTop: 10 }}>
          <SearchBar
            autoFocus={false}
            value={searchText}
            isCloseVisible={isSearching}
            onFocus={this.handleSearchTextFocus}
            onClose={this.handleSearchTextClose}
            onChangeText={this.handleSearchTextChange}
          />
        </View>
        <KeyboardFlatList data={showff} renderItem={this.renderListItem} />
      </View>
    );
  };

  fmRenderFriends = () => {
    const { searchText, isSearching } = this.state;
    return (
      <ContactsPage
        isSearching={isSearching}
        searchText={searchText}
        handleSearchTextFocus={this.handleSearchTextFocus}
        handleSearchTextChange={this.handleSearchTextChange}
        handleSearchTextClose={this.handleSearchTextClose}
        friendItemOnPress={(item) => {
          this.onSelect({ userId: item.userId, uniqueId: item.uniqueId });
          this.props.navigation.pop();
        }}
        renderNoFriends={() => {
          return (
            <View style={{ ...FmStyles.screenContainer, justifyContent: 'center' }}>
              <FmText style={{ fontSize: getCurrentUser().getMediumFontSize() }}>
                {i18n2.t('SelectGroupOrUserScreen.NoFriend')}
              </FmText>
            </View>
          );
        }}
      />
    );
  };

  renderGroups = () => {
    const { groups } = this.state;
    const { searchText, isSearching } = this.state;
    const searchTextLowerCase = searchText.trim().toLowerCase();
    const showGroup = searchText.trim()
      ? groups.filter((it) => it.name.toLowerCase().includes(searchTextLowerCase))
      : groups;

    return (
      <View style={{ flex: 1, paddingLeft: this.context.insets.left, paddingRight: this.context.insets.right }}>
        <View style={{ marginTop: 10 }}>
          <SearchBar
            autoFocus={false}
            value={searchText}
            isCloseVisible={isSearching}
            onFocus={this.handleSearchTextFocus}
            onClose={this.handleSearchTextClose}
            onChangeText={this.handleSearchTextChange}
          />
        </View>
        <KeyboardFlatList
          data={showGroup}
          renderItem={({ item }) => {
            return (
              <TouchableOpacity
                activeOpacity={1}
                key={item.groupId}
                onPress={() => {
                  this.onSelect({ groupId: item.groupId });
                  this.props.navigation.pop();
                }}>
                <View
                  style={{
                    marginTop: 10,
                    marginHorizontal: 10,
                    backgroundColor: Colors.lightBlue,
                    borderColor: Colors.darkBlue,
                    borderWidth: 1,
                    borderRadius: 25,
                    height: 50,
                    paddingHorizontal: 10,
                    alignItems: 'baseline',
                    justifyContent: 'center'
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      width: Dimensions.get('window').width - 75
                    }}>
                    {getGroupIcon(item)}
                    <Text
                      style={{
                        fontSize: getCurrentUser().getSmallFontSize(),
                        fontWeight: 'bold',
                        paddingLeft: 7,
                        color: '#707070'
                      }}
                      numberOfLines={1}>
                      {item.name}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            );
          }}
        />
      </View>
    );
  };

  render() {
    const { page } = this.state;

    if (isFishMeet) {
      return page === 0 ? this.fmRenderFriends() : this.renderGroups();
    } else {
      return page === 0 ? this.renderFriends() : this.renderGroups();
    }
  }
}

const styles = StyleSheet.create({
  titleText: {
    color: Colors.darkBlue,
    textAlign: 'center',
    marginTop: 7
  }
});
