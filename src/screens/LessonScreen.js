import * as Clipboard from 'expo-clipboard';
import * as Linking from 'expo-linking';
import * as React from 'react';
import * as ScreenOrientation from 'expo-screen-orientation';
import * as WebBrowser from 'expo-web-browser';

import { Alert, Dimensions, Platform, Share, Text, TouchableOpacity, View } from 'react-native';
import { ModalPopup, NavBarTitle, RedDot, getImage } from '@/components';
import { cloneDeep, debounce, isEqual } from 'lodash-es';
import { dealLessonSessionResult, getTemplateHtml } from '@/utils/lesson';
import { getBibleRef, initializeAsNeeded } from '@/utils/bible';
import { getCurrentUser, getHttpsServer } from '@/utils/user';
import { getObjectAsync, resetObjectAsync } from '@/dataStorage/localStorage';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import { CommonActions } from '@react-navigation/native';
import MediaModal from '@/components/Lesson/MediaModal';
import PagerView from 'react-native-pager-view';
import { WebView } from 'react-native-webview';
import { i18n2 } from '@/utils/i18n2';
import { launchMeetingAsync } from '@/utils/meeting';
import { logEvent } from '@/utils/logger';
import { showMessage } from '@/components/MessageBox';
import { updateHtmlFontSize } from '@/utils/helper';

export default class LessonScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = () => ({ headerShown: false });

  constructor(props) {
    super(props);

    this.lessonId = this.props.route.params.id;
    // Class download status constants 1-Downloadable 2-Downloaded
    this.downloadState = this.props.route.params.downloadState;
    this.classId = this.props.route.params.classId;
    this.lessonFile = this.props.route.params.file;
    this.isLessonWebUrl = !this.lessonFile.toLowerCase().endsWith('.json');
    this.lessonTitle = this.props.route.params.title;
    this.sessionIndex = this.props.route.params.sessionIndex;
    this.bookTitle = this.props.route.params.bookTitle;
    this.studyGroups = this.props.route.params.groups;
    this.free = this.props.route.params.lesson.free;
    this.debouncedSaveAsync = debounce(this.saveAsync, 2000);
    this.debouncedSaveScrollPosition = debounce(this.saveScrollPositionAsync, 500);
    this.lastScrollTop = 0;
    this.topNavBarTitleTop = 0;
    // this.topNavBarTitleTop = new Animated.Value(0); // 0 - (-42 - this.context.insets.top)
    this.dayNavBarTop = 0;
    // this.dayNavBarTop = new Animated.Value(0); // (44 + this.context.insets.top) - (this.context.insets.top - 2)
    this.bottomNavBarHeightMax = 57;
    this.bottomNavBarHeight = 0;
    // this.bottomNavBarHeight = new Animated.Value(0); // 0 - (this.bottomNavBarHeightMax + this.context.insets.bottom)
    this.viewPager = React.createRef();
    this.lessonWebView = React.createRef();
    this.lessonWebViewCanGoBack = false;
    this.hideNavBar = false;
    this.questionsCount = 0;
    this.webView = [];
    this.delayLoadImage = this.downloadState !== 2;
    this.loadAsyncPayload = {};
    this.accessToken = getCurrentUser().getAccessToken(); // TODO: Use a new token instead of user token
    this.playingAudio = {};
    this.reloadNeeded = true;

    const position = getCurrentUser().getProperty(`LessonProgress.${this.classId}`);
    this.state = {
      showModal: false,
      modalTitle: '',
      modalItems: [],
      scrollTop: position?.scrollTop || 0,
      day: position?.day || 1,
      content: [],
      html: '',
      answers: {},
      lesson: {},
      mediaModalVisible: false,
      currentMedia: {}
    };
    if (!this.isLessonWebUrl) {
      getTemplateHtml(this.classId, this.props.navigation.goBack).then((html) => {
        if (html) {
          this.cacheKey = `lesson/${this.lessonId}/-/${this.lessonFile}?index=${this.sessionIndex}`;
          getObjectAsync(this.cacheKey).then((result) => {
            if (result) {
              this.setState({ ...result, html });
            } else {
              this.setState({ html });
            }
          });
        }
      });
    }
  }

  componentDidMount() {
    this.bottomNavBarHeight = this.bottomNavBarHeightMax + this.context.insets.bottom;
    // this.bottomNavBarHeight = new Animated.Value(this.bottomNavBarHeightMax + this.context.insets.bottom);

    this.dayNavBarTop = 44 + this.context.insets.top;
    // this.dayNavBarTop = new Animated.Value(44 + this.context.insets.top);

    ScreenOrientation.addOrientationChangeListener(() => {
      setTimeout(() => {
        this.bottomNavBarHeight = this.bottomNavBarHeightMax + this.context.insets.bottom;
        // this.bottomNavBarHeight = new Animated.Value(this.bottomNavBarHeightMax + this.context.insets.bottom);

        this.dayNavBarTop = 44 + this.context.insets.top;
        // this.dayNavBarTop = new Animated.Value(44 + this.context.insets.top);

        this.startShowNavBar();
      }, 1000);
    });

    this.context.setCurrentProgressAsync({ LessonScreen: this.props.route.params });

    if (!this.isLessonWebUrl) {
      this.props.navigation.addListener('focus', () => {
        if (this.reloadNeeded && this.downloadState !== 2) {
          this.reloadNeeded = false;
          this.loadAsync();
        }
      });
    } else {
      // force update after animated value is updated
      this.forceUpdate();
    }
  }

  componentWillUnmount() {
    ScreenOrientation.removeOrientationChangeListeners();
    this.context.setAudioPlayerMinimized(true);
  }

  async loadAsync() {
    try {
      // load lesson
      console.log(`loadAsync: ${this.lessonFile}`);

      const result = await globalThis.dsObject.loadLesson(this.lessonId, this.lessonFile, this.sessionIndex);
      if (!result) {
        return;
      }

      // no need to refresh if no change
      if (isEqual(result.body, this.loadAsyncPayload)) {
        console.log('Same content, skip refreshing UI');
        return;
      }
      this.loadAsyncPayload = cloneDeep(result.body);
      const { stateContent, questionsCount, discussions } = await dealLessonSessionResult(
        { classId: this.classId },
        result,
        this.sessionIndex,
        this.context,
        this.state.day
      );
      resetObjectAsync(this.cacheKey, stateContent);
      this.getAnswers(result.body.answers, stateContent.answers);
      this.questionsCount = questionsCount;
      this.discussions = discussions;
      this.setState({ ...stateContent });
    } catch (error) {
      alert(error);
      this.props.navigation.pop();
    }
  }

  async saveAsync(classId, sessionIndex, body) {
    const result = await globalThis.dsObject.saveAnswer(classId, sessionIndex, body);
    if (!result) {
      showMessage({
        message: i18n2.t('Errors.ErrSaveAnswer'),
        type: 'danger'
      });
      return;
    }

    console.log(`Answer progress ${Object.keys(body).length} of ${this.questionsCount}`);
    globalThis.eventEmitter.emit('answerChanged', {
      classId,
      sessionIndex,
      percentage: ((100 * Object.keys(body).length) / this.questionsCount).toFixed(0) + '%'
    });
  }

  renderBottomSheet = () => {
    const { modalTitle, modalItems } = this.state;
    return (
      <ModalPopup
        insets={this.context.insets}
        visible={this.state.showModal}
        showDivider={true}
        setNotVisible={() => this.setState({ showModal: false })}
        title={modalTitle}
        items={modalItems}
        iconsPerRow={modalItems.length}
        showCancel={true}
        hideOnPress={true}
      />
    );
  };

  shareAnswerToGroupAsync = async (group, isAnonymous) => {
    if (!group) {
      Alert.alert(i18n2.t('Common.Failed'), i18n2.t('InvalidGroup'));
      return;
    }

    const { answers } = this.state;
    const message = answers[this.questionId] || '';
    this.goToDiscussion({
      questionId: this.questionId,
      isAnonymous,
      currentTag: group.groupId,
      onDiscussionIdReadyCallbackAsync: async (discussionId) => {
        await this.setChatMessageAsync(discussionId, message);
      }
    });
  };

  shareAnswerToEveryoneAsync = async () => {
    const { answers } = this.state;
    const message = answers[this.questionId] || '';
    this.goToDiscussion({
      questionId: this.questionId,
      currentTag: 0, // 0 is public
      onDiscussionIdReadyCallbackAsync: async (discussionId) => {
        await this.setChatMessageAsync(discussionId, message);
      }
    });
  };

  setChatMessageAsync = async (chatId, message) => {
    await getCurrentUser().resetPropertyAsync(`chat.draft.${chatId}`, message);
    await getCurrentUser().resetPropertyAsync(`chat.draft.replyId.${chatId}`, -1);
    await getCurrentUser().resetPropertyAsync(`chat.draft.replyText.${chatId}`, '');
    await getCurrentUser().resetPropertyAsync(`chat.draft.mentionedUsers.${chatId}`, []);
  };

  openLink(data) {
    try {
      let url = data.url;

      if (!url.toLowerCase().startsWith('http://') && !url.toLowerCase().startsWith('https://')) {
        url = `${getHttpsServer(`lesson/${this.lessonId}/content/`)}${url}?token=${this.accessToken}`;
      }

      if (data.openInOtherApp) {
        Linking.openURL(url);
      } else {
        WebBrowser.openBrowserAsync(url, {
          controlsColor: '#ffffff',
          toolbarColor: Colors.blue,
          showTitle: true,
          collapseToolbar: true
        });
      }
    } catch (error) {
      alert(error);
    }
  }

  setAnswer(data) {
    const { id, value } = data;
    if (id === undefined || id === null || id === '') {
      alert(`Invalid answer id: ${id}`);
      return;
    }

    const { answers } = this.state;
    const body = answers;
    if (value === undefined || value === null || value === '') {
      delete body[id];
    } else {
      body[data.id] = value;
    }

    this.debouncedSaveAsync(this.classId, this.sessionIndex, body);
  }

  goToBible(data) {
    this.props.navigation.navigate('Bible', {
      book: data.book,
      verse: data.verse
    });
  }

  selectGroup = (groups, onSelected) => {
    this.props.navigation.navigate('Select', {
      choices: groups,
      titleControl: getImage('groupSelect'),
      title: i18n2.t('SelectGroup'),
      getDisplayName: (choice) => choice.name,
      classId: this.classId,
      isCurrent: (choice) => false,
      onSelect: (group) => {
        setTimeout(() => {
          onSelected(group);
        }, 0);
      }
    });
  };

  getAnswerText(questionId) {
    const { answers, lesson } = this.state;
    const answer = answers[questionId] || '';
    let lastText = '';
    const sessions = lesson.sessions || [];
    for (let sessionIndex in sessions) {
      const sessionContent = sessions[sessionIndex].content;
      for (const item of sessionContent) {
        if (item.type === 'text') {
          lastText = item.value;
        }
        if (item.type === 'question') {
          if (item.value === questionId) {
            const { bookTitle } = this.props.route.params;
            const title = `${bookTitle} ${lesson.title}`.trim();
            let result = `[${title}]`;

            const questionText = item.title || lastText;
            result += ` ${questionText.replace(/<[^>]*>/g, '').trim()}`;

            result += `\n---------------------------\n${answer.trim()}`;
            return result;
          }
        }
      }
    }

    return answer;
  }

  goToDay = async (day, force = false) => {
    const { lesson } = this.state;
    const days = lesson.sessions?.length || 1;
    // day range is 1...{days}
    if (day <= 0 || day > days) {
      alert(`Invalid day ${day}`);
      return;
    }

    // when day is the same during initialization, dismiss the operation
    if (!force && day === this.state.day) {
      return;
    }

    await getCurrentUser().setPropertyAsync(`LessonProgress.${this.classId}`, {
      day,
      scrollTop: 0
    });
    this.setState({ day });
    this.startShowNavBar();
    this.viewPager.current.setPage(day - 1);
  };

  saveScrollPositionAsync = async (scrollTop) => {
    const position = getCurrentUser().getProperty(`LessonProgress.${this.classId}`);
    if (position?.scrollTop === scrollTop) {
      return;
    }

    await getCurrentUser().setPropertyAsync(`LessonProgress.${this.classId}`, { scrollTop });
  };

  hasNewMessage = (day) => {
    const { content } = this.state;
    // it's possible that it's not loaded yet
    if (content.length <= day - 1) {
      return false;
    }
    const { session, discussions } = content[day - 1];
    for (const item of session?.content || []) {
      if (item.type === 'question' && discussions[item.value]?.hasNewMessage) {
        return true;
      }
    }

    return false;
  };

  renderDayNavBar() {
    const { lesson } = this.state;
    const days = lesson.sessions?.length || 0;
    if (days <= 1) {
      return null;
    }

    const { day } = this.state;
    return (
      <View
        style={{
          position: 'absolute',
          top: this.dayNavBarTop,
          width: Dimensions.get('window').width - this.context.insets.left - this.context.insets.right
        }}>
        <View
          style={{
            width: '100%',
            height: '100%',
            flex: 1,
            flexDirection: 'row',
            flexWrap: 'wrap',
            paddingHorizontal: 25,
            justifyContent: 'center'
          }}>
          <NavItem
            image={'firstDay'}
            day={1}
            showRedDot={this.hasNewMessage(1)}
            selected={1 === day}
            onPress={() => this.goToDay(1)}
          />
          {Array.from({ length: days - 2 }, (_, i) => i + 2).map((d) => (
            <NavItem
              key={d}
              image={'dayN'}
              day={d}
              showRedDot={this.hasNewMessage(d)}
              selected={d === day}
              onPress={() => this.goToDay(d)}
            />
          ))}
          <NavItem
            image={'lastDay'}
            day={days}
            showRedDot={this.hasNewMessage(days)}
            selected={days === day}
            onPress={() => this.goToDay(days)}
          />
        </View>
      </View>
    );
  }

  startHideNavBar = () => {
    // if (this.hideNavBar) {
    //   return;
    // }
    // this.hideNavBar = true;
    // this.bottomNavBarHeight = 0;
    // //   Animated.timing(this.bottomNavBarHeight, {
    // //     toValue: 0,
    // //     easing: EasingNode.out(EasingNode.ease),
    // //     duration: 200,
    // //     useNativeDriver: true
    // //   }).start();
    // this.topNavBarTitleTop = -42 - this.context.insets.top;
    // //   Animated.timing(this.topNavBarTitleTop, {
    // //     toValue: -42 - this.context.insets.top,
    // //     easing: EasingNode.out(EasingNode.ease),
    // //     duration: 200,
    // //     useNativeDriver: true
    // //   }).start();
    // const { lesson } = this.state;
    // const sessionLength = lesson.sessions?.length || 0;
    // if (sessionLength > 1) {
    //   this.dayNavBarTop = this.context.insets.top - 2;
    //   //     Animated.timing(this.dayNavBarTop, {
    //   //       toValue: this.context.insets.top - 2,
    //   //       easing: EasingNode.out(EasingNode.ease),
    //   //       duration: 200,
    //   //       useNativeDriver: true
    //   //     }).start();
    // }
    // this.forceUpdate();
  };

  startShowNavBar = () => {
    // if (!this.hideNavBar) {
    //   return;
    // }
    // this.hideNavBar = false;
    // this.bottomNavBarHeight = this.bottomNavBarHeightMax + this.context.insets.bottom;
    // //   Animated.timing(this.bottomNavBarHeight, {
    // //     toValue: this.bottomNavBarHeightMax + this.context.insets.bottom,
    // //     easing: EasingNode.out(EasingNode.ease),
    // //     duration: 200,
    // //     useNativeDriver: true
    // //   }).start();
    // this.topNavBarTitleTop = 0;
    // //   Animated.timing(this.topNavBarTitleTop, {
    // //     toValue: 0,
    // //     easing: EasingNode.out(EasingNode.ease),
    // //     duration: 200,
    // //     useNativeDriver: true
    // //   }).start();
    // const { lesson } = this.state;
    // const sessionLength = lesson.sessions?.length || 0;
    // if (sessionLength > 1) {
    //   this.dayNavBarTop = 44 + this.context.insets.top;
    //   //     Animated.timing(this.dayNavBarTop, {
    //   //       toValue: 44 + this.context.insets.top,
    //   //       easing: EasingNode.out(EasingNode.ease),
    //   //       duration: 200,
    //   //       useNativeDriver: true
    //   //     }).start();
    // }
    // this.forceUpdate();
  };

  selectOneGroup = (onSelected) => {
    if (this.studyGroups.length === 1) {
      onSelected(this.studyGroups[0]);
    } else {
      this.selectGroup(this.studyGroups, (group) => {
        onSelected(group);
      });
    }
  };

  showShareAnswer = () => {
    const modalItems = [];

    modalItems.push({
      renderIcon: () => getImage('copy'),
      text: i18n2.t('Copy'),
      onPress: () => {
        Clipboard.setStringAsync(this.getAnswerText(this.questionId));
      }
    });

    if (this.studyGroups.length > 0) {
      modalItems.push({
        renderIcon: () => getImage('groupWithName'),
        text: i18n2.t('ShareToGroup'),
        onPress: () => {
          this.selectOneGroup((group) => {
            this.shareAnswerToGroupAsync(group, false);
          });
        }
      });

      modalItems.push({
        renderIcon: () => getImage('groupWithNoName'),
        text: i18n2.t('ShareToGroupNLAnon'),
        onPress: () => {
          this.selectOneGroup((group) => {
            this.shareAnswerToGroupAsync(group, true);
          });
        }
      });
    }

    modalItems.push({
      renderIcon: () => getImage('wechat'),
      text: i18n2.t('ShareToEveryone'),
      onPress: () => {
        this.shareAnswerToEveryoneAsync();
      }
    });

    this.setState({ showModal: true, modalTitle: i18n2.t('ShareAnswer'), modalItems });
  };

  showMore = () => {
    const modalItems = [];

    modalItems.push({
      renderIcon: () => getImage('wechat'),
      text: i18n2.t('Discussion'),
      onPress: () => {
        this.selectOneGroup((group) => {
          this.props.navigation.navigate('Chat', {
            chatId: group.groupId,
            title: group.name,
            group: group,
            classId: this.classId,
            showGroupIcon: true,
            enableAudioChat: true
          });
        });
      }
    });

    modalItems.push({
      renderIcon: () => getImage('conference'),
      text: i18n2.t('MeetingUI.Title'),
      onPress: () => {
        this.selectOneGroup((group) => {
          launchMeetingAsync(group.groupId, this.context.canOpenJitsi, this.context.canOpenFishMeet);
        });
      }
    });

    this.setState({ showModal: true, modalTitle: null, modalItems });
  };
  showMedia = (item) => {
    this.setState({
      currentMedia: item,
      mediaModalVisible: true
    });
  };

  hideMedia = () => {
    this.setState({
      currentMedia: {},
      mediaModalVisible: false
    });
  };

  renderContent = (content, index) => {
    const { answers } = this.state;
    const html = updateHtmlFontSize(this.state.html);
    return (
      <WebView
        ref={(r) => (this.webView[index] = r)}
        style={{ flex: 1 }}
        allowsLinkPreview={true}
        originWhitelist={['*']}
        source={{ html: `<script>content=${JSON.stringify(content)}</script>${html}` }}
        scrollEnabled={true}
        allowUniversalAccessFromFileURLs={true}
        allowFileAccessFromFileURLs={true}
        allowFileAccess={true}
        mixedContentMode='always'
        bounces={false}
        showsVerticalScrollIndicator={true}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        allowsFullscreenVideo={true}
        onShouldStartLoadWithRequest={(event) => {
          if (event.url.toLowerCase().endsWith('.apk')) {
            // disallow download apk
            alert(i18n2.t('OpsNotAllowed'));
            return false;
          }

          return true;
        }}
        onMessage={async (evt) => {
          const data = JSON.parse(evt.nativeEvent.data);
          switch (data.command) {
            case 'openLink':
              this.openLink(data);
              break;
            case 'setAnswer':
              this.setAnswer(data);
              break;
            case 'goToBible':
              this.goToBible(data);
              break;
            case 'shareAnswer':
              if (!answers[data.questionId]) {
                Alert.alert(i18n2.t('Common.Information'), i18n2.t('NoAnswer'));
              } else {
                this.questionId = data.questionId;
                this.showShareAnswer();
              }
              break;
            case 'onScroll':
              {
                const hideNavBar = this.lastScrollTop < data.scrollTop;
                if (hideNavBar) {
                  this.startHideNavBar();
                } else {
                  this.startShowNavBar();
                }

                this.lastScrollTop = data.scrollTop;
                this.debouncedSaveScrollPosition(data.scrollTop);
              }
              break;
            case 'showNavBar':
              if (this.hideNavBar) {
                this.startShowNavBar();
              } else {
                this.startHideNavBar();
              }
              break;
            case 'loaded':
              // delay load bible reference
              this.parseBibleRef(this.webView[index], data.value);

              // delay load images (TODO: load from base64 src from local storage for offline mode)
              this.delayLoadImages(this.webView[index], content);
              break;
            case 'goToDiscussion':
              this.goToDiscussion({ questionId: data.questionId });
              break;
            case 'playAudio':
              this.playingAudio = data;
              await this.context.sound.loadAsync(data.value);
              await this.context.sound.playAsync();
              this.context.setAudioPlayerMinimized(false);
              break;
            case 'goToPage':
              this.goToPage(data);
              break;
            case 'showMedia':
              this.showMedia(data);
              break;
            default:
              break;
          }
        }}
      />
    );
  };

  renderBottomNavBar = () => {
    const { className, weekName } = this.props.route.params;
    const { content, answers } = this.state;
    const showShareButton = this.free === true && !this.isLessonWebUrl;
    const showExportButton = !this.isLessonWebUrl;
    return (
      <View
        style={{
          position: 'absolute',
          bottom: 0,
          backgroundColor: Colors.lightBlue,
          height: this.bottomNavBarHeight,
          width: Dimensions.get('window').width
        }}>
        <View
          style={{
            paddingTop: 7,
            flexDirection: 'row'
          }}>
          <View style={{ flex: 1, alignItems: 'center' }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => {
                this.props.navigation.popToTop();
              }}>
              {getImage('home')}
            </TouchableOpacity>
            <Text style={{ marginTop: 3, fontSize: 12 }}>{i18n2.t('Home')}</Text>
          </View>
          {showShareButton && (
            <View style={{ flex: 1, alignItems: 'center' }}>
              <TouchableOpacity
                activeOpacity={1}
                onPress={() => {
                  const { className, weekName } = this.props.route.params;
                  Share.share(
                    Platform.OS === 'ios'
                      ? {
                          message: `${className}:${weekName}`,
                          url: getCurrentUser().getDownloadServer() + `/#/lesson/${this.classId}/${this.sessionIndex}`
                        }
                      : {
                          title: `${i18n2.t('iDigest')}-${className}-${weekName}`,
                          message:
                            getCurrentUser().getDownloadServer() + `/#/lesson/${this.classId}/${this.sessionIndex}`
                        },
                    { subject: `${i18n2.t('iDigest')}-${className}-${weekName}` }
                  );
                }}>
                {getImage('export')}
              </TouchableOpacity>
              <Text style={{ marginTop: 3, fontSize: 12 }}>{i18n2.t('Common.Share')}</Text>
            </View>
          )}
          {showExportButton && (
            <View style={{ flex: 1, alignItems: 'center' }}>
              <TouchableOpacity
                activeOpacity={1}
                onPress={() => {
                  this.props.navigation.navigate('Export', {
                    className,
                    weekName,
                    answers,
                    content
                  });
                }}>
                {getImage('exportPrint')}
              </TouchableOpacity>
              <Text style={{ marginTop: 3, fontSize: 12 }}>{i18n2.t('Export')}</Text>
            </View>
          )}
          <View style={{ flex: 1, alignItems: 'center' }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => {
                this.props.navigation.navigate('Note', { classId: this.classId });
              }}>
              {getImage('note')}
            </TouchableOpacity>
            <Text style={{ marginTop: 3, fontSize: 12 }}>{i18n2.t('ClassNotebook')}</Text>
          </View>
        </View>
      </View>
    );
  };

  renderNavHeader = () => {
    const { className, weekName } = this.props.route.params;
    const { insets } = this.context;
    const leftWidth = 44;
    const rightWidth = 44;
    const centerWidth = Dimensions.get('window').width - insets.left - insets.right - leftWidth - rightWidth;
    return (
      <View
        style={{
          position: 'absolute',
          marginLeft: insets.left,
          marginRight: insets.right,
          top: this.topNavBarTitleTop,
          width: leftWidth + rightWidth + centerWidth
        }}>
        <View style={{ height: insets.top, backgroundColor: 'white' }} />
        <View
          style={{
            height: 44,
            flexDirection: 'row',
            borderBottomColor: '#e2e2e3',
            borderBottomWidth: 1,
            backgroundColor: 'white'
          }}>
          <View style={{ width: leftWidth, justifyContent: 'center' }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => {
                if (this.lessonWebViewCanGoBack) {
                  // we go back in web site first
                  this.lessonWebView.goBack();
                } else {
                  this.props.navigation.pop();
                }
              }}>
              {getImage('back')}
            </TouchableOpacity>
          </View>
          <View style={{ width: centerWidth, justifyContent: 'center' }}>
            <NavBarTitle title={className} subTitle={weekName} />
          </View>
          <View style={{ width: rightWidth, justifyContent: 'center' }}>
            {this.studyGroups.length > 0 ? (
              <TouchableOpacity
                activeOpacity={1}
                onPress={() => {
                  this.showMore();
                }}>
                {getImage('group')}
              </TouchableOpacity>
            ) : null}
          </View>
        </View>
      </View>
    );
  };

  renderWebLesson = () => {
    const { insets } = this.context;
    return (
      <>
        <View
          style={{
            flex: 1,
            marginTop: 42 + insets.top,
            marginBottom: 57 + insets.bottom
          }}>
          <WebView
            ref={(r) => {
              this.lessonWebView = r;
            }}
            onNavigationStateChange={(navState) => {
              this.lessonWebViewCanGoBack = navState.canGoBack;
            }}
            allowsLinkPreview={true}
            originWhitelist={['*']}
            source={{ uri: this.lessonFile }}
            scrollEnabled={true}
            allowUniversalAccessFromFileURLs={true}
            allowFileAccessFromFileURLs={true}
            allowFileAccess={true}
            mixedContentMode='always'
            bounces={false}
            showsVerticalScrollIndicator={true}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            allowsFullscreenVideo={true}
            onShouldStartLoadWithRequest={(event) => {
              if (event.url.toLowerCase().endsWith('.apk')) {
                // disallow download apk
                alert(i18n2.t('OpsNotAllowed'));
                return false;
              }

              return true;
            }}
          />
        </View>
        {this.renderNavHeader()}
        {this.renderBottomNavBar()}
      </>
    );
  };

  render() {
    if (this.isLessonWebUrl) {
      return this.renderWebLesson();
    }

    const { content, mediaModalVisible, currentMedia } = this.state;
    if (content.length <= 0) {
      // no content, not ready to render
      return <></>;
    }
    const { insets } = this.context;

    // TODO: Workaround to fix the map() doesn't work issue in SDK50
    const dayContent = [
      <View key={0} style={{ flex: 1 }}>
        {this.renderContent(content[0], 1)}
      </View>
    ];
    for (let index = 1; index < content.length; index++) {
      dayContent.push(
        <View key={index} style={{ flex: 1 }}>
          {this.renderContent(content[index], index + 1)}
        </View>
      );
    }

    return (
      <>
        <MediaModal visible={mediaModalVisible} media={currentMedia} onClose={this.hideMedia} />
        <PagerView
          style={{ flex: 1, marginLeft: insets.left, marginRight: insets.right }}
          ref={this.viewPager}
          scrollEnabled={Platform.OS === 'ios'}
          onPageSelected={(value) => {
            if (!this._skipOnPageSelectedFirstCall) {
              // FIXME: Current ViewPager has a bug that the first time it's called is always page_0
              this._skipOnPageSelectedFirstCall = 1;
              this.goToDay(this.state.day, true);
              return;
            }
            this.goToDay(value.nativeEvent.position + 1);
          }}
          initialPage={this.state.day - 1}
          keyboardDismissMode='on-drag'>
          {dayContent}
        </PagerView>
        {this.renderNavHeader()}
        {!this.isLessonWebUrl && this.renderDayNavBar()}
        {this.renderBottomNavBar()}
        {this.renderBottomSheet()}
      </>
    );
  }

  parseBibleRef = (webView, bibleContent) => {
    if (!Array.isArray(bibleContent)) {
      return;
    }

    initializeAsNeeded(require('@/assets/json/bible.json'));
    const setBibleResult = [];
    bibleContent.forEach((item) => {
      const value = getBibleRef(item.value);
      if (value.some((it) => it.type === 'bible')) {
        // changing html will create new nodes, so we update from last to first
        setBibleResult.unshift({ id: item.id, index: item.index, value });
      }
    });

    if (setBibleResult.length > 0) {
      webView.postMessage(JSON.stringify({ type: 'setBible', value: setBibleResult }));
    }
  };

  delayLoadImages = (webView, content) => {
    if (!this.delayLoadImage) {
      return;
    }
    let imageIndex = 0;
    content.session.content.forEach((item) => {
      if (item.type === 'image') {
        let src = item.value;
        if (!src.toLowerCase().startsWith('http://') && !src.toLowerCase().startsWith('https://')) {
          src = `${content.contentUrl}${src}?token=${content.accessToken}`;
        }
        webView.postMessage(JSON.stringify({ type: 'setImage', id: `image-${imageIndex++}`, src }));
      }
    });
  };

  getAnswers = (answers, validAnswers) => {
    if (Object.keys(answers).length !== Object.keys(validAnswers).length) {
      console.log('Fix invalid answers!');
      this.debouncedSaveAsync(this.classId, this.sessionIndex, validAnswers);
    }

    return validAnswers;
  };

  goToDiscussion = async ({ questionId, currentTag, isAnonymous = false, onDiscussionIdReadyCallbackAsync = null }) => {
    let discussionId = this.discussions[questionId]?.studyGroupId;
    if (!discussionId) {
      // create a new discussion if it doesn't exist
      const result = await globalThis.dsObject.createDiscussion(this.lessonId, this.sessionIndex, questionId);
      if (!result) {
        this.props.navigation.pop();
        return;
      }
      discussionId = result.body.studyGroupId;
    }

    if (onDiscussionIdReadyCallbackAsync) {
      await onDiscussionIdReadyCallbackAsync(discussionId);
    }

    logEvent('goToDiscussion', { classId: this.classId, discussionId });
    this.props.navigation.navigate('Chat', {
      chatId: discussionId,
      title: i18n2.t('Common.Share'),
      group: {
        groupId: discussionId,
        isClassDiscussionGroup: true,
        isGroupLeader: false
      },
      classId: this.classId,
      showGroupIcon: false,
      disableTagging: true,
      disableAtPeople: true,
      showGroups: this.studyGroups,
      currentTag,
      isAnonymous
    });

    this.reloadNeeded = true;
  };

  getLessonScreenByWeek = (week) => {
    const {
      currentProgress: { LessonScreen }
    } = this.context;

    const lessonScreen = cloneDeep(LessonScreen);
    const sessions = lessonScreen.sessions;
    if (!Array.isArray(sessions)) {
      // cannot find sessions, need to re-enter the LessonScreen
      this.props.navigation.pop();
      return null;
    }

    if (typeof week === 'number') {
      // if week is a number, we will navigate depending on currentSessionIndex (+/-)
      const newWeek = lessonScreen.sessionIndex + week;
      if (newWeek >= 0 && newWeek < sessions.length) {
        // valid new week
        const item = sessions[newWeek];
        lessonScreen.file = item.lesson;
        lessonScreen.sessionIndex = newWeek;
        // update className/weekName for navigation title/subTitle
        lessonScreen.className = item.navTitle ?? lessonScreen.bookTitle;
        lessonScreen.weekName = item.navSubTitle ?? item.title;
        return lessonScreen;
      }
    } else if (typeof week === 'string') {
      // if week is a number, we will navigate depending on the lesson json
      // Update for new week
      lessonScreen.file = week;
      for (let i = 0; i < sessions.length; i++) {
        const item = sessions[i];
        if (item.lesson.toLowerCase() === week.toLowerCase()) {
          lessonScreen.sessionIndex = i;
          // update className/weekName for navigation title/subTitle
          lessonScreen.className = item.navTitle ?? lessonScreen.bookTitle;
          lessonScreen.weekName = item.navSubTitle ?? item.title;
          return lessonScreen;
        }
      }
    }

    return null;
  };

  goToPage = ({ week, day }) => {
    const lessonScreen = this.getLessonScreenByWeek(week);
    if (lessonScreen) {
      console.log(JSON.stringify(lessonScreen, undefined, 2));
      // update session index for table of content page
      getCurrentUser().setPropertyAsync(`LessonProgress.${lessonScreen.lesson.classId}`, {
        sessionId: lessonScreen.sessionIndex,
        day,
        scrollTop: 0
      });

      // update state for new class session/week
      // replace navigation stack
      this.props.navigation.dispatch(
        CommonActions.reset({
          index: 2,
          routes: [
            { name: 'BottomTab' },
            {
              name: 'LessonTableOfContent',
              params: { lesson: lessonScreen.lesson }
            },
            {
              name: 'Lesson',
              params: lessonScreen
            }
          ]
        })
      );
      return;
    }

    // otherwise navigate between days
    if (typeof day === 'number') {
      this.goToDay(day);
      return;
    }
  };
}

const NavItem = (props) => {
  const { image, day, selected, showRedDot, onPress } = props;
  return (
    <TouchableOpacity activeOpacity={1} onPress={onPress}>
      {getImage(image + (selected ? 'On' : 'Off'))}
      <View
        style={{
          position: 'absolute',
          height: '100%',
          width: '100%',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
        <Text style={{ fontSize: getCurrentUser().getXLargeFontSize(), color: selected ? 'white' : Colors.darkBlue }}>
          {day}
        </Text>
        {showRedDot ? <RedDot top={1} right={3} /> : null}
      </View>
    </TouchableOpacity>
  );
};
