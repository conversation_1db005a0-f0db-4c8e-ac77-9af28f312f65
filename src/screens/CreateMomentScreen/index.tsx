/* eslint-disable @typescript-eslint/no-explicit-any */

import {
  CreateMomentContext,
  FormDataType,
  MomentFormTypeEnum,
  SelectGroupValueType
} from '@/screens/CreateMomentScreen/utils';
import { KeyboardView, LoadingIndicator } from '@/components';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { ScrollView, View } from 'react-native';

import AcceptRule from '@/screens/CreateMomentScreen/components/AcceptRule';
import { AppContext } from '@/context/AppContext';
import { OkButton } from '@/components/navigation/NavigationButtons';
import PersonContent from '@/screens/CreateMomentScreen/components/PersonContent';
import SelectFormType from '@/screens/CreateMomentScreen/components/SelectFormType';
import SelectGroup from '@/screens/CreateMomentScreen/components/SelectGroup';
import TitleContent from '@/screens/CreateMomentScreen/components/TitleContent';
import UrlContent from '@/screens/CreateMomentScreen/components/UrlContent';
import { i18n2 } from '@/utils/i18n2';
import { useNavigation } from '@react-navigation/native';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';

const CreateMomentScreen = () => {
  const navigation = useNavigation<any>();
  const [busy, setBusy] = useState<boolean>(false);
  const [showOk, setShowOk] = useState<boolean>(false);
  const [accept, setAccept] = useState<boolean>(false);
  const [formType, setFormType] = useState<MomentFormTypeEnum>();
  const [formData, setFormData] = useState<FormDataType>({});
  const [groupsInfo, setGroupsInfo] = useState<SelectGroupValueType>({ able: false });
  const { insets } = useContext(AppContext) as any;

  const stopDoubleClick = useRef<boolean>(false);

  const createAsync = useCallback(async () => {
    if (stopDoubleClick.current) {
      return;
    }
    try {
      stopDoubleClick.current = true;
      const { url, title, image, description, media } = formData;
      const { able, list } = groupsInfo;
      const groups = able ? list?.map((item) => item.groupId) : undefined;
      console.log('groupsInfo', groupsInfo);
      const body: any = {
        url,
        title,
        description,
        groups,
        type: formType
      };
      if (formType === MomentFormTypeEnum.URL) {
        body.image = image;
      } else {
        body.media = media?.filter((item) => !!item.filename)?.map((item) => item.filename);
      }
      const result = await globalThis.dsObject.createMoment(body);
      if (result) {
        globalThis.eventEmitter.emit('onMomentCreated', result.body);
        navigation.goBack();
      }
    } finally {
      stopDoubleClick.current = false;
    }
  }, [formData, formType, groupsInfo, navigation]);
  useEffect(() => {
    if (accept && formType !== undefined) {
      if (groupsInfo?.able && !groupsInfo?.list?.length) {
        setShowOk(false);
      } else if (formType === MomentFormTypeEnum.URL && formData?.url && formData?.image) {
        setShowOk(true);
      } else if (formType === MomentFormTypeEnum.PERSON && formData?.title && formData?.media) {
        setShowOk(!!formData?.media?.filter((item) => !!item.filename)?.length);
      }
    } else {
      setShowOk(false);
    }
  }, [accept, formType, formData, groupsInfo, navigation]);
  useSetNavigationOptions({
    title: i18n2.t('MomentsUI.EditResource'),
    headerRight: () => {
      return (
        <OkButton
          disabled={!showOk}
          right={true}
          onPress={() => {
            createAsync();
          }}
        />
      );
    }
  });
  return (
    <CreateMomentContext.Provider value={{ setBusy }}>
      <KeyboardView>
        <View style={{ flex: 1 }}>
          <ScrollView
            style={{
              marginBottom: insets.bottom,
              paddingLeft: insets.left,
              paddingRight: insets.right
            }}>
            <AcceptRule accept={accept} setAccept={setAccept} />
            {accept && (
              <>
                <SelectFormType formType={formType} setFormType={setFormType} />
                {formType !== undefined && (
                  <>
                    <TitleContent title={i18n2.t('CreateMomentScreen.EditContent')}>
                      {formType === MomentFormTypeEnum.URL && <UrlContent onChange={setFormData} data={formData} />}
                      {formType === MomentFormTypeEnum.PERSON && (
                        <PersonContent onChange={setFormData} data={formData} />
                      )}
                    </TitleContent>
                    <TitleContent title={i18n2.t('CreateMomentScreen.PublishScope')}>
                      <SelectGroup value={groupsInfo} onChange={setGroupsInfo} />
                    </TitleContent>
                  </>
                )}
              </>
            )}
          </ScrollView>
          {busy ? <LoadingIndicator /> : null}
        </View>
      </KeyboardView>
    </CreateMomentContext.Provider>
  );
};

export default CreateMomentScreen;
