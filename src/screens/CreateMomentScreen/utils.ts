import React from 'react';
import { SelectMediaValue } from '@/screens/CreateMomentScreen/components/SelectMedia';

export interface UrlFormDataProps {
  url: string;
  title: string;
  description?: string;
  image: string;
}

export interface PersonFormDataProps {
  title?: string;
  media: SelectMediaValue[];
}

export type FormDataType = Partial<UrlFormDataProps & PersonFormDataProps>;

export enum MomentFormTypeEnum {
  PERSON = 1,
  URL = 0
}

export const CreateMomentContext = React.createContext<{
  setBusy?: (v: boolean) => void;
}>({});

export interface SelectGroupValueType {
  able: boolean;
  list?: { name: string; groupId: string }[];
}
