import { TouchableWithoutFeedback, View } from 'react-native';
import { Image } from 'expo-image';
import { Colors } from '@/styles/colors';
import React from 'react';
interface UrlSelectImagesProps {
  images: string[];
  value?: string | undefined;
  onChange: (v: string) => void;
}
const UrlSelectImages: React.FC<UrlSelectImagesProps> = ({ images, value, onChange }) => {
  return (
    <View style={{ flexWrap: 'wrap', flexDirection: 'row' }}>
      {images.map((item, index) => {
        const selected = value === item;
        return (
          <TouchableWithoutFeedback
            key={item + index}
            onPress={() => {
              onChange(item);
            }}>
            <Image
              style={{
                margin: 5,
                height: 100,
                width: 100,
                borderColor: 'red',
                borderWidth: selected ? 3 : 0,
                backgroundColor: Colors.buttonBackground,
                opacity: selected ? 1 : 0.5
              }}
              transition={100}
              cachePolicy={'memory-disk'}
              contentFit={'fill'}
              source={{ uri: item }}
            />
          </TouchableWithoutFeedback>
        );
      })}
    </View>
  );
};

export default UrlSelectImages;
