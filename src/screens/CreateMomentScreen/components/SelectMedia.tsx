import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';

import { Image, StyleSheet, TouchableOpacity, View } from 'react-native';
import { LoadingIndicator, ModalPopup, getImage } from '@/components';
import React, { useCallback, useContext, useRef, useState } from 'react';

import { AppContext } from '@/context/AppContext';
import { Icon } from '@rneui/themed';
import { i18n2 } from '@/utils/i18n2';
import { showMessage } from '@/components/MessageBox';

enum UploadStatus {
  local,
  uploading,
  uploaded,
  uploadFail
}

export interface SelectMediaValue {
  url: string;
  type: ImagePicker.ImagePickerAsset['type'] | undefined;
  status: UploadStatus;
  filename?: string;
}

interface SelectMediaProps {
  value?: SelectMediaValue[] | undefined;
  onChange: (value: SelectMediaValue[]) => void;
}

const SelectMedia: React.FC<SelectMediaProps> = ({ value = [], onChange }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const pickInfo = useRef<{ max: number; mediaTypes: ImagePicker.MediaTypeOptions }>({
    max: 9,
    mediaTypes: ImagePicker.MediaTypeOptions.Images
  });
  const waitList = useRef<SelectMediaValue[]>([]);
  const [visible, setVisible] = useState<boolean>(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const context = useContext(AppContext) as any;
  const handleAddMedia = async (sureAdd?: boolean) => {
    if (!value?.length && !sureAdd) {
      setVisible(true);
      return;
    }
    const { max, mediaTypes } = pickInfo.current;
    const mediaOptions: ImagePicker.ImagePickerOptions = {
      mediaTypes,
      allowsMultipleSelection: max > 1, // 启用多选
      quality: 0.75,
      selectionLimit: max - value?.length,
      videoMaxDuration: 30
    };
    setLoading(true);
    const isVideo = mediaTypes === ImagePicker.MediaTypeOptions.Videos;
    const result = await ImagePicker.launchImageLibraryAsync(mediaOptions);
    setLoading(false);
    if (!result.canceled) {
      const maxSize = isVideo ? 10 * 1024 * 1024 : 5 * 1024 * 1024;
      const validMediaList: SelectMediaValue[] = [];
      let isMaxError = false;
      for (const media of result.assets) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const fileInfo: any = await FileSystem.getInfoAsync(media.uri);
        if (fileInfo.size && fileInfo.size <= maxSize) {
          validMediaList.push({
            url: media.uri,
            type: media.type,
            status: UploadStatus.local
          });
        } else {
          isMaxError = true;
        }
      }
      if (isMaxError) {
        showMessage({
          message: i18n2.t('CreateMomentScreen.FileSizeExceeded').replace('5', isVideo ? '10' : '5'),
          duration: 2000,
          type: 'warning'
        });
      }
      // 如果有合法的文件，更新状态
      if (validMediaList.length > 0) {
        if (value?.find((item) => item.status === UploadStatus.uploading)) {
          onChange([...value, ...validMediaList]);
          waitList.current = validMediaList;
        } else {
          upload([...value, ...validMediaList]);
        }
      }
    }
  };

  const handleRemoveMedia = (index: number) => {
    const updatedValue = value.filter((_, i) => i !== index);
    onChange(updatedValue);
  };

  const upload = useCallback(
    async (list: SelectMediaValue[]) => {
      waitList.current = [];
      const originalIndex: number[] = [];
      const localMedias = list?.filter((item, index) => {
        const isLocal = item.status === UploadStatus.local;
        if (isLocal) {
          originalIndex.push(index);
        }
        return isLocal;
      });
      console.log('localMedias?.length', localMedias?.length);
      if (localMedias?.length) {
        // status to `uploading`
        const newValue = list.map((item, index) => {
          if (item.status === UploadStatus.local) {
            return { ...item, status: UploadStatus.uploading, index }; // 记录原始索引
          }
          return item;
        });
        onChange(newValue);

        // upload all
        const uploadResults = await Promise.all(
          localMedias.map(async (media) => {
            try {
              const resp = await globalThis.dsObject.uploadMoments(media.url);
              console.log('resp', resp);
              const filename = JSON.parse(resp?.body || '{}')?.filename;
              if (filename) {
                return { ...media, status: UploadStatus.uploaded, filename };
              } else {
                return { ...media, status: UploadStatus.uploadFail };
              }
            } catch (error) {
              return { ...media, status: UploadStatus.uploadFail };
            }
          })
        );

        const updatedValue = list.map((item, index) => {
          if (index === originalIndex[0]) {
            originalIndex.shift();
            return uploadResults.shift() || item;
          } else {
            return item;
          }
        });
        if (waitList.current?.length) {
          upload([...updatedValue, ...waitList.current]);
        } else {
          onChange(updatedValue);
        }
      }
    },
    [onChange]
  );

  return (
    <>
      {loading && <LoadingIndicator />}
      <View style={styles.container}>
        {value.map((item, index) => (
          <View key={index} style={styles.mediaContainer}>
            <TouchableOpacity style={styles.closeContainer} onPress={() => handleRemoveMedia(index)}>
              <Icon name='close' type='material' size={20} color='#fff' />
            </TouchableOpacity>
            <Image source={{ uri: item.url }} style={styles.mediaThumbnail} />

            {item.type === 'video' && item.status === UploadStatus.uploaded && (
              <Icon
                name='play-circle-outline'
                type='material'
                size={30}
                color='#fff'
                containerStyle={styles.playIcon}
              />
            )}

            {item.status !== UploadStatus.uploaded && (
              <View style={styles.overlay}>
                <Icon
                  name={
                    item.status === UploadStatus.uploading
                      ? 'cloud-upload'
                      : item.status === UploadStatus.uploadFail
                        ? 'error'
                        : 'hourglass-empty'
                  }
                  type='material'
                  size={30}
                  color='#fff'
                />
              </View>
            )}
          </View>
        ))}
        {value.length < pickInfo.current.max && (
          <TouchableOpacity
            onPress={() => {
              handleAddMedia();
            }}
            style={styles.addButton}>
            <Icon name='add' type='material' size={40} color='#999' />
          </TouchableOpacity>
        )}
      </View>
      <ModalPopup
        insets={context.insets}
        visible={visible}
        showDivider={true}
        setNotVisible={() => {
          setVisible(false);
        }}
        items={[
          {
            renderIcon: () => getImage('camera'),
            text: i18n2.t('CreateMomentScreen.UploadVideo'),
            onPress: () => {
              setVisible(false);
              pickInfo.current = { max: 1, mediaTypes: ImagePicker.MediaTypeOptions.Videos };
              setTimeout(() => {
                handleAddMedia(true);
              }, 500);
            }
          },
          {
            renderIcon: () => getImage('photo'),
            text: i18n2.t('CreateMomentScreen.UploadImage'),
            onPress: () => {
              setVisible(false);
              pickInfo.current = { max: 9, mediaTypes: ImagePicker.MediaTypeOptions.Images };
              setTimeout(() => {
                handleAddMedia(true);
              }, 500);
            }
          }
        ]}
        iconsPerRow={2}
        showCancel={true}
        hideOnPress={true}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10
  },
  mediaContainer: {
    position: 'relative',
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#acacac'
  },
  closeContainer: {
    width: 24,
    height: 24,
    backgroundColor: 'rgba(0,0,0,0.7)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    borderRadius: 12,
    right: -6,
    top: -6,
    zIndex: 2
  },
  mediaThumbnail: {
    width: '100%',
    height: '100%',
    borderRadius: 10
  },
  playIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -15 }, { translateY: -15 }]
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  addButton: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8
  }
});

export default SelectMedia;
