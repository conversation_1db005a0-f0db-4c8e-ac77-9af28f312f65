import { CheckBox, Icon } from '@rneui/themed';
import React, { useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { Button } from '@/components';
import { Colors } from '@/styles/colors';
import MultipleGroup from '@/components/MultipleGroup';
import { SelectGroupValueType } from '@/screens/CreateMomentScreen/utils';
import { getFontSize } from '@/utils/getFontSize';
import { i18n2 } from '@/utils/i18n2';

interface SelectGroupProps {
  value: SelectGroupValueType;
  onChange: (v: SelectGroupValueType) => void;
}

const SelectGroup: React.FC<SelectGroupProps> = ({ value, onChange }) => {
  const [visible, setVisible] = useState<boolean>(false);
  return (
    <>
      <CheckBox
        containerStyle={styles.checkBox}
        title={i18n2.t('CreateMomentScreen.NoRestriction')}
        checkedColor={Colors.darkBlue}
        checked={!value.able}
        onPress={() => {
          onChange({ able: false });
        }}
        checkedIcon='dot-circle-o'
        uncheckedIcon='circle-o'
      />
      <CheckBox
        title={i18n2.t('CreateMomentScreen.SpecificGroup')}
        containerStyle={styles.checkBox}
        checkedColor={Colors.darkBlue}
        checked={value.able}
        onPress={() => {
          onChange({ able: true, list: value?.list || [] });
        }}
        checkedIcon='dot-circle-o'
        uncheckedIcon='circle-o'
      />
      <Button
        disabled={!value.able}
        title={i18n2.t('CreateMomentScreen.SelectGroup')}
        style={{ backgroundColor: '#ffffff', width: 140 }}
        textStyle={{ lineHeight: getFontSize().mediumFontSize + 4 }}
        onPress={() => {
          setVisible(true);
        }}
      />
      <View style={styles.selectedList}>
        {value?.list?.map((item) => (
          <TouchableOpacity
            key={item.groupId}
            style={styles.selected}
            onPress={() => {
              onChange({
                able: true,
                list: value?.list?.filter((v) => v.groupId !== item.groupId) || []
              });
            }}>
            <Text style={styles.selectedText} numberOfLines={1}>
              {item.name}
            </Text>
            <Icon name='close' type='material' size={14} color={Colors.darkBlue} />
          </TouchableOpacity>
        ))}
      </View>
      {value?.able && (
        <MultipleGroup
          visible={visible}
          onCancel={() => {
            setVisible(false);
          }}
          onConfirm={(list) => {
            onChange({
              able: true,
              list
            });
            setVisible(false);
          }}
          defaultSelect={value?.list}
        />
      )}
    </>
  );
};

export default SelectGroup;

const styles = StyleSheet.create({
  checkBox: {
    backgroundColor: 'transparent',
    padding: 0,
    marginLeft: 0
  },
  selectedList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    paddingBottom: 20
  },
  selected: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 10,
    alignItems: 'center',
    justifyContent: 'center',
    height: 30,
    borderRadius: 15,
    flexDirection: 'row',
    width: 120
  },
  selectedText: {
    fontSize: 12,
    color: Colors.answerText,
    marginRight: 4
  }
});
