import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Colors } from '@/styles/colors';
import { getFontSize } from '@/utils/getFontSize';

interface FormItemProps {
  label: string;
  children: React.ReactNode;
}

const FormItem: React.FC<FormItemProps> = ({ label, children }) => {
  const { smallFontSize } = getFontSize();
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { fontSize: smallFontSize, lineHeight: smallFontSize + 4 }]}>{label}</Text>
      {children}
    </View>
  );
};

export default FormItem;

const styles = StyleSheet.create({
  container: {
    marginVertical: 8
  },
  title: {
    color: Colors.text,
    marginBottom: 8
  }
});
