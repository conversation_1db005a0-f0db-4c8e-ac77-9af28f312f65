import { Text, TouchableOpacity, View } from 'react-native';

import { CheckBox } from '@rneui/themed';
import { Colors } from '@/styles/colors';
import React from 'react';
import TitleContent from '@/screens/CreateMomentScreen/components/TitleContent';
import { getFontSize } from '@/utils/getFontSize';
import { i18n2 } from '@/utils/i18n2';

interface AcceptRuleProps {
  accept: boolean;
  setAccept: (accept: boolean) => void;
}

const AcceptRule: React.FC<AcceptRuleProps> = ({ accept, setAccept }) => {
  const { mediumFontSize: fontSize, smallFontSize } = getFontSize();
  return (
    <TitleContent title={i18n2.t('CreateMomentScreen.AgreeTerms')}>
      <TouchableOpacity
        activeOpacity={1}
        style={{ flexDirection: 'row' }}
        onPress={() => {
          setAccept(!accept);
        }}>
        <CheckBox
          containerStyle={{ padding: 0, margin: 0, backgroundColor: 'transparent' }}
          textStyle={{ fontSize }}
          checkedColor={Colors.darkBlue}
          checked={accept}
          onPress={(e) => {
            e.stopPropagation();
            setAccept(!accept);
          }}
        />
        <View style={{ flex: 1 }}>
          <Text style={{ fontSize, lineHeight: fontSize + 4, marginBottom: 8 }}>
            {i18n2.t('MomentsUI.AcceptTerms1')}
          </Text>
          <Text style={{ fontSize: smallFontSize, lineHeight: smallFontSize + 4, marginBottom: 8 }}>
            {i18n2.t('MomentsUI.AcceptTerms2')}
          </Text>
        </View>
      </TouchableOpacity>
    </TitleContent>
  );
};

export default AcceptRule;
