import { TextInput, View } from 'react-native';
import { PersonFormDataProps } from '@/screens/CreateMomentScreen/utils';
import React from 'react';
import FormItem from '@/screens/CreateMomentScreen/components/FormItem';
import SelectMedia from '@/screens/CreateMomentScreen/components/SelectMedia';
import { getFontSize } from '@/utils/getFontSize';
import { i18n2 } from '@/utils/i18n2';

interface PersonContentProps {
  data: Partial<PersonFormDataProps>;
  onChange: (v: Partial<PersonFormDataProps>) => void;
}

const PersonContent: React.FC<PersonContentProps> = ({ data, onChange }) => {
  const { smallFontSize } = getFontSize();
  return (
    <View>
      <FormItem label={i18n2.t('CreateMomentScreen.InputTextContent')}>
        <TextInput
          multiline
          numberOfLines={4}
          style={{
            backgroundColor: '#ffffff',
            padding: 8,
            lineHeight: smallFontSize + 4,
            fontSize: smallFontSize,
            height: 16 + 4 * (smallFontSize + 4),
            textAlignVertical: 'top'
          }}
          value={data?.title}
          onChangeText={(v) => {
            onChange({ ...data, title: v });
          }}
          placeholder={i18n2.t('Common.InputPrompt')}
        />
      </FormItem>
      <FormItem label={i18n2.t('CreateMomentScreen.ImageOrVideo')}>
        <SelectMedia
          value={data?.media}
          onChange={(v) => {
            onChange({ ...data, media: v });
          }}
        />
      </FormItem>
    </View>
  );
};

export default PersonContent;
