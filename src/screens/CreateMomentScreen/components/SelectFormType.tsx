import { Text, TouchableOpacity, View } from 'react-native';

import { CheckBox } from '@rneui/themed';
import { Colors } from '@/styles/colors';
import { MomentFormTypeEnum } from '@/screens/CreateMomentScreen/utils';
import React from 'react';
import TitleContent from '@/screens/CreateMomentScreen/components/TitleContent';
import { getFontSize } from '@/utils/getFontSize';
import { i18n2 } from '@/utils/i18n2';

interface RadioCardProps {
  checked: boolean;
  setChecked: (checked: boolean) => void;
  title: string;
  description: string;
}
const RadioCard: React.FC<RadioCardProps> = ({ checked, setChecked, title, description }) => {
  const { mediumFontSize: fontSize, smallMinusFontSize } = getFontSize();
  return (
    <TouchableOpacity
      onPress={() => {
        setChecked(!checked);
      }}
      style={{
        flexDirection: 'row',
        backgroundColor: '#ffffff',
        paddingVertical: 10,
        paddingRight: 10,
        flex: 1,
        borderWidth: 1,
        borderColor: Colors.buttonBorder,
        borderRadius: 10,
        overflow: 'hidden'
      }}>
      <CheckBox
        containerStyle={{ padding: 0, margin: 0 }}
        checkedColor={Colors.darkBlue}
        checked={checked}
        onPress={(e) => {
          e.stopPropagation();
          setChecked(!checked);
        }}
        checkedIcon='dot-circle-o'
        uncheckedIcon='circle-o'
      />
      <View style={{ flex: 1 }}>
        <Text style={{ fontSize, fontWeight: 'bold', lineHeight: fontSize + 4 }}>{title}</Text>
        <Text style={{ fontSize: smallMinusFontSize, lineHeight: smallMinusFontSize + 4 }}>{description}</Text>
      </View>
    </TouchableOpacity>
  );
};

interface SelectFormTypeProps {
  formType?: MomentFormTypeEnum | undefined;
  setFormType: (formType?: MomentFormTypeEnum) => void;
}

const SelectFormType: React.FC<SelectFormTypeProps> = ({ formType, setFormType }) => {
  return (
    <TitleContent title={i18n2.t('CreateMomentScreen.SelectType')}>
      <View style={{ flexDirection: 'row', overflow: 'hidden', gap: 10 }}>
        <RadioCard
          title={i18n2.t('CreateMomentScreen.PersonalDevotion')}
          description={i18n2.t('CreateMomentScreen.SharePersonalContent')}
          checked={formType === MomentFormTypeEnum.PERSON}
          setChecked={(value) => {
            setFormType(value ? MomentFormTypeEnum.PERSON : undefined);
          }}
        />
        <RadioCard
          title={i18n2.t('CreateMomentScreen.ExternalResourceLink')}
          description={i18n2.t('CreateMomentScreen.ShareExternalResource')}
          checked={formType === MomentFormTypeEnum.URL}
          setChecked={(value) => {
            setFormType(!value ? undefined : MomentFormTypeEnum.URL);
          }}
        />
      </View>
    </TitleContent>
  );
};

export default SelectFormType;
