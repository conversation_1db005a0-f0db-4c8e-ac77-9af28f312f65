import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Colors } from '@/styles/colors';
import { getFontSize } from '@/utils/getFontSize';

interface TitleContentProps {
  title: string;
  children: React.ReactNode;
}

const TitleContent: React.FC<TitleContentProps> = ({ title, children }) => {
  const { mediumFontSize: fontSize } = getFontSize();
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { fontSize, lineHeight: fontSize + 4 }]}>{title}</Text>
      {children}
    </View>
  );
};

export default TitleContent;

const styles = StyleSheet.create({
  container: {
    margin: 6,
    padding: 10,
    backgroundColor: Colors.answerBackground
  },
  title: {
    color: Colors.text,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8
  }
});
