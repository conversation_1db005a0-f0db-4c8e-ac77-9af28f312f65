import React, { useState, useRef, useCallback } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView, Keyboard, Dimensions, Image } from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';
import { getBibleData, IBibleData, getDefaultTranslation, ETranslation, IBook } from '@/utils/bibleUtils';
import { getFontSize } from '@/utils/getFontSize';
import Title from '@/screens/HomeScreen/components/Title';
import BibleCategorySelector from '@/components/BibleCategorySelector';
import SearchBar from '@/components/SearchBar';
import { getObjectAsync } from '@/dataStorage/localStorage';
import { i18n2 } from '@/utils/i18n2';

const BibleBooks: React.FC = () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const navigation = useNavigation<any>();
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchText, setSearchText] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const bibleData = useRef<IBibleData>(getBibleData()).current;

  const getCurrentLanguageFields = (): {
    translation: ETranslation;
    bookNameField: keyof IBook;
    bookAbbrField: keyof IBook;
  } => {
    const defaultTranslation = getDefaultTranslation();

    if (defaultTranslation === ETranslation.asv) {
      return {
        translation: ETranslation.asv,
        bookNameField: 'asv',
        bookAbbrField: 'abbr'
      };
    }

    if (defaultTranslation === ETranslation.cuvt) {
      return {
        translation: ETranslation.cuvt,
        bookNameField: 'cuvt',
        bookAbbrField: 'abbr_t'
      };
    }

    return {
      translation: ETranslation.cuvs,
      bookNameField: 'cuvs',
      bookAbbrField: 'abbr_s'
    };
  };

  const { bookNameField, bookAbbrField } = getCurrentLanguageFields();

  const getBooksByCategory = (categoryId: string) => {
    if (!bibleData) {
      return [];
    }

    if (categoryId === 'all') {
      return bibleData.books;
    }

    const category = bibleData.categories.find((cat) => cat.id === categoryId);
    if (!category) {
      return [];
    }

    return bibleData.books.filter(
      (book) => book.bookId >= category.bookRange[0] && book.bookId <= category.bookRange[1]
    );
  };

  const isOldTestament = (bookId: number) => bookId <= 39;

  const filteredBooks = getBooksByCategory(activeCategory);

  const oldTestamentBooks = filteredBooks.filter((book) => isOldTestament(book.bookId));
  const newTestamentBooks = filteredBooks.filter((book) => !isOldTestament(book.bookId));

  const handleCategoryPress = (categoryId: string) => {
    setActiveCategory((prevCategory) => (prevCategory === categoryId ? 'all' : categoryId));
  };

  // 加载上次选择的翻译版本
  const [savedTranslation, setSavedTranslation] = useState<ETranslation | null>(null);

  useFocusEffect(
    useCallback(() => {
      const loadSavedTranslation = async () => {
        const saved = await getObjectAsync('bible.search.translation');
        console.log('saved.translation', saved.translation);
        if (saved && saved.translation) {
          setSavedTranslation(saved.translation);
        }
      };
      loadSavedTranslation();
    }, [])
  );

  // 处理搜索提交
  const handleSearchSubmit = () => {
    if (searchText.trim()) {
      Keyboard.dismiss();
      // 导航到搜索页面，传递关键词、分类和翻译版本
      console.log('savedTranslation', savedTranslation);
      navigation.navigate('BibleSearch', {
        keyword: searchText.trim(),
        categoryId: activeCategory,
        // 使用保存的翻译版本或默认版本
        translation: savedTranslation || getDefaultTranslation()
      });
    }
  };

  useSetNavigationOptions({
    title: i18n2.t('BibleBooksScreen.Title'),
    headerRight: () => (
      <TouchableOpacity style={styles.bibleButton} onPress={() => navigation.navigate('BibleSetting' as never)}>
        <Image style={styles.bibleIcon} source={require('@/assets/images/icon-Bible2.jpg')}></Image>
      </TouchableOpacity>
    )
  });

  return (
    <ScrollView style={styles.booksContainer}>
      <BibleCategorySelector activeCategory={activeCategory} onCategoryChange={handleCategoryPress} />
      <View style={styles.searchContainer}>
        <SearchBar
          autoFocus={false}
          value={searchText}
          isCloseVisible={isSearching}
          onChangeText={setSearchText}
          onFocus={() => setIsSearching(true)}
          onClose={() => {
            Keyboard.dismiss();
            setSearchText('');
            setIsSearching(false);
          }}
          onSubmitEditing={handleSearchSubmit}
        />
      </View>

      {oldTestamentBooks.length > 0 && (
        <View style={styles.testamentSection}>
          {activeCategory === 'all' && (
            <Title title={i18n2.t('BibleBooksScreen.OldTestament')} style={styles.testamentHeader} />
          )}
          <View style={styles.booksGrid}>
            {oldTestamentBooks.map((book) => (
              <TouchableOpacity
                key={book.bookId}
                style={styles.bookButton}
                onPress={() =>
                  navigation.navigate('BibleChapter', {
                    bookId: book.bookId,
                    bookName: book[bookNameField]
                  })
                }
                onLongPress={() => {
                  setSearchText('');
                  navigation.navigate('BibleSearch', {
                    bookId: book.bookId,
                    translation: savedTranslation || getDefaultTranslation()
                  });
                }}>
                <Text style={styles.bookName}>{book[bookAbbrField]}</Text>
                <Text style={styles.bookAbbr}>{book[bookNameField]}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {newTestamentBooks.length > 0 && (
        <View style={styles.testamentSection}>
          {activeCategory === 'all' && (
            <Title title={i18n2.t('BibleBooksScreen.NewTestament')} style={styles.testamentHeader} />
          )}
          <View style={styles.booksGrid}>
            {newTestamentBooks.map((book) => (
              <TouchableOpacity
                key={book.bookId}
                style={styles.bookButton}
                onPress={() =>
                  navigation.navigate('BibleChapter', {
                    bookId: book.bookId,
                    bookName: book[bookNameField]
                  })
                }
                onLongPress={() => {
                  // 长按进入该书卷的搜索页面
                  navigation.navigate('BibleSearch', {
                    bookId: book.bookId,
                    // 使用保存的翻译版本或默认版本
                    translation: savedTranslation || getDefaultTranslation()
                  });
                }}>
                <Text style={styles.bookName}>{book[bookAbbrField]}</Text>
                <Text style={styles.bookAbbr}>{book[bookNameField]}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}
    </ScrollView>
  );
};

export default BibleBooks;

const styles = StyleSheet.create({
  bibleButton: {
    padding: 5
  },
  bibleIcon: {
    width: 32,
    height: 32
  },
  searchContainer: {
    marginVertical: 8
  },
  booksContainer: {
    flex: 1,
    paddingHorizontal: 15
  },
  testamentSection: {
    marginBottom: 20
  },
  testamentHeader: {
    marginBottom: 10
  },
  booksGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start'
  },
  bookButton: {
    width: (Dimensions.get('window').width - 30) / 4 - 2,
    height: (Dimensions.get('window').width - 30) / 4 - 2,
    margin: 1,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center'
  },
  bookName: {
    fontSize: getFontSize().mediumFontSize,
    textAlign: 'center',
    marginBottom: 4
  },
  bookAbbr: {
    fontSize: getFontSize().smallMinusFontSize,
    color: '#666',
    textAlign: 'center'
  }
});
