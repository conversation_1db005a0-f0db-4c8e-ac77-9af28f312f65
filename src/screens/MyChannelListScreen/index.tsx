import React, { useEffect, useState } from 'react';
import { View, ScrollView, Text, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Image } from 'expo-image';
import { getChannelImageUrl } from '@/utils/url';
import { useColumn } from '@/utils/lesson/Column';
import { getFontSize } from '@/utils/getFontSize';
import { IChannel } from '@/utils/channel/IChannel';

const MyChannelListScreen = () => {
  const [channels, setChannels] = useState<IChannel[]>([]);
  const { itemMargin, itemWidth } = useColumn();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const navigation = useNavigation<any>();

  useEffect(() => {
    initData();
  }, []);

  const initData = async () => {
    const [ownedResp, subscribesResp] = await Promise.all([
      global.dsObject.getChannelOwned(),
      global.dsObject.getChannelSubscribes()
    ]);
    const owned = ownedResp?.body || [];
    const subscribes = subscribesResp?.body || [];

    const allChannelsMap = new Map<string, IChannel>();
    [...owned, ...subscribes].forEach((channel: IChannel) => {
      allChannelsMap.set(channel.channelId.toString(), channel);
    });
    setChannels(Array.from(allChannelsMap.values()));
  };

  return (
    <ScrollView>
      <View style={{ padding: itemMargin, flexDirection: 'row', flexWrap: 'wrap' }}>
        {channels.map((channel) => (
          <TouchableOpacity
            key={channel.channelId}
            style={{ width: itemWidth, alignItems: 'center', marginBottom: itemMargin }}
            onPress={() =>
              navigation.navigate('ChannelDetail', {
                channelId: channel.channelId,
                channelName: channel.name
              })
            }>
            <Image
              source={{ uri: getChannelImageUrl(channel.channelId, 'cover.jpg') }}
              style={{
                width: itemWidth - itemMargin * 2,
                height: itemWidth - itemMargin * 2,
                borderRadius: itemWidth / 2,
                marginBottom: 8,
                backgroundColor: '#eee'
              }}
            />
            <Text style={{ fontSize: getFontSize().mediumFontSize }} numberOfLines={1}>
              {channel.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
};

export default MyChannelListScreen;
