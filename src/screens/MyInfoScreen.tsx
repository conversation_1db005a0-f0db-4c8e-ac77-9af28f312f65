import React, { useContext } from 'react';
import { AppContext } from '@/context/AppContext';
import { Dimensions, Text, View } from 'react-native';
import { Colors } from '@/styles/colors';
import { Image } from 'expo-image';
import QR from 'qrcode-base64';
import { StyleSheet } from 'react-native';

import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';

function MyInfoScreen() {
  useSetNavigationOptions({
    title: i18n2.t('MyInfoScreen.Title')
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const context: any = useContext(AppContext);

  const userProfile = context.userProfile;

  const qrCode = QR.drawImg(`!contact!${userProfile.uniqueId ?? -1}!${userProfile.displayName}`, {
    typeNumber: 4,
    errorCorrectLevel: 'M',
    size: 300
  });
  const qrCodeSize = Dimensions.get('window').width / 2;

  return (
    <View style={styles.container}>
      <Text style={styles.text}>{i18n2.t('MyInfoScreen.Account') + ' ' + userProfile.uniqueId ?? ''}</Text>
      <Image style={{ width: qrCodeSize, height: qrCodeSize }} source={{ uri: qrCode }} />
    </View>
  );
}

export default MyInfoScreen;

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  text: {
    marginTop: 40,
    color: Colors.text,
    fontSize: getCurrentUser().getMediumFontSize(),
    marginBottom: 40
  }
});
