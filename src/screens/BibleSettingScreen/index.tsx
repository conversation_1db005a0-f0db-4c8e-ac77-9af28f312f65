import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { getObjectAsync, setObjectAsync } from '@/dataStorage/localStorage';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';
import {
  ETranslation,
  getDefaultTranslation,
  downloadAndStoreBible,
  isBibleVersionDownloaded,
  checkBibleDataIntegrity,
  repairBibleData
} from '@/utils/bibleUtils';
import { i18n2 } from '@/utils/i18n2';
import Title from '@/screens/HomeScreen/components/Title';

const BibleSettingScreen: React.FC = () => {
  const [versions, setVersions] = useState<Record<ETranslation, boolean>>({
    cuvs: true,
    cuvt: false,
    asv: false
  });

  const [downloadedVersions, setDownloadedVersions] = useState<Record<ETranslation, boolean>>({
    cuvs: false,
    cuvt: false,
    asv: false
  });

  const [downloading, setDownloading] = useState<Record<ETranslation, boolean>>({
    cuvs: false,
    cuvt: false,
    asv: false
  });

  const toggleVersion = async (key: ETranslation) => {
    // Don't allow deselecting the last translation
    if (versions[key] && Object.values(versions).filter((v) => v).length <= 1) {
      return;
    }

    const isSelected = !versions[key];

    // 如果要选中版本但尚未下载，先提示下载
    if (isSelected && !downloadedVersions[key]) {
      Alert.alert(
        i18n2.t('BibleSettingScreen.DownloadRequired'),
        i18n2.tValue?.('BibleSettingScreen.DownloadRequiredMsg', getTranslationName(key)),
        [
          {
            text: i18n2.t('BibleSettingScreen.Cancel'),
            style: 'cancel'
          },
          {
            text: i18n2.t('BibleSettingScreen.Download'),
            onPress: async () => {
              // 设置下载状态
              setDownloading((prev) => ({ ...prev, [key]: true }));

              // 执行下载
              const success = await downloadAndStoreBible(key);

              // 重置下载状态
              setDownloading((prev) => ({ ...prev, [key]: false }));

              if (success) {
                // 更新下载状态
                setDownloadedVersions((prev) => ({ ...prev, [key]: true }));

                // 选中该版本
                const newVersions = { ...versions, [key]: true };
                setVersions(newVersions);

                // 保存选中的翻译版本
                const selectedTranslations = Object.entries(newVersions)
                  .filter(([, isSelected]) => isSelected)
                  .map(([translation]) => translation);
                setObjectAsync('bible.settings', { versions: selectedTranslations });

                Alert.alert(
                  i18n2.t('BibleSettingScreen.DownloadSuccess'),
                  i18n2.tValue?.('BibleSettingScreen.DownloadSuccessMsg', getTranslationName(key))
                );
              } else {
                Alert.alert(
                  i18n2.t('BibleSettingScreen.DownloadFailed'),
                  i18n2.tValue?.('BibleSettingScreen.DownloadFailedMsg', getTranslationName(key))
                );
              }
            }
          }
        ]
      );
      return;
    }

    // 如果已下载，直接切换选中状态
    const newVersions = { ...versions, [key]: isSelected };
    setVersions(newVersions);

    // Save the selected translations as an array
    const selectedTranslations = Object.entries(newVersions)
      .filter(([, isSelected]) => isSelected)
      .map(([translation]) => translation);
    setObjectAsync('bible.settings', { versions: selectedTranslations });
  };

  // 获取翻译版本的显示名称
  const getTranslationName = (translation: ETranslation): string => {
    switch (translation) {
      case ETranslation.cuvs:
        return '和合本（中文简体）';
      case ETranslation.cuvt:
        return '和合本（中文繁體）';
      case ETranslation.asv:
        return 'ASV';
      default:
        return translation || '';
    }
  };

  useEffect(() => {
    (async () => {
      // 加载选中的版本
      const settings = await getObjectAsync('bible.settings');
      if (settings) {
        // Reset all versions to false
        const newVersions = {
          cuvs: false,
          cuvt: false,
          asv: false
        };

        // Set selected versions to true
        if (Array.isArray(settings)) {
          // New format: array of selected translations
          settings.forEach((version: ETranslation) => {
            newVersions[version] = true;
          });
        } else if (settings.versions && Array.isArray(settings.versions)) {
          // Old format: {versions: [...]} object
          settings.versions.forEach((version: ETranslation) => {
            newVersions[version] = true;
          });
        }

        // Ensure at least one translation is selected
        if (Object.values(newVersions).every((v) => !v)) {
          // Use default translation based on device language
          const defaultTranslation = getDefaultTranslation();
          newVersions[defaultTranslation] = true;
        }

        setVersions(newVersions);

        // 检查选中版本的数据完整性
        for (const [translation, isSelected] of Object.entries(newVersions)) {
          if (isSelected) {
            const translationEnum = translation as ETranslation;
            // 检查是否已下载
            const isDownloaded = await isBibleVersionDownloaded(translationEnum);
            if (isDownloaded) {
              // 检查数据完整性
              const isDataComplete = await checkBibleDataIntegrity(translationEnum);
              if (!isDataComplete) {
                // 数据不完整，显示修复提示
                Alert.alert(
                  i18n2.t('BibleSettingScreen.DataIncomplete'),
                  i18n2.tValue?.('BibleSettingScreen.DataIncompleteMsg', getTranslationName(translationEnum)),
                  [
                    {
                      text: i18n2.t('BibleSettingScreen.Cancel'),
                      style: 'cancel'
                    },
                    {
                      text: i18n2.t('BibleSettingScreen.Repair'),
                      onPress: async () => {
                        // 设置下载状态
                        setDownloading((prev) => ({ ...prev, [translationEnum]: true }));

                        // 执行修复
                        const success = await repairBibleData(translationEnum);

                        // 重置下载状态
                        setDownloading((prev) => ({ ...prev, [translationEnum]: false }));

                        if (success) {
                          // 更新下载状态
                          setDownloadedVersions((prev) => ({ ...prev, [translationEnum]: true }));
                          Alert.alert(
                            i18n2.t('BibleSettingScreen.RepairSuccess'),
                            i18n2.tValue?.('BibleSettingScreen.RepairSuccessMsg', getTranslationName(translationEnum))
                          );
                        } else {
                          Alert.alert(
                            i18n2.t('BibleSettingScreen.RepairFailed'),
                            i18n2.tValue?.('BibleSettingScreen.RepairFailedMsg', getTranslationName(translationEnum))
                          );
                        }
                      }
                    }
                  ]
                );
              }
            }
          }
        }
      }

      // 加载已下载的版本状态
      const newDownloadedVersions = {
        cuvs: false,
        cuvt: false,
        asv: false
      };

      // 检查每个版本是否已下载
      for (const translation of Object.values(ETranslation)) {
        newDownloadedVersions[translation] = await isBibleVersionDownloaded(translation);
      }

      setDownloadedVersions(newDownloadedVersions);
    })();
  }, []);
  useSetNavigationOptions({
    title: i18n2.t('BibleSettingScreen.Title')
  });

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.section}>
          <Title title={i18n2.t('BibleSettingScreen.Version')} />
          <View style={styles.optionRow}>
            <Text style={styles.optionText}>和合本（中文简体）</Text>
            <View style={styles.optionControls}>
              {downloading.cuvs && (
                <View style={styles.loadingIndicator}>
                  <ActivityIndicator size='small' color='#666' />
                </View>
              )}

              {/* 选择复选框 */}
              <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleVersion(ETranslation.cuvs)}>
                {versions.cuvs ? (
                  <View style={styles.checkbox}>
                    <Ionicons name='checkmark' size={18} color='black' />
                  </View>
                ) : (
                  <View style={styles.emptyCheckbox} />
                )}
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.optionRow}>
            <Text style={styles.optionText}>和合本（中文繁體）</Text>
            <View style={styles.optionControls}>
              {/* 下载中的加载指示器 */}
              {downloading.cuvt && (
                <View style={styles.loadingIndicator}>
                  <ActivityIndicator size='small' color='#666' />
                </View>
              )}

              <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleVersion(ETranslation.cuvt)}>
                {versions.cuvt ? (
                  <View style={styles.checkbox}>
                    <Ionicons name='checkmark' size={18} color='black' />
                  </View>
                ) : (
                  <View style={styles.emptyCheckbox} />
                )}
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.optionRow}>
            <Text style={styles.optionText}>ASV</Text>
            <View style={styles.optionControls}>
              {downloading.asv && (
                <View style={styles.loadingIndicator}>
                  <ActivityIndicator size='small' color='#666' />
                </View>
              )}

              <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleVersion(ETranslation.asv)}>
                {versions.asv ? (
                  <View style={styles.checkbox}>
                    <Ionicons name='checkmark' size={18} color='black' />
                  </View>
                ) : (
                  <View style={styles.emptyCheckbox} />
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default BibleSettingScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff'
  },
  content: {
    flex: 1,
    padding: 15
  },
  section: {
    marginBottom: 20
  },
  optionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12
  },
  optionText: {
    fontSize: 16,
    flex: 1
  },
  optionControls: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  checkboxContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center'
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: '#ccc',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0'
  },
  emptyCheckbox: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: '#ccc'
  },
  loadingIndicator: {
    marginRight: 10,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center'
  }
});
