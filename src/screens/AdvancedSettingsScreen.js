import * as Sharing from 'expo-sharing';

import { Alert, View } from 'react-native';
import {
  canUseNotificationsAsync,
  getPushTokenAsync,
  registerForPushNotificationsAsync,
  unregisterForPushNotificationsAsync
} from '@/utils/notification';
import { deleteEventsAsync, getLogFile, logEvent } from '@/utils/logger';

import { AppContext } from '@/context/AppContext';
import { LoadingIndicator } from '@/components';
import React from 'react';
import SettingsList from '@/components/SettingsList';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { removeLocalFilesAsync } from '@/dataStorage/localStorage';
import { showMessage } from '@/components/MessageBox';

export default class AdvancedSettingsScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = () => ({
    title: i18n2.t('Common.Advanced')
  });

  constructor(props) {
    super(props);

    this.state = {
      disablePushNotification: getCurrentUser().getProperty('disablePushNotification'),
      canUsePushNotification: false,
      useExperimentFeatures: !!getCurrentUser().getProperty('setting.useExperimentFeatures'),
      useJitsiSDK: !!getCurrentUser().getProperty('setting.useJitsiSDK'),
      development: !!getCurrentUser().getProperty('setting.development'),
      channelMode: !!getCurrentUser().getProperty('setting.channelMode'),
      bibleMode: !!getCurrentUser().getProperty('setting.bibleMode'),
      logToFile: !!getCurrentUser().getProperty('setting.logToFile'),
      busy: false
    };
  }

  componentDidMount = async () => {
    const canUse = await canUseNotificationsAsync();
    if (canUse) {
      this.setState({ canUsePushNotification: true });
    }
  };

  switchPushNotificationAsync = async () => {
    try {
      const { disablePushNotification, canUsePushNotification } = this.state;

      if (!disablePushNotification && canUsePushNotification) {
        // switch off

        // it's possible that we no longer can get push token (user can manually disable notification), in this case we no longer need to call unregisterForPushNotificationsAsync
        const token = await getPushTokenAsync(false);
        if (token) {
          // token is valid, canUsePushNotification should be true
          this.setState({ busy: true, canUsePushNotification: true });
          const succeed = await unregisterForPushNotificationsAsync(token);
          if (!succeed) {
            return;
          }
        }

        this.setState({ disablePushNotification: true });
        await getCurrentUser().resetPropertyAsync('disablePushNotification', true);
      } else {
        // switch on
        const token = await getPushTokenAsync(true);
        if (!token) {
          return;
        }

        // token is valid, canUsePushNotification should be true
        this.setState({ busy: true, canUsePushNotification: true });
        const succeed = await registerForPushNotificationsAsync(token);
        if (!succeed) {
          return;
        }

        this.setState({ disablePushNotification: false });
        await getCurrentUser().resetPropertyAsync('disablePushNotification', false);
      }
    } catch (error) {
      showMessage({
        message: i18n2.t('Errors.Error'),
        description: JSON.stringify(error),
        type: 'danger'
      });
    } finally {
      this.setState({ busy: false });
    }
  };

  render() {
    const fontSize = getCurrentUser().getSmallFontSize();
    const { disablePushNotification, canUsePushNotification, logToFile } = this.state;
    return (
      <View
        style={{
          flex: 1,
          paddingLeft: this.context.insets.left,
          paddingRight: this.context.insets.right
        }}>
        <SettingsList underlayColor='#FFFFFF' borderColor='#DCDCDC'>
          <SettingsList.Item
            title={i18n2.t('SystemInfoScreen.Title')}
            titleStyle={{ fontSize }}
            hasNavArrow={true}
            onPress={() => {
              this.props.navigation.navigate('SystemInfo');
            }}
          />
          <SettingsList.Item
            title={i18n2.t('AdvancedSettingsScreen.PushNotification')}
            titleStyle={{ fontSize }}
            titleInfoStyle={{ fontSize }}
            hasNavArrow={false}
            switchState={!disablePushNotification && canUsePushNotification}
            switchOnValueChange={async () => {
              await this.switchPushNotificationAsync();
            }}
            hasSwitch={true}
          />
          <SettingsList.Item
            title={i18n2.t('AdvancedSettingsScreen.ResetAppData')}
            titleStyle={{ fontSize }}
            hasNavArrow={false}
            onPress={async () => {
              getCurrentUser().deleteAllSettingsAsync();
              removeLocalFilesAsync();
              this.context.checkUserUpdateAsync();
              Alert.alert(i18n2.t('Common.Information'), i18n2.t('Common.Ok'));
            }}
          />
          <SettingsList.Item
            title={i18n2.t('App.SetupProxy')}
            titleStyle={{ fontSize }}
            hasNavArrow={false}
            onPress={() => {
              this.props.navigation.navigate('SetupProxy');
            }}
          />
          {this.experimentItem()}
          <SettingsList.Item
            title={i18n2.t('AdvancedSettingsScreen.LogToFile')}
            titleStyle={{ fontSize }}
            hasNavArrow={false}
            switchState={logToFile}
            switchOnValueChange={async () => {
              const newValue = !logToFile;
              if (newValue) {
                await getCurrentUser().setPropertyAsync('setting.logToFile', true);
              } else {
                await getCurrentUser().deletePropertyAsync('setting.logToFile');
                await deleteEventsAsync();
              }
              this.setState({ logToFile: newValue });
              logEvent('Logs enabled');
            }}
            hasSwitch={true}
          />
          {logToFile && (
            <SettingsList.Item
              title={i18n2.t('AdvancedSettingsScreen.OpenLogFile')}
              titleStyle={{ fontSize }}
              hasNavArrow={false}
              onPress={() => {
                const url = getLogFile();
                Sharing.shareAsync(url, { UTI: '.txt', mimeType: 'text/plain' });
              }}
            />
          )}
        </SettingsList>
        {/* {Platform.OS === 'ios' ? (
          <AppleAuthentication.AppleAuthenticationButton
            buttonType={AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN}
            buttonStyle={AppleAuthentication.AppleAuthenticationButtonStyle.BLACK}
            onPress={async () => {
              this.signInWithAppleAsync();
            }}
          />
        ) : null} */}
        {this.state.busy ? <LoadingIndicator /> : null}
      </View>
    );
  }

  experimentItem() {
    const fontSize = getCurrentUser().getSmallFontSize();
    const { useExperimentFeatures, useJitsiSDK, development, channelMode, bibleMode } = this.state;
    return isFishMeet
      ? null
      : [
          <SettingsList.Item
            key='betaFeature'
            title={i18n2.t('AdvancedSettingsScreen.Beta')}
            titleStyle={{ fontSize }}
            hasNavArrow={false}
            switchState={useExperimentFeatures}
            switchOnValueChange={async () => {
              const newValue = !useExperimentFeatures;
              if (newValue) {
                await getCurrentUser().setPropertyAsync('setting.useExperimentFeatures', true);
              } else {
                await getCurrentUser().deletePropertyAsync('setting.useExperimentFeatures');
                // When disabling experiment features, also disable channelMode and bibleMode
                await getCurrentUser().deletePropertyAsync('setting.channelMode');
                await getCurrentUser().deletePropertyAsync('setting.bibleMode');
                // Emit events for immediate effect
                globalThis.eventEmitter.emit('channelModeChanged', false);
                globalThis.eventEmitter.emit('bibleModeChanged', false);
              }
              this.setState({
                useExperimentFeatures: newValue,
                // Reset channelMode and bibleMode when disabling experiment features
                ...(newValue ? {} : { channelMode: false, bibleMode: false })
              });
            }}
            hasSwitch={true}
          />,
          useExperimentFeatures && (
            <SettingsList.Item
              key='useJitsiSDK'
              title={i18n2.t('AdvancedSettingsScreen.UseJitsiSDK')}
              titleStyle={{ fontSize }}
              hasNavArrow={false}
              switchState={useJitsiSDK}
              switchOnValueChange={async () => {
                const newValue = !useJitsiSDK;
                if (newValue) {
                  await getCurrentUser().setPropertyAsync('setting.useJitsiSDK', true);
                } else {
                  await getCurrentUser().deletePropertyAsync('setting.useJitsiSDK');
                }
                this.setState({ useJitsiSDK: newValue });
              }}
              hasSwitch={true}
            />
          ),
          useExperimentFeatures && (
            <SettingsList.Item
              key='development'
              title={i18n2.t('AdvancedSettingsScreen.Development')}
              titleStyle={{ fontSize }}
              hasNavArrow={false}
              switchState={development}
              switchOnValueChange={async () => {
                const newValue = !development;
                if (newValue) {
                  await getCurrentUser().resetPropertyAsync('setting.development', true);
                } else {
                  await getCurrentUser().deletePropertyAsync('setting.development');
                }
                this.setState({ development: newValue });
              }}
              hasSwitch={true}
            />
          ),
          useExperimentFeatures && (
            <SettingsList.Item
              key='channelMode'
              title={i18n2.t('AdvancedSettingsScreen.ExperimentalChannelFeature')}
              titleStyle={{ fontSize }}
              hasNavArrow={false}
              switchState={channelMode}
              switchOnValueChange={async () => {
                const newValue = !channelMode;
                if (newValue) {
                  await getCurrentUser().resetPropertyAsync('setting.channelMode', true);
                } else {
                  await getCurrentUser().deletePropertyAsync('setting.channelMode');
                }
                this.setState({ channelMode: newValue });
                // Emit event for immediate effect
                globalThis.eventEmitter.emit('channelModeChanged', newValue);
              }}
              hasSwitch={true}
            />
          ),
          useExperimentFeatures && (
            <SettingsList.Item
              key='bibleMode'
              title={i18n2.t('AdvancedSettingsScreen.ExperimentalBibleFeature')}
              titleStyle={{ fontSize }}
              hasNavArrow={false}
              switchState={bibleMode}
              switchOnValueChange={async () => {
                const newValue = !bibleMode;
                if (newValue) {
                  await getCurrentUser().resetPropertyAsync('setting.bibleMode', true);
                } else {
                  await getCurrentUser().deletePropertyAsync('setting.bibleMode');
                }
                this.setState({ bibleMode: newValue });
                // Emit event for immediate effect
                globalThis.eventEmitter.emit('bibleModeChanged', newValue);
              }}
              hasSwitch={true}
            />
          )
        ];
  }
}
