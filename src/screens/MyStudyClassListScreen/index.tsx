/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useContext } from 'react';
import { View, ScrollView } from 'react-native';
import { AppContext } from '@/context/AppContext';
import { getCurrentUser } from '@/utils/user';
import { cloneDeep } from 'lodash-es';
import { useNavigation } from '@react-navigation/native';
import { goToLesson } from '@/utils/lesson/GoToLesson';
import { ClassCover } from '@/components/ClassCover';
import { useColumn } from '@/utils/lesson/Column';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';
import { i18n2 } from '@/utils/i18n2';

const MyStudyClassListScreen = () => {
  const { itemMargin, itemWidth } = useColumn();
  const context = useContext(AppContext) as any;
  const navigation = useNavigation<any>();
  const classes = cloneDeep(context.classes) as any[];
  const classAccessTime = getCurrentUser().getProperty('classOrder') || {};

  const myClasses = classes
    .filter((it) => it.isMyClass)
    .sort((a, b) => (classAccessTime[b.name] || 0) - (classAccessTime[a.name] || 0));
  useSetNavigationOptions({
    title: i18n2.t('HomeScreen.MyStudyTitle')
  });
  return (
    <ScrollView>
      <View style={{ flexDirection: 'row', flexWrap: 'wrap', paddingVertical: itemMargin }}>
        {myClasses.map((item) => (
          <ClassCover
            key={`full_${item.classId}`}
            size={itemWidth}
            margin={itemMargin}
            classId={item.classId}
            text={item.classNameChs}
            unreadCount={item.newMessageCount}
            onPress={() => goToLesson(item, navigation.navigate)}
          />
        ))}
      </View>
    </ScrollView>
  );
};

export default MyStudyClassListScreen;
