import { Al<PERSON>, Dimensions, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { AvatarIcon, ListItem, LoadingIndicator, NavBarTitle, getImage } from '@/components';

import Accordion from 'react-native-collapsible/Accordion';
import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { logEvent } from '@/utils/logger';

export default class GroupProgress extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ route }) => ({
    headerTitle: () => <NavBarTitle title={route.params?.groupName ?? ''} subTitle={i18n2.t('Common.StudyProgress')} />
  });

  constructor(props) {
    super(props);

    const params = this.props.route.params;
    this.groupId = params.groupId;
    this.groupName = params.groupName;
    this.classId = params.classId;
    this.classTitle = params.classTitle;

    const progress = getCurrentUser().getProperty(`GroupProgressActiveSections.${this.groupId}.${this.classId}`);
    this.state = {
      activeSections: Array.isArray(progress) ? progress : [0],
      data: null
    };
  }

  componentDidMount() {
    logEvent('showGroupProgress');
    this.loadAsync();
  }

  async loadAsync() {
    const result = await globalThis.dsObject.getClassStudyProgress(this.groupId, this.classId);
    if (!result) {
      this.props.navigation.pop();
      return;
    }

    this.setState({ data: result.body });
  }

  renderHeader = (content) => {
    return (
      <View style={{ flex: 1, marginVertical: 5 }}>
        <ListItem textLeft={content.title} textRight={content.progress} />
      </View>
    );
  };

  renderContent = (content) => {
    const avatarSize = 70;
    return (
      <View
        style={{
          backgroundColor: Colors.lightBlue,
          width: Dimensions.get('window').width - (Dimensions.get('window').width % avatarSize),
          marginLeft: (Dimensions.get('window').width % avatarSize) / 2,
          borderRadius: 7
        }}>
        <View
          style={{
            flexDirection: 'row',
            flexWrap: 'wrap'
          }}>
          {content.users.map((user) => (
            <View
              key={user.loginId}
              style={{ width: avatarSize, marginVertical: 7, alignItems: 'center', justifyContent: 'center' }}>
              <AvatarIcon
                size={avatarSize * 0.8}
                borderColor='#515151'
                backgroundColor={'#EDEDED'}
                textColor={'#494949'}
                fontSize={getCurrentUser().getXSmallFontSize()}
                name={(user.name || '').trim() || user.loginId}
              />
              <Text style={{ fontSize: 14, fontWeight: 'bold' }}>{user.progress}</Text>
            </View>
          ))}
        </View>

        {content.progress !== '-' ? (
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
              const userId = getCurrentUser().getUserId();
              let myProgress = '';
              content.users.forEach((item) => {
                if (item.userId === userId) {
                  myProgress = item.progress;
                }
              });
              Alert.alert(
                i18n2.t('StudyReminder'),
                i18n2.t('StudyGroupMsg') +
                  '\n\n' +
                  i18n2
                    .t('GroupProgress')
                    .replace('{ClassTitle}', this.classTitle)
                    .replace('{WeekTitle}', content.title)
                    .replace('{TotalProgress}', content.progress) +
                  ' ' +
                  this.getTotalProgressText(content.progress) +
                  '\n\n' +
                  i18n2.t('PersonalMsg') +
                  '\n\n' +
                  i18n2.t('YourCurrentProgress').replace('{PersonalProgress}', myProgress) +
                  ' ' +
                  this.getPersonalProgressText(myProgress, content.progress),
                [
                  {
                    text: i18n2.t('Send'),
                    onPress: () => {
                      this.sendReminderAsync(content.title, content.progress, content.users);
                    }
                  },
                  {
                    text: i18n2.t('Common.Cancel')
                  }
                ]
              );
            }}>
            <View style={{ marginTop: 7, alignItems: 'center' }}>
              {getImage('setReminder')}
              <Text
                style={{
                  marginBottom: 7,
                  color: Colors.darkBlue,
                  fontSize: getCurrentUser().getSmallFontSize(),
                  textAlign: 'center'
                }}>
                {i18n2.t('StudyReminder')}
              </Text>
            </View>
          </TouchableOpacity>
        ) : null}
      </View>
    );
  };

  getTotalProgressText = (progress) => {
    const n = parseInt(progress);
    if (n <= 30) {
      return i18n2.t('EveryoneCatchup');
    }
    if (n <= 70) {
      return i18n2.t('KeepUp');
    }

    return i18n2.t('GoodJobEveryone');
  };

  getPersonalProgressText = (personalProgress, totalProgress) => {
    const personal = parseInt(personalProgress);
    const total = parseInt(totalProgress);
    if (personal === 0) {
      return i18n2.t('NeedHelp');
    }
    if (personal <= 20) {
      return i18n2.t('Catchup');
    }
    if (personal <= 70) {
      if (personal >= total) {
        return i18n2.t('KeepUp');
      }

      return i18n2.t('Catchup');
    }

    return i18n2.t('GoodJob');
  };

  sendReminderAsync = async (weekTitle, totalProgress, users) => {
    const perUserMessages = [];
    users.forEach((item) => {
      perUserMessages.push([
        item.userId,
        'StudyReminder',
        this.groupName +
          '\n\n' +
          i18n2.t('HiName!').replace('{Name}', (item.name || '').trim() || item.loginId) +
          ' ' +
          i18n2
            .t('GroupProgress')
            .replace('{ClassTitle}', this.classTitle)
            .replace('{WeekTitle}', weekTitle)
            .replace('{TotalProgress}', totalProgress) +
          '\n\n' +
          i18n2.t('YourCurrentProgress').replace('{PersonalProgress}', item.progress) +
          ' ' +
          this.getPersonalProgressText(item.progress, totalProgress)
      ]);
    });

    const result = await globalThis.dsObject.sendMessage({
      room: this.groupId,
      message:
        i18n2
          .t('GroupProgress')
          .replace('{ClassTitle}', this.classTitle)
          .replace('{WeekTitle}', weekTitle)
          .replace('{TotalProgress}', totalProgress) +
        ' ' +
        this.getTotalProgressText(totalProgress),
      perUserMessages
    });

    if (result) {
      Alert.alert(i18n2.t('Common.Succeeded'));
    }
  };

  render() {
    if (!this.state.data) {
      return <LoadingIndicator />;
    }

    const fontSize = getCurrentUser().getMediumFontSize();
    return (
      <ScrollView
        style={{ flex: 1 }}
        scrollIndicatorInsets={{ right: 1 }}
        contentContainerStyle={{ paddingBottom: 40 }}>
        <View style={{ alignItems: 'center' }}>
          <Text style={{ fontSize, margin: 7 }}>{this.classTitle}</Text>
        </View>
        <Accordion
          activeSections={this.state.activeSections}
          underlayColor='white'
          sections={this.state.data}
          renderHeader={this.renderHeader}
          renderContent={this.renderContent}
          onChange={(activeSections) => {
            this.setState({ activeSections });
            getCurrentUser().resetPropertyAsync(
              `GroupProgressActiveSections.${this.groupId}.${this.classId}`,
              activeSections
            );
          }}
        />
      </ScrollView>
    );
  }
}
