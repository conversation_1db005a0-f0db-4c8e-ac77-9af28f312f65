import * as Clipboard from 'expo-clipboard';
import * as Linking from 'expo-linking';
import * as ScreenOrientation from 'expo-screen-orientation';

import { Alert, Share, View } from 'react-native';
import { ModalPopup, getImage } from '@/components';

import { AppContext } from '@/context/AppContext';
import { NavBarButton } from '@/components/navigation/NavigationButtons';
import React from 'react';
import { WebView } from 'react-native-webview';
import { i18n2 } from '@/utils/i18n2';

export default class WebAppScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ route }) => ({
    title: route.params.title ? i18n2.t(route.params.title) : '...',
    headerRight: () =>
      route.params.enableShare ? (
        <NavBarButton
          image='more'
          right={true}
          onPress={() => {
            globalThis.eventEmitter.emit('exportUrl');
          }}
        />
      ) : null
  });

  constructor(props) {
    super(props);

    const params = this.props.route.params;
    this.url = params.url;
    this.title = params.title || '';
    this.state = {
      loading: true,
      showModal: false,
      key: 1
    };
    this.listeners = [];

    console.log(this.url);
  }

  componentDidMount() {
    ScreenOrientation.unlockAsync();

    this.listeners.push(
      globalThis.eventEmitter.addListener('exportUrl', () => {
        this.setState({ showModal: true });
      })
    );
  }

  componentWillUnmount() {
    ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);

    this.listeners.forEach((listener) => {
      listener.remove();
    });
  }

  refreshWebView = () => {
    this.setState({ key: this.state.key + 1 });
  };

  selectGroup = (onSelected) => {
    const groups = this.context.groups.filter((studyGroup) => studyGroup.status);
    this.props.navigation.navigate('Select', {
      choices: groups,
      titleControl: getImage('groupSelect'),
      title: i18n2.t('SelectGroup'),
      getDisplayName: (choice) => choice.name,
      classId: this.classId,
      isCurrent: (choice) => false,
      onSelect: (group) => {
        setTimeout(() => {
          onSelected(group);
        }, 0);
      }
    });
  };

  shareUrlAsync = async (group, isAnonymous) => {
    if (!group) {
      Alert.alert(i18n2.t('Common.Failed'), i18n2.t('InvalidGroup'));
      return;
    }

    const result = await globalThis.dsObject.sendMessage({
      room: group.groupId,
      message: this.url,
      isAnonymous
    });
    if (result) {
      setTimeout(() => {
        this.props.navigation.navigate('Chat', {
          chatId: group.groupId,
          title: group.name,
          group: group,
          classId: this.classId,
          showGroupIcon: true,
          isAnonymous,
          enableAudioChat: true
        });
      }, 1000);
    } else {
      Alert.alert(i18n2.t('Common.Failed'), i18n2.t('Errors.MsgSendFailed'));
    }
  };

  renderBottomSheet = () => {
    const items = [];

    items.push({
      renderIcon: () => getImage('groupWithName'),
      text: i18n2.t('ShareToGroup'),
      onPress: () => {
        this.selectGroup((group) => {
          this.shareUrlAsync(group, false);
        });
      }
    });

    items.push({
      renderIcon: () => getImage('export'),
      text: i18n2.t('ShareWeb'),
      onPress: () => {
        setTimeout(() => {
          Share.share({ url: this.url });
        }, 600);
      }
    });

    items.push({
      renderIcon: () => getImage('copy'),
      text: i18n2.t('Copy'),
      onPress: () => {
        Clipboard.setStringAsync(this.url);
      }
    });

    items.push({
      renderIcon: () => getImage('webLink'),
      text: i18n2.t('OpenWithBrowser'),
      onPress: () => {
        Linking.openURL(this.url);
      }
    });

    return (
      <ModalPopup
        insets={this.context.insets}
        visible={this.state.showModal}
        showDivider={true}
        setNotVisible={() => this.setState({ showModal: false })}
        items={items}
        iconsPerRow={items.length}
        showCancel={true}
        hideOnPress={true}
      />
    );
  };

  render() {
    const { insets } = this.context;
    return (
      <View style={{ flex: 1, marginBottom: insets.bottom, marginLeft: insets.left, marginRight: insets.right }}>
        <WebView
          allowsLinkPreview={true}
          style={{ flex: 1 }}
          key={this.state.key}
          ref={(ref) => (this.browser = ref)}
          source={{
            uri: this.url
          }}
          allowFileAccessFromFileURLs={false}
          allowFileAccess={false}
          onLoadEnd={(event) => {
            this.setState({ loading: false });
            this.title = event.nativeEvent.title;
            this.props.navigation.setParams({ title: this.title });
          }}
          onLoadStart={(event) => {
            this.setState({ loading: true });
            this.title = event.nativeEvent.title;
          }}
          onLoadProgress={(evt) => {
            const progress = evt.nativeEvent.progress;
            this.props.navigation.setParams({
              title: (progress === 1 ? '' : `${parseInt(progress * 100)}% `) + this.title
            });
          }}
          onShouldStartLoadWithRequest={(event) => {
            if (event.url.toLowerCase().endsWith('.apk')) {
              // disallow download apk
              alert(i18n2.t('OpsNotAllowed'));
              return false;
            }

            return true;
          }}
        />
        {this.renderBottomSheet()}
      </View>
    );
  }
}
