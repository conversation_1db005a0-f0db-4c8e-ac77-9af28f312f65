import { LoadingIndicator, NavBarTitle } from '@/components';

import { AppContext } from '@/context/AppContext';
import Chat from '@/utils/chat';
import ChatUI from '@/components/ChatUI';
import { NavBarButton } from '@/components/navigation/NavigationButtons';
import React from 'react';
import { checkRegisterForPushNotificationsAsync } from '@/utils/notification';
import { getCurrentUser } from '@/utils/user';
import { getDisappearingMessagesText } from '@/utils/disappearingMessages';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';

export default class ChatScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ route, navigation }) => {
    const { title, subTitle, group, showGroupIcon, isOrgGroupChat, classId, hideMessages, disappearingMessages } =
      route.params;

    let subTitleToShow = '';
    const showDisappearMessage = typeof hideMessages === 'number' && hideMessages > 0;
    const showDeleteMessage = typeof disappearingMessages === 'number' && disappearingMessages > 0;
    let subTitleSize = 14;
    if (showDisappearMessage && showDeleteMessage) {
      subTitleToShow = i18n2
        .t('MsgAutoDisappearDelete')
        .replace('{0}', getDisappearingMessagesText(hideMessages))
        .replace('{1}', getDisappearingMessagesText(disappearingMessages));
      if (getCurrentUser().getLanguage() === 'eng') {
        // English cannot show all, so use a smaller font
        subTitleSize = 10;
      }
    } else if (showDisappearMessage) {
      subTitleToShow = i18n2.t('MsgAutoDisappear').replace('{0}', getDisappearingMessagesText(hideMessages));
    } else if (showDeleteMessage) {
      subTitleToShow = i18n2.t('MsgAutoDelete').replace('{0}', getDisappearingMessagesText(disappearingMessages));
    }

    const displayTitle = isFishMeet ? i18n2.t('Group') : `${title}` || i18n2.t('Chat');
    return {
      headerTitle: () => (
        <NavBarTitle
          title={displayTitle}
          boldTitle={true}
          titleSize={16}
          subTitle={subTitleToShow ?? subTitle}
          boldSubTitle={false}
          subTitleSize={subTitleSize}
          subTitleColor={subTitleToShow ? 'red' : 'black'}
        />
      ),

      headerRight: () =>
        !isFishMeet && showGroupIcon ? (
          <NavBarButton
            right={true}
            image='group'
            onPress={() => {
              navigation.navigate('Group', {
                title,
                group,
                classId,
                isOrgGroup: isOrgGroupChat
              });
            }}
          />
        ) : null
    };
  };

  constructor(props) {
    super(props);

    const params = props.route.params;
    this.chatId = `${params.chatId}`;
    this.onExit = params.onExit;
    this.listeners = [];

    // allow audio chat (jitsi)
    this.enableAudioChat = !!params.enableAudioChat;

    // set this user id from token
    const token = getCurrentUser().getJwtObj();
    this.userId = token.userId;
    this.group = params.group;
    this.currentTag = params.currentTag;
    this.disableTagging = params.disableTagging;
    this.disableAtPeople = params.disableAtPeople;
    this.disableUserInfo = params.disableUserInfo;
    this.showGroups = params.showGroups;
    this.isAnonymous = params.isAnonymous;
    this.title = params.title;
    this.useNewFav = !this.group.isMomentDiscussionGroup && !this.group.isClassDiscussionGroup;
    this.shareIntentData = params.shareIntentData;
    if (this.showGroups && this.showGroups.length > 0) {
      const shareInGroups = JSON.stringify([this.currentTag || 0]);
      this.chatServer = new Chat({
        id: this.chatId,
        shareInGroups,
        useNewFav: this.useNewFav
      });
    } else {
      this.chatServer = new Chat({ id: this.chatId, useNewFav: this.useNewFav });
    }
    this.theAllUser = {
      userId: -2,
      name: i18n2.t('ChatScreen.AllUsers'),
      isAllUser: true,
      users: []
    };
    this.state = {
      // wait get leader info
      loaded: !(this.group.disableMemberShowList && !this.group.isGroupLeader),
      leaderIds: []
    };
  }

  async componentDidMount() {
    this.context.setAudioPlayerMinimized(true);

    // fix android redmini bug,this need lock shareIntent logic;
    globalThis.eventEmitter.emit('lockProcessedShareIntent', true);
    // register push notification - show dialog if not enabled (once per 7 days unless explicitly disabled)
    checkRegisterForPushNotificationsAsync(true);
    setTimeout(() => {
      globalThis.eventEmitter.emit('lockProcessedShareIntent', false);
    }, 2000);
    this.listeners.push(
      globalThis.eventEmitter.addListener('currentGroupUpdated', (data) => {
        const params = {};
        if (data.title !== undefined) {
          params.title = data.title;
        }
        if (data.hideMessages !== undefined) {
          params.hideMessages = data.hideMessages;
        }
        if (data.disappearingMessages !== undefined) {
          params.disappearingMessages = data.disappearingMessages;
        }

        const { title, hideMessages, disappearingMessages } = this.props.route.params;
        if (
          params.title !== title ||
          params.hideMessages !== hideMessages ||
          params.disappearingMessages !== disappearingMessages
        ) {
          this.props.navigation.setParams(params);
          // reload messages since group is updated
          this.chatView.reloadMessages();
        }
      })
    );

    this.listeners.push(
      globalThis.eventEmitter.addListener('appInForeground', async () => {
        // after app in foreground again, check if group info (name and disappearingMessages) is updated
        await this.updateChatInfoAsync();
      })
    );

    this.updateChatInfoAsync();
  }

  updateChatInfoAsync = async () => {
    // only do it for normal group + one-on-one group + org group
    if (this.group.isMomentDiscussionGroup || this.group.isClassDiscussionGroup) {
      this.setState({
        loaded: true
      });
      return;
    }
    try {
      const result = await globalThis.dsObject.getGroup(this.chatId);
      if (result) {
        const otherUser = result.body.users.find((user) => user.userId !== this.userId);
        const otherUserId = otherUser ? otherUser.userId : undefined;
        let name = result.body.name;
        const {
          disappearingMessages,
          hideMessages,
          notice,
          onlyLeaderCanPost,
          disableMemberShowList,
          users = []
        } = result.body;
        if (this.group.isOneOnOneGroup) {
          const result = await globalThis.dsObject.fetchUserTagAndBlock(otherUserId);
          const userTag = result.tag;
          name = `${this.title}${userTag && userTag.trim() ? ` (${userTag.trim()})` : ''}`;
        } else if (!this.state.loaded) {
          this.setState({
            leaderIds: users.filter((item) => !!item.isGroupLeader).map((item) => item.userId)
          });
        }

        this.props.navigation.setParams({
          group: {
            ...this.group,
            onlyLeaderCanPost: onlyLeaderCanPost ? 1 : 0,
            disableMemberShowList: disableMemberShowList ? 1 : 0
          }
        });

        this.onChatInfoUpdated({
          // One-on-one group has special title, we will not update
          name,
          hideMessages,
          disappearingMessages
        });

        globalThis.eventEmitter.emit('currentGroupUpdated', {
          groupId: this.group.groupId,
          title: name,
          notice,
          hideMessages,
          disappearingMessages
        });
      }
    } finally {
      // hide loading
      !this.state.loaded &&
        this.setState({
          loaded: true
        });
    }
  };

  componentWillUnmount() {
    this.chatServer.closeChat();

    this.listeners.forEach((listener) => {
      listener.remove();
    });

    if (this.onExit) {
      this.onExit();
    }
  }

  onChatInfoUpdated = (info) => {
    const { title, hideMessages, disappearingMessages } = this.props.route.params;
    if (
      title === info.name &&
      disappearingMessages === info.disappearingMessages &&
      hideMessages == info.hideMessages
    ) {
      // nothing to update
      console.log('Group info no update');
      return;
    }

    this.props.navigation.setParams({
      title: info.name,
      hideMessages: info.hideMessages,
      disappearingMessages: info.disappearingMessages
    });
  };

  render() {
    const { loaded, leaderIds } = this.state;
    return (
      <>
        <ChatUI
          ref={(view) => {
            this.chatView = view;
          }}
          navigation={this.props.navigation}
          chatServer={this.chatServer}
          chatId={this.chatId}
          group={this.group}
          leaderIds={leaderIds}
          userId={this.userId}
          isAnonymous={this.isAnonymous}
          enableTagging={!this.disableTagging}
          enableTaggingLink={false}
          enableUserInfo={!this.disableUserInfo}
          showInput={true}
          enableAudioChat={this.enableAudioChat}
          currentTag={this.currentTag}
          onPeopleAt={(succeed, cancel) => {
            if (!this.disableAtPeople) {
              this.props.navigation.navigate('GroupMemberSelect', {
                skipUserIds: [this.userId],
                AddUsers: [this.theAllUser],
                group: this.group,
                onSelect: (user) => {
                  succeed(user);
                },
                onCancel: () => {
                  cancel();
                }
              });
            }
          }}
          showGroups={this.showGroups}
          onChatInfoUpdated={this.onChatInfoUpdated}
          shareIntentData={this.shareIntentData}
        />
        {!loaded && <LoadingIndicator backgroundColor='#ffffff' />}
      </>
    );
  }
}
