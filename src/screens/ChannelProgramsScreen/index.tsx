/* eslint-disable @typescript-eslint/no-explicit-any */
import { useRoute } from '@react-navigation/native';
import React from 'react';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';
import ProgramList from '@/screens/HomeScreen/components/ProgramList';
import { View } from 'react-native';
import { useColumn } from '@/utils/lesson/Column';

const ChannelProgramScreen = () => {
  const { params } = useRoute();
  const { mode, title } = params as any;

  const { itemMargin } = useColumn();

  useSetNavigationOptions({
    title
  });
  return <ProgramList ListHeaderComponent={<View style={{ height: itemMargin }} />} mode={mode} />;
};

export default ChannelProgramScreen;
