import { Text, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { Button } from '@/components/Button';
import { Colors } from '@/styles/colors';
import { OkButton } from '@/components/navigation/NavigationButtons';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';

export default class AccountDoneScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = () => ({
    title: i18n2.t('NewAcct'),
    headerLeft: () => (
      <OkButton
        onPress={() => {
          getCurrentUser().loginAsync();
        }}
      />
    )
  });

  render() {
    return (
      <View style={{ flex: 1, backgroundColor: 'white', alignItems: 'center' }}>
        <Text
          style={{
            marginVertical: 10,
            marginHorizontal: 20,
            color: Colors.darkBlue,
            fontSize: getCurrentUser().getMediumFontSize()
          }}>
          {i18n2.t('NewAcctCreationSuccess')}
        </Text>
        <Button
          title={'Ok'}
          onPress={() => {
            this.context.checkUserUpdateAsync();
            getCurrentUser().loginAsync();
          }}
        />
      </View>
    );
  }
}
