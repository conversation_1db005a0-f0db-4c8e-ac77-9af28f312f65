import * as Device from 'expo-device';

import { LoadingIndicator, NavBarTitle } from '@/components';
import { Platform, ScrollView, Text } from 'react-native';
import { downloadAsync, getFileInfoAsync, getStringAsync } from '@/dataStorage/localStorage';

import { AppContext } from '@/context/AppContext';
import { Config } from '@/dataStorage/config';
import { NavBarButton } from '@/components/navigation/NavigationButtons';
import React from 'react';
import { WebView } from 'react-native-webview';
import { cloneDeep } from 'lodash-es';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { logEvent } from '@/utils/logger';

const globalBibleCache = {
  version: null,
  bible: null
};

export default class BibleScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ route, navigation }) => ({
    headerTitle: () => (
      <NavBarTitle
        title={i18n2.t('BibleRef')}
        subTitle={i18n2.t(route.params?.book ?? '') + (route.params?.verse ?? '')}
      />
    ),

    headerRight: () => (
      <NavBarButton
        right={true}
        image='bible'
        onPress={() => {
          navigation.navigate('BibleSelect');
        }}
      />
    )
  });

  state = {
    passage: null,
    scrollTop: 0,
    busy: false
  };

  constructor(props) {
    super(props);

    this.book = this.props.route.params.book;
    this.verse = this.props.route.params.verse;
    this.bookIdList = [];
    const bibleData = require('@/assets/json/bible.json');
    for (let index = 0; index < bibleData.length; index++) {
      const bookData = bibleData[index].split('|');
      for (let i = 0; i < bookData.length - 1; i++) {
        this.bookIdList[bookData[i]] = index + 1;
      }
    }

    this.props.navigation.addListener('focus', async () => {
      try {
        logEvent('showBible');

        this.setState({ busy: true });

        const bibleVersion1 = getCurrentUser().getBibleVersion();
        const bibleVersion2 = getCurrentUser().getBibleVersion2();
        console.log('willFocus: ' + bibleVersion1 + ' ' + bibleVersion2);

        await this.ensureBibleIsDownloadedAsync(bibleVersion1);
        await this.ensureBibleIsDownloadedAsync(bibleVersion2);

        await this.loadAsync();
      } finally {
        this.setState({ busy: false });
      }
    });
  }

  getId(book, verse) {
    // Parse the book name to id
    return this.bookIdList[book] + '/' + verse;
  }

  async loadAsync() {
    if (this.isWebVersion()) {
      return;
    }

    // TODO: Load bible verse
    const passageId = this.getId(this.book, this.verse);
    console.log(`loadPassage: ${passageId}`);
    const passage = await this.getPassageAsync(getCurrentUser().getBibleVersion(), passageId);

    let parsedPassage = cloneDeep(passage);
    const version = getCurrentUser().getBibleVersion2();
    if (version) {
      const verses = [];
      const passage2 = await this.getPassageAsync(getCurrentUser().getBibleVersion2(), passageId);
      if (passage2) {
        // merge
        const length = passage.length > passage2.length ? passage.length : passage2.length;
        for (let i = 0; i < length; i++) {
          if (passage[i]) {
            verses.push(passage[i]);
          }
          if (passage2[i]) {
            verses.push(passage2[i]);
          }
        }
        parsedPassage = verses;
      }
    }

    // Get position
    const pos = getCurrentUser().getProperty('BibleScreen');
    let scrollTop = 0;
    if (pos && pos.book === this.book && pos.verse === this.verse && typeof pos.scrollTop === 'number') {
      scrollTop = pos.scrollTop;
    }

    this.setState({ passage: parsedPassage, scrollTop });
  }

  async loadBibleAsync(version) {
    if (globalBibleCache.version === version) {
      console.log('Load bible from global_bible_cache');
      return globalBibleCache.bible;
    }

    const info = await getFileInfoAsync(`book-${version}`);
    if (!info || !info.exists) {
      return null;
    }

    const content = await getStringAsync(`book-${version}`);
    if (!content) {
      return {};
    }

    const bible = JSON.parse(content);
    console.log(`Load bible ${version} ${content.length}`);
    globalBibleCache.version = version;
    globalBibleCache.bible = bible;
    return bible;
  }

  async getPassageAsync(version, passage) {
    const result = [];

    try {
      // parse book "<book>/..."
      const index = passage.indexOf('/');
      if (index === -1) {
        alert('wrong passage format');
        return result;
      }

      const bible = await this.loadBibleAsync(version);
      if (!bible) {
        alert('no bible content');
        return result;
      }

      const book = parseInt(passage.substring(0, index));
      let chapterFrom = 0;
      let verseFrom = 0;
      let chapterTo = 0;
      let verseTo = 0;
      const items = passage.substring(index + 1).split(/(:|-)/g);
      if (items.length === 1) {
        // parse chapter: 1
        chapterFrom = parseInt(items[0]);
        verseFrom = 1;
        chapterTo = chapterFrom;
        verseTo = 999;
      } else if (items.length === 3 && items[1] === '-') {
        // parse chapter: 1-2
        chapterFrom = parseInt(items[0]);
        verseFrom = 1;
        chapterTo = parseInt(items[2]);
        verseTo = 999;
      } else if (items.length === 3 && items[1] === ':') {
        // parse chapter: 1:33
        chapterFrom = parseInt(items[0]);
        verseFrom = parseInt(items[2]);
        chapterTo = chapterFrom;
        verseTo = verseFrom;
      } else if (items.length === 5 && items[1] === ':' && items[3] === '-') {
        // parse chapter: 1:1-3
        chapterFrom = parseInt(items[0]);
        verseFrom = parseInt(items[2]);
        chapterTo = chapterFrom;
        verseTo = parseInt(items[4]);
      } else if (items.length === 7 && items[1] === ':' && items[3] === '-' && items[5] === ':') {
        // parse chapter: 1:1-2:10
        chapterFrom = parseInt(items[0]);
        verseFrom = parseInt(items[2]);
        chapterTo = parseInt(items[4]);
        verseTo = parseInt(items[6]);
      } else {
        alert('Error format: ' + passage);
        return result;
      }

      console.log(`getPassageAsync(${version}, ${passage}) => ${chapterFrom}:${verseFrom}-${chapterTo}:${verseTo}`);
      let chapter = chapterFrom;
      let verse = verseFrom;
      while (chapter * 1000 + verse <= chapterTo * 1000 + verseTo) {
        const id = book * 1000000 + chapter * 1000 + verse;
        const text = bible[id] ? bible[id] : '';
        // Chinese bible has some empty verse
        if (!bible[id] && !bible[id + 1]) {
          chapter++;
          verse = 1;
        } else {
          result.push({
            verse: `${chapter}:${verse}`,
            text: this.getVerseText(text)
          });
          verse++;
        }
      }
    } catch (e) {
      alert(e);
      console.log(e);
    }

    return result;
  }

  getVerseText(verseText) {
    const AnnotationWords = ['the', 'in', 'of', 'on', 'and', 'an', 'to', 'a', 'as', 'for'];
    // Check to see if the first line is part of the bible
    const firstLinePos = verseText.indexOf('\n');
    if (firstLinePos !== -1) {
      const firstLine = verseText.substring(0, firstLinePos);
      let annotation = true;
      if (verseText.length > firstLinePos) {
        // We have more than one lines
        let words = firstLine.split(' ');
        // It has to be more than one words
        if (words.length > 1) {
          // Check each word starts with upper case
          for (let w in words) {
            if (AnnotationWords.indexOf(words[w]) === -1 && words[w][0] !== words[w][0].toUpperCase()) {
              // Not upper case, not an annotation
              annotation = false;
              break;
            }
          }

          // Use "()" for annotation if found
          if (annotation) {
            verseText = '[' + firstLine + '] ' + verseText.substring(firstLinePos + 1);
          }
        }
      }
    }

    return verseText;
  }

  async isBibleValidAsync(version) {
    try {
      const bible = await this.loadBibleAsync(version);
      return bible !== null && Object.keys(bible).length > 0;
    } catch (e) {
      console.log(e);
      return false;
    }
  }

  async downloadBibleAsync(bible) {
    console.log('downloadBibleAsync:' + bible);
    try {
      const remoteUri = Config.DownloadBibleUrl + bible + '.json';
      await downloadAsync(`book-${bible}`, remoteUri);
    } catch (error) {
      alert(error);
      console.log(error);
    }
  }

  async ensureBibleIsDownloadedAsync(id) {
    const version = this.getBibleVersion(id);
    if (!version || version.useWeb) {
      return;
    }

    const valid = await this.isBibleValidAsync(id);
    console.log(`ensureBibleIsDownloadedAsync: ${valid}`);
    if (!valid) {
      await this.downloadBibleAsync(version.id);
      console.log('ensureBibleIsDownloadedAsync: downloaded');
    }
  }

  getBibleVersion(id) {
    if (!id) {
      return null;
    }

    for (const lang in Config.BibleVersions) {
      const vers = Config.BibleVersions[lang];
      for (const i in vers) {
        if (vers[i].id === id) {
          return vers[i];
        }
      }
    }

    return null;
  }

  isWebVersion() {
    const bibleVersion = getCurrentUser().getBibleVersion();
    const version = this.getBibleVersion(bibleVersion);
    return version && version.useWeb;
  }

  render = () => {
    // Show Web version
    if (this.isWebVersion()) {
      const search = `${this.props.route.params.book} ${this.props.route.params.verse}`;
      let bibleVersion = getCurrentUser().getBibleVersion();
      if (bibleVersion === 'niv2011') {
        bibleVersion = 'niv'; // web search on biblegateway only has 'niv'
      }
      return (
        <WebView
          allowsLinkPreview={true}
          showsVerticalScrollIndicator={true}
          key={this.webViewKey++}
          style={{ flex: 1 }}
          source={{
            uri: `https://www.biblegateway.com/passage/?search=${search}&version=${bibleVersion}`
          }}
        />
      );
    }

    const passage = this.state.passage;
    if (!passage || this.state.busy) {
      return <LoadingIndicator />;
    }

    const fontSize = getCurrentUser().getMediumFontSize();

    // Using text (some Android device [deviceYearClass <= 2013?] cannot show CJK or even UTF8 in WebView)
    // https://github.com/facebook/device-year-class - RAM 2GB - 2013, 3GB - 2014, 5GB - 2015...
    if (Platform.OS == 'android' && Device.deviceYearClass <= 2013) {
      let line = '';
      for (const verse of passage) {
        if (verse) {
          line += verse.verse + ' ' + verse.text + '\n';
        }
      }

      return (
        <ScrollView scrollIndicatorInsets={{ right: 1 }}>
          <Text
            selectable={true}
            style={{
              marginVertical: 6,
              marginHorizontal: 6,
              fontSize,
              lineHeight: fontSize * 1.5
            }}>
            {line}
          </Text>
        </ScrollView>
      );
    }

    // Show WebView
    let html = `<head>
            <meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0" />
            <style>
              body { -webkit-text-size-adjust: none; text-size-adjust: none; font-family: -apple-system,Segoe UI,Helvetica,Arial,sans-serif; }
              td { font-size: ${fontSize}px; padding: 4px;}
              tr:nth-child(even) { background: #EEEEEE }
            </style>
          </head><body onload="onload()"><table style="width: 100%">`;
    for (const verse of passage) {
      html += `<tr><td>${verse.verse} ${verse.text.replace(/\n/g, '<br>')}</td></tr>`;
    }

    if (Array.isArray(passage) && passage.length > 0) {
      let bibleVersions = getCurrentUser().getBibleVersionDisplayName();
      if (getCurrentUser().getBibleVersion2DisplayName()) {
        bibleVersions += '/' + getCurrentUser().getBibleVersion2DisplayName();
      }
      html += `<tr><td style="text-align:right; font-size: 12px;">[${bibleVersions}]</td></tr>`;
    }
    const jsScript = `function onload() {
      document.body.scrollTop=${this.state.scrollTop};
      window.onscroll = function (e) {
        window.ReactNativeWebView.postMessage(document.body.scrollTop);
      };
    }`;
    html += `</table><p style="padding: 7px;"></p><script>${jsScript}</script></body>`;

    return (
      <WebView
        style={{ flex: 1 }}
        allowsLinkPreview={true}
        source={{ html }}
        showsVerticalScrollIndicator={true}
        onMessage={async (evt) => {
          const data = JSON.parse(evt.nativeEvent.data);
          if (typeof data === 'number') {
            await getCurrentUser().setPropertyAsync('BibleScreen', {
              book: this.book,
              verse: this.verse,
              scrollTop: data
            });
          }
        }}
      />
    );
  };
}
