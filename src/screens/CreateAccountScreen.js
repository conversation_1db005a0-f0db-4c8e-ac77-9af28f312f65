import { But<PERSON>, KeyboardView } from '@/components';
import { CheckBox, Input } from '@rneui/themed';
import { Dimensions, Keyboard, StyleSheet, Text, View } from 'react-native';
import { FmCheckBoxCheckedIcon, FmCheckBoxUncheckedIcon } from '@/fishMeet/components/FmIcons';
import { showMessage, showServerErrMessage } from '@/components/MessageBox';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import { ExplanationHeader } from '@/fishMeet/components/ExplanationHeader';
import { FmStyles } from '@/fishMeet/styles/fmStyles';
import { FmText } from '@/fishMeet/components/FmText';
import { InputField } from '@/fishMeet/components/InputField';
import React from 'react';
import { fmStyles } from '@/fishMeet/styles/screens/createAccountScreenStyles';
import { getCurrentUser } from '@/utils/user';
import { getInputContainerStyle } from '@/styles/styles';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';

export default class CreateAccountScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ navigation }) => ({
    title: i18n2.t('NewAcct')
  });

  constructor(props) {
    super(props);

    this.state = {
      userId: '',
      password: '',
      displayName: ''
    };

    this.displayNameInputRef = React.createRef(null);
    this.emailInputRef = React.createRef(null);
    this.passwordInputRef = React.createRef(null);
  }

  validateEmail(mail) {
    // eslint-disable-next-line no-useless-escape
    if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(mail)) {
      return true;
    }

    return false;
  }

  async createUser() {
    if (!this.state.displayName || this.state.displayName.length < 0) {
      this.displayNameInputRef.current.shake();
      this.displayNameInputRef.current.focus();
      return;
    }

    if (!this.state.userId || this.state.userId.length < 6) {
      this.emailInputRef.current.shake();
      this.emailInputRef.current.focus();
      return;
    }

    if (!this.state.password || this.state.password.length < 6) {
      this.passwordInputRef.current.shake();
      this.passwordInputRef.current.focus();
      return;
    }

    if (!this.state.agreeEULA) {
      showMessage({
        message: i18n2.t('ReadTerms'),
        type: 'info'
      });
      return;
    }

    try {
      this.setState({ busy: true });
      const body = {
        displayName: this.state.displayName,
        loginId: this.state.userId,
        password: this.state.password
      };

      const result = await globalThis.dsObject.createUser(body);
      if (!result) {
        this.emailInputRef.current.focus();
        return;
      }

      if (!result.body.accessToken) {
        showServerErrMessage();
        return;
      }

      // save token and make sure user is logged in
      await getCurrentUser().setUserInfoAsync({
        accessToken: result.body.accessToken,
        nickname: this.state.displayName
      });

      if (isFishMeet) {
        this.context.checkUserUpdateAsync();
        await getCurrentUser().loginAsync();
      } else {
        this.props.navigation.navigate('AccountDone');
      }
    } finally {
      this.setState({ busy: false });
    }
  }

  render() {
    const currentUser = getCurrentUser();
    const fontSize = currentUser.getMediumFontSize();
    const smallFontSize = currentUser.getSmallMinusFontSize();

    return isFishMeet ? this.fmRender(fontSize) : this.iDigestRender(fontSize, smallFontSize);
  }

  iDigestRender = (fontSize, smallFontSize) => {
    const inputContainerStyle = getInputContainerStyle(Dimensions.get('window').width).inputContainerStyle;

    return (
      <KeyboardView>
        <View style={styles.container}>
          <Text style={[styles.textStyle, { fontSize }]}>{i18n2.t('DisplayName')}</Text>
          <Text style={[styles.tipStyle, { fontSize: smallFontSize }]}>{i18n2.t('CreateAccountScreen.NameTip')}</Text>
          <Input
            inputContainerStyle={inputContainerStyle}
            errorStyle={{ height: 0 }}
            placeholder={i18n2.t('DisplayNameExample')}
            defaultValue={this.state.displayName}
            ref={this.displayNameInputRef}
            onChangeText={(text) => {
              this.setState({ displayName: text });
            }}
            submitBehavior='submit'
            onSubmitEditing={() => {
              this.emailInputRef.current.focus();
            }}
          />

          <Text style={[styles.textStyle, { fontSize }]}>{i18n2.t('Email')}</Text>
          <Text style={[styles.tipStyle, { fontSize: smallFontSize }]}>{i18n2.t('CreateAccountScreen.EmailTip')}</Text>
          <Input
            inputContainerStyle={inputContainerStyle}
            ref={this.emailInputRef}
            autoCorrect={false}
            autoCapitalize='none'
            returnKeyType={'next'}
            onChangeText={(text) => {
              this.setState({ userId: text });
            }}
            errorStyle={{ height: 0 }}
            defaultValue={this.state.userId}
            placeholder={i18n2.t('EmailExample')}
            submitBehavior='submit'
            onSubmitEditing={() => {
              this.passwordInputRef.current.focus();
            }}
          />

          <Text style={[styles.textStyle, { fontSize }]}>{i18n2.t('Common.Pwd')}</Text>
          <Input
            inputContainerStyle={inputContainerStyle}
            placeholder='*******'
            ref={this.passwordInputRef}
            defaultValue={this.state.password}
            secureTextEntry={true}
            errorStyle={{ height: 0 }}
            onChangeText={(text) => {
              this.setState({ password: text });
            }}
            submitBehavior='submit'
            onSubmitEditing={() => {
              Keyboard.dismiss();
            }}
          />

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignContent: 'center',
              alignItems: 'center',
              marginHorizontal: 30
            }}>
            <CheckBox
              containerStyle={styles.checkBoxStyle}
              checked={this.state.agreeEULA}
              checkedColor={Colors.darkBlue}
              onPress={() => this.setState({ agreeEULA: !this.state.agreeEULA })}
            />
            <Text style={[styles.textStyle, { fontSize, marginTop: 0 }]}>
              <Text onPress={() => this.setState({ agreeEULA: !this.state.agreeEULA })}>{i18n2.t('Agree')}</Text>
              <Text
                style={[styles.textStyle, { fontSize, color: '#0044cc' }]}
                onPress={() => {
                  this.props.navigation.navigate('WebApp', {
                    url: 'https://gracetechstorage.blob.core.windows.net/idigest/tou.html',
                    title: i18n2.t('Terms')
                  });
                }}>
                {i18n2.t('Terms')}
              </Text>
              <Text style={[styles.textStyle, { fontSize }]}> & </Text>
              <Text
                style={[styles.textStyle, { fontSize, color: '#0044cc' }]}
                onPress={() => {
                  this.props.navigation.navigate('WebApp', {
                    url: 'https://gracetechstorage.blob.core.windows.net/idigest/privacy.html',
                    title: i18n2.t('Privacy')
                  });
                }}>
                {i18n2.t('Privacy')}
              </Text>
            </Text>
          </View>

          <Button
            title={i18n2.t('Submit')}
            onPress={() => {
              this.createUser();
            }}
          />
        </View>
      </KeyboardView>
    );
  };

  fmRender = (fontSize) => (
    <KeyboardView>
      <View style={FmStyles.screenContainer}>
        <ExplanationHeader
          titleText={i18n2.t('LoginScreen.HeaderTitle')}
          explanationText={i18n2.t('LoginScreen.HeaderExplanation')}
        />

        <InputField
          ref={this.displayNameInputRef}
          label={i18n2.t('DisplayName')}
          placeholder={i18n2.t('DisplayNameExample')}
          defaultValue={this.state.displayName}
          returnKeyType='next'
          onChangeText={(text) => {
            this.setState({ displayName: text });
          }}
          onSubmitEditing={() => {
            this.emailInputRef.current.focus();
          }}
        />
        <View style={FmStyles.sectionSpacer} />

        <InputField
          inputType='email'
          ref={this.emailInputRef}
          label={i18n2.t('Email')}
          defaultValue={this.state.userId}
          onChangeText={(text) => {
            this.setState({ userId: text });
          }}
          onSubmitEditing={() => {
            this.passwordInputRef.current.focus();
          }}
        />
        <View style={FmStyles.sectionSpacer} />

        <InputField
          inputType='password'
          ref={this.passwordInputRef}
          label={i18n2.t('Common.Pwd')}
          defaultValue={this.state.password}
          returnKeyType='done'
          onChangeText={(text) => {
            this.setState({ password: text });
          }}
          onSubmitEditing={() => {
            Keyboard.dismiss();
          }}
        />

        <View style={fmStyles.termsContainer}>
          {this.state.agreeEULA ? (
            <FmCheckBoxCheckedIcon
              style={fmStyles.checkBox}
              onPress={() => this.setState({ agreeEULA: !this.state.agreeEULA })}
            />
          ) : (
            <FmCheckBoxUncheckedIcon
              style={fmStyles.checkBox}
              onPress={() => this.setState({ agreeEULA: !this.state.agreeEULA })}
            />
          )}
          <View style={fmStyles.termsTextContainer}>
            <FmText style={{ fontSize }}>
              <FmText style={{ fontSize }} onPress={() => this.setState({ agreeEULA: !this.state.agreeEULA })}>
                {i18n2.t('Agree')}
              </FmText>
              <FmText
                style={{ ...fmStyles.linkText, fontSize }}
                onPress={() => {
                  this.props.navigation.navigate('WebApp', {
                    url: 'https://gracetechstorage.blob.core.windows.net/idigest/tou.html',
                    title: i18n2.t('Terms')
                  });
                }}>
                {i18n2.t('Terms')}
              </FmText>
              {/* eslint-disable-next-line react-native/no-raw-text */}
              {' & '}
              <FmText
                style={{ ...fmStyles.linkText, fontSize }}
                onPress={() => {
                  this.props.navigation.navigate('WebApp', {
                    url: 'https://gracetechstorage.blob.core.windows.net/idigest/privacy.html',
                    title: i18n2.t('Privacy')
                  });
                }}>
                {i18n2.t('Privacy')}
              </FmText>
            </FmText>
          </View>
        </View>

        <View style={FmStyles.buttonContainer}>
          <Button
            title={i18n2.t('SignUp')}
            onPress={() => {
              this.createUser();
            }}
          />
        </View>
      </View>
    </KeyboardView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff'
  },
  textStyle: {
    marginTop: 15,
    color: Colors.darkBlue,
    fontSize: 16
  },
  tipStyle: {
    marginTop: 6,
    color: Colors.darkBlue,
    textAlign: 'center'
  },
  checkBoxStyle: {
    borderColor: '#fff',
    width: 25
  }
});
