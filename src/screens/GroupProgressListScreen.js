import { ListItem, NavBarTitle } from '@/components';
import { ScrollView, Text, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';

export default class GroupProgressListScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ route }) => ({
    headerTitle: () => <NavBarTitle title={route.params.groupName} subTitle={i18n2.t('Common.StudyProgress')} />
  });

  constructor(props) {
    super(props);

    const params = this.props.route.params;
    this.groupId = params.groupId;
    this.groupName = params.groupName;
    this.classes = params.classes;
    console.log(this.classes);
  }

  render() {
    if (!Array.isArray(this.classes) || this.classes.length === 0) {
      return (
        <Text
          style={{
            fontSize: getCurrentUser().getMediumFontSize(),
            color: Colors.darkBlue,
            marginLeft: this.context.insets.left,
            marginRight: this.context.insets.right,
            lineHeight: getCurrentUser().getMediumFontSize() * 1.5,
            padding: 14
          }}>
          {i18n2.t('GroupProgressListScreen.GroupNotTakenClass')}
          {i18n2.t('GroupProgressListScreen.AsMembersGoThroughDiscussion')}
        </Text>
      );
    }

    let keyIndex = 0;
    return (
      <ScrollView
        style={{ flex: 1 }}
        scrollIndicatorInsets={{ right: 1 }}
        contentContainerStyle={{ marginLeft: this.context.insets.left, marginRight: this.context.insets.right }}>
        {this.classes.map((item) => (
          <View key={keyIndex++} style={{ marginVertical: 5 }}>
            <ListItem
              onPress={() => {
                this.props.navigation.navigate('GroupProgress', {
                  groupId: this.groupId,
                  groupName: this.groupName,
                  classId: item.classId,
                  classTitle: item.className
                });
              }}
              textLeft={item.className}
              textRight={item.progress.slice(-3)}
            />
          </View>
        ))}
      </ScrollView>
    );
  }
}
