import * as Clipboard from 'expo-clipboard';

import {
  Animated,
  Dimensions,
  Easing,
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  useWindowDimensions
} from 'react-native';
import { ChapterData, ETranslation, IChapter, IVerse, calculateVerseScrollPosition } from '@/utils/bibleUtils';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useFocusEffect, useNavigation, useRoute } from '@react-navigation/native';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';
import { getObjectAsync, setObjectAsync } from '@/dataStorage/localStorage';

import { getFontSize } from '@/utils/getFontSize';

interface RouteParams {
  bookId: number;
  bookName: string;
  chapter?: number;
  verse?: number;
}

export default function BibleChapterScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { bookId = 1, bookName, chapter = 1, verse } = route.params as RouteParams;
  const flatListRef = useRef<FlatList<ChapterData>>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const stopLoadRef = useRef<boolean>(true);
  const itemHeightsRef = useRef<{ [key: number]: number }>({});

  const [allChaptersData, setAllChaptersData] = useState<ChapterData[]>([]);
  const [chaptersData, setChaptersData] = useState<ChapterData[]>([]);
  const [currentChapter, setCurrentChapter] = useState(chapter);
  const horizontalScrollX = useRef<number>(0);

  const totalChapters = allChaptersData.length;
  const chapterNumbers = Array.from({ length: totalChapters }, (_, i) => i + 1);
  const windowWidth = useWindowDimensions().width;
  const windowHeight = useWindowDimensions().height;
  const verseCircleSize = windowWidth / 7.5 - 5;

  // Function to check if a chapter is visible in the horizontal ScrollView
  // Wrapped in useCallback to prevent recreation on every render
  const isChapterVisible = useCallback(
    (chapter: number) => {
      // Calculate the position of the chapter in the ScrollView
      const chapterPositionRight = chapter * (verseCircleSize + 5);
      const chapterPositionLeft = (chapter - 1) * (verseCircleSize + 5);
      // Check if the chapter is within the visible area
      const visibleLeft = chapterPositionLeft >= horizontalScrollX.current;
      const visibleRight =
        chapterPositionRight >= horizontalScrollX.current &&
        chapterPositionRight < horizontalScrollX.current + windowWidth;
      return { visible: visibleRight && visibleLeft, visibleLeft };
    },
    [verseCircleSize, windowWidth]
  );
  // We'll define the effect after loadAllChapters is declared

  // Wrap loadChapterData in useCallback to avoid recreation on every render
  const loadChapterData = useCallback(async () => {
    try {
      // Get the selected translations from settings
      const settings = await getObjectAsync('bible.settings');
      let selectedTranslations: ETranslation[] = [ETranslation.cuvs];

      if (settings) {
        if (Array.isArray(settings)) {
          // New format: array of selected translations
          selectedTranslations = settings as ETranslation[];
        } else if (settings.versions && Array.isArray(settings.versions)) {
          // Old format: {versions: [...]} object
          selectedTranslations = settings.versions;
        }
      }

      // Create a map to store chapter data for each translation
      const translationsData: Record<ETranslation, IChapter | null> = {
        cuvs: null,
        cuvt: null,
        asv: null
      };

      // Load data for each selected translation
      for (const translation of selectedTranslations) {
        const storageKey = `bible.chapters.${bookId}.${translation}`;
        const chapterData = (await getObjectAsync(storageKey)) as IChapter;

        let hasData = false;

        if (chapterData && Object.keys(chapterData).length > 0) {
          hasData = true;
        }

        if (hasData) {
          translationsData[translation as ETranslation] = chapterData;
        }
      }

      // Use the first translation as the base structure
      const baseTranslation = selectedTranslations[0] as ETranslation;
      const baseChapterData = translationsData[baseTranslation];

      if (!baseChapterData) {
        console.error(`No data found for base translation ${baseTranslation}`);
        return null;
      }

      // Merge translations into a single chapter data structure
      const mergedChapterData: IChapter = {};

      Object.keys(baseChapterData).forEach((chapterNum) => {
        const verses = baseChapterData[chapterNum] || [];
        mergedChapterData[chapterNum] = verses.map((verse) => {
          const verseWithTranslations: IVerse = {
            ...verse,
            translations: {}
          };

          // Add the base translation content
          verseWithTranslations.translations![baseTranslation] = verse.content;

          // Add content from other translations
          for (const translation of selectedTranslations) {
            if (translation === baseTranslation) {
              continue;
            }

            const translationData = translationsData[translation as ETranslation];
            if (translationData && translationData[chapterNum]) {
              const matchingVerse = translationData[chapterNum]?.find((v) => v.verseId === verse.verseId);
              if (matchingVerse) {
                verseWithTranslations.translations![translation as ETranslation] = matchingVerse.content;
              }
            }
          }

          return verseWithTranslations;
        });
      });

      return mergedChapterData;
    } catch (error) {
      console.error('Failed to load chapter data:', error);
      return null;
    }
  }, [bookId]);

  // Use loadChapterData in loadAllChapters
  const loadAllChapters = useCallback(async () => {
    const chapterData = await loadChapterData();
    if (chapterData) {
      // Load all chapters data at once
      const allChapters: ChapterData[] = [];
      // Process verses: merge empty content verses with previous non-empty verse
      Object.keys(chapterData).forEach((chapterNum) => {
        const chapterNumber = parseInt(chapterNum);
        const verses = chapterData[chapterNum] || [];
        const processedVerses: IVerse[] = [];

        let i = 0;
        while (i < verses.length) {
          const currentVerse = verses[i] as IVerse;

          // If current verse has no content or empty content
          if (!currentVerse?.content || currentVerse.content.trim() === '') {
            // Look for next verse with content
            let j = i + 1;
            while (j < verses.length && (!verses[j]?.content || verses[j]?.content.trim() === '')) {
              j++;
            }

            // If we have a previous processed verse and found a next verse with content
            if (processedVerses.length > 0 && j < verses.length) {
              // Modify the previous verse to include this range
              const lastVerse = processedVerses[processedVerses.length - 1] as IVerse;
              lastVerse.verse = `${lastVerse?.verse}-${currentVerse?.verse}`;
            }

            i++;
            continue;
          }

          // Current verse has content, check if next verses are empty
          let j = i + 1;
          let endVerse = currentVerse.verse;

          while (j < verses.length && (!verses[j]?.content || verses[j]?.content.trim() === '')) {
            endVerse = verses[j]?.verse || 1;
            j++;
          }

          // Add the verse (possibly with merged range)
          processedVerses.push({
            ...currentVerse,
            verse: j > i + 1 ? `${currentVerse.verse}-${endVerse}` : currentVerse.verse
          });

          i = j;
        }

        allChapters.push({
          chapter: chapterNumber,
          verses: processedVerses
        });
      });

      // Sort chapters by number
      allChapters.sort((a, b) => a.chapter - b.chapter);

      // Set the total chapters data
      setAllChaptersData(allChapters);

      // Calculate the range of chapters to display
      const targetChapter = Math.min(chapter, allChapters.length);
      const startChapter = Math.max(1, targetChapter - 1);
      const endChapter = Math.min(allChapters.length, targetChapter + 1);

      // Set the initial chapters to display
      const initialChapters = allChapters.slice(startChapter - 1, endChapter);
      setChaptersData(initialChapters);

      // Set the current chapter
      setCurrentChapter(targetChapter);
      // Calculate the index of the target chapter in the initial data
      const targetIndex = initialChapters.findIndex((chapter) => chapter.chapter === targetChapter);

      if (targetIndex !== -1) {
        setTimeout(() => {
          console.log('itemHeightsRef.current[targetIndex]', itemHeightsRef.current);

          // Get the chapter data for precise calculation
          const targetChapterData = initialChapters[targetIndex];
          const chapterHeight = itemHeightsRef.current[targetChapter] || 0;

          // Calculate precise scroll position using the new method
          const preciseViewOffset =
            chapterHeight > 0 && targetChapterData && verse
              ? calculateVerseScrollPosition(
                targetChapterData,
                verse,
                chapterHeight,
                windowHeight,
                verseCircleSize,
                getFontSize().mediumFontSize,
                windowWidth
              )
              : verseCircleSize;

          flatListRef.current?.scrollToIndex({
            index: targetIndex,
            animated: false,
            viewOffset: preciseViewOffset
          });

          // Initialize the horizontal scroll position
          const initialScrollX = (targetChapter - 1) * (verseCircleSize + 5);
          horizontalScrollX.current = initialScrollX;
          scrollViewRef.current?.scrollTo({
            x: initialScrollX,
            animated: false
          });
        }, 300);
      }

      setTimeout(() => {
        stopLoadRef.current = false;
      }, 300);
    }
  }, [chapter, verse, loadChapterData, verseCircleSize, windowWidth, windowHeight]);

  const handleChapterSelect = (targetChapter: number) => {
    stopLoadRef.current = true;
    setCurrentChapter(targetChapter);
    // Calculate the range of chapters to display
    const startChapter = Math.max(1, targetChapter - 1);
    const endChapter = Math.min(totalChapters, targetChapter + 1);

    // Update the chapters data with the new range
    const newChaptersData = allChaptersData.slice(startChapter - 1, endChapter);
    setChaptersData(newChaptersData);

    setTimeout(() => {
      flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
      setTimeout(() => {
        flatListRef.current?.scrollToIndex({
          index: targetChapter === 1 ? 0 : 1,
          animated: false,
          viewOffset: verseCircleSize - 15
        });
        setTimeout(() => {
          stopLoadRef.current = false;
        }, 300);
      }, 100);
    }, 50);
  };
  useEffect(() => {
    const chapterVisible = isChapterVisible(currentChapter);
    if (!chapterVisible?.visible) {
      const targetScrollX = Math.min(
        chapterNumbers.length * (verseCircleSize + 5),
        currentChapter * (verseCircleSize + 5) -
        (!chapterVisible?.visibleLeft ? ((verseCircleSize + 5) * 3) / 2 : windowWidth - (verseCircleSize + 5) / 2)
      );
      scrollViewRef.current?.scrollTo({
        x: targetScrollX,
        animated: true
      });
    }
    setObjectAsync('bible.currentVerse', { verseId: allChaptersData[currentChapter - 1]?.verses?.[0]?.verseId });
  }, [currentChapter, allChaptersData, chapterNumbers.length, isChapterVisible, verseCircleSize, windowWidth]);

  // Define loadAllChaptersEffect after loadAllChapters is declared
  const loadAllChaptersEffect = useCallback(() => {
    loadAllChapters();
  }, [loadAllChapters]);

  useFocusEffect(loadAllChaptersEffect);

  useSetNavigationOptions({
    title: bookName,
    headerRight: () => (
      <TouchableOpacity style={styles.settingsButton} onPress={() => navigation.navigate('BibleSetting' as never)}>
        <Image style={styles.bibleIcon} source={require('@/assets/images/icon-Bible2.jpg')}></Image>
      </TouchableOpacity>
    )
  });

  // 高光和操作栏相关state
  const [highlightedVerseIds, setHighlightedVerseIds] = useState<number[]>([]);
  const [selectedVerseIds, setSelectedVerseIds] = useState<number[]>([]); // 新增：选中状态
  const [baseTranslation, setBaseTranslation] = useState<ETranslation>(ETranslation.cuvs);
  const [selectedVerse, setSelectedVerse] = useState<{ chapter: number; verse: IVerse } | null>(null);
  const [actionBarVisible, setActionBarVisible] = useState(false);
  const actionBarAnim = useRef(new Animated.Value(0)).current;

  // 切换选中状态（checkbox）
  const toggleSelection = (verseId: number) => {
    setSelectedVerseIds((prev) => {
      if (prev.includes(verseId)) {
        return prev.filter((id) => id !== verseId);
      } else {
        return [...prev, verseId];
      }
    });
  };

  // 高光切换并存储
  const toggleHighlight = async (verseId: number) => {
    setHighlightedVerseIds((prev) => {
      let newIds;
      if (prev.includes(verseId)) {
        newIds = prev.filter((id) => id !== verseId);
      } else {
        newIds = [...prev, verseId];
      }
      // 存储到本地
      const key = `bible.highlightedVerses.${baseTranslation}`;
      getObjectAsync(key).then((data) => {
        const chapterKey = `${bookId}:${currentChapter}`;
        const newData = { ...(data || {}) };
        newData[chapterKey] = newIds;
        setObjectAsync(key, newData);
      });
      return newIds;
    });
  };

  // 进入页面时读取高亮
  useEffect(() => {
    // 获取当前主版本
    (async () => {
      const settings = await getObjectAsync('bible.settings');
      let selectedTranslations: ETranslation[] = [ETranslation.cuvs];
      if (settings) {
        if (Array.isArray(settings)) {
          selectedTranslations = settings as ETranslation[];
        } else if (settings.versions && Array.isArray(settings.versions)) {
          selectedTranslations = settings.versions;
        }
      }
      const mainVersion = selectedTranslations[0] as ETranslation;
      setBaseTranslation(mainVersion);
      // 读取高亮
      const key = `bible.highlightedVerses.${mainVersion}`;
      const data = await getObjectAsync(key);
      const chapterKey = `${bookId}:${currentChapter}`;
      setHighlightedVerseIds((data && data[chapterKey]) || []);
    })();
  }, [bookId, currentChapter]);

  // 复制内容
  const copyVerseContent = (verse: IVerse) => {
    const text = verse.translations ? Object.values(verse.translations).join('\n') : verse.content;
    Clipboard.setStringAsync(text || '');
  };



  // 显示/隐藏操作栏动画
  useEffect(() => {
    if (selectedVerse) {
      setActionBarVisible(true);
      Animated.timing(actionBarAnim, {
        toValue: 1,
        duration: 250,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true
      }).start();
    } else {
      Animated.timing(actionBarAnim, {
        toValue: 0,
        duration: 200,
        easing: Easing.in(Easing.ease),
        useNativeDriver: true
      }).start(() => setActionBarVisible(false));
    }
  }, [selectedVerse]);

  return (
    <View style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.chapterSelector}
        onScroll={(event) => {
          horizontalScrollX.current = event.nativeEvent.contentOffset.x;
        }}
        scrollEventThrottle={16} // Update scroll position at 60fps for smooth tracking
      >
        {chapterNumbers.map((num) => (
          <TouchableOpacity
            key={num}
            style={[
              styles.chapterButton,
              currentChapter === num && styles.activeChapterButton,
              {
                width: verseCircleSize,
                height: verseCircleSize,
                borderRadius: verseCircleSize / 2,
                margin: 2.5
              }
            ]}
            onPress={() => handleChapterSelect(num)}>
            <Text
              style={[
                styles.chapterButtonText,
                currentChapter === num && styles.activeChapterButtonText,
                { fontSize: getFontSize().mediumFontSize }
              ]}>
              {num}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <FlatList
        ref={flatListRef}
        data={chaptersData}
        initialNumToRender={chaptersData.length}
        keyExtractor={(item) => item.chapter.toString()}
        onScroll={({ nativeEvent: { contentOffset } }) => {
          if (stopLoadRef.current) {
            return;
          }
          // Calculate current chapter based on scroll position
          const y = contentOffset.y;
          let totalHeight = 0;
          let currentVisibleChapter = currentChapter;

          // Find which chapter is most visible in the viewport
          for (let i = 0; i < chaptersData.length; i++) {
            const chapter = chaptersData[i];
            if (chapter) {
              const chapterHeight = itemHeightsRef.current[chapter.chapter] || 0;

              // If scroll position is within this chapter's range
              if (y >= totalHeight && y < totalHeight + chapterHeight) {
                currentVisibleChapter = chapter.chapter;
                break;
              }

              totalHeight += chapterHeight;
            }
          }

          // Update current chapter if it changed
          if (currentVisibleChapter !== currentChapter) {
            setCurrentChapter(currentVisibleChapter);
          }
        }}
        onEndReached={() => {
          if (stopLoadRef.current) {
            return;
          }
          stopLoadRef.current = true;
          if (currentChapter < totalChapters && chaptersData) {
            const currentChapter = chaptersData[chaptersData.length - 1]?.chapter || 1;
            const nextChapter = currentChapter + 1;

            // Calculate the range of chapters to display
            const startChapter = Math.max(1, nextChapter - 1);
            const endChapter = Math.min(totalChapters, nextChapter + 1);

            // Update the chapters data with the new range
            setChaptersData(allChaptersData.slice(startChapter - 1, endChapter));
            setTimeout(() => {
              flatListRef.current?.scrollToIndex({
                index: 0,
                animated: false,
                viewOffset: -(itemHeightsRef.current[currentChapter] || 0) + Dimensions.get('window').height - 200
              });
              setTimeout(() => {
                stopLoadRef.current = false;
              }, 300);
            }, 100);
          }
        }}
        onStartReached={({ distanceFromStart }) => {
          if (stopLoadRef.current || distanceFromStart > 20) {
            return;
          }
          if (currentChapter > 1 && chaptersData) {
            stopLoadRef.current = true;
            const currentChapter = chaptersData[0]?.chapter || 1;
            const prevChapter = currentChapter - 1;

            // Calculate the range of chapters to display
            const startChapter = Math.max(1, prevChapter - 1);
            const endChapter = Math.min(totalChapters, prevChapter + 1);

            // Update the chapters data with the new range
            setChaptersData(allChaptersData.slice(startChapter - 1, endChapter));

            setTimeout(() => {
              flatListRef.current?.scrollToIndex({
                index: 1,
                animated: false,
                viewOffset: 200
              });
              setTimeout(() => {
                stopLoadRef.current = false;
              }, 300);
            }, 100);
          }
        }}
        renderItem={({ item }) => (
          <View
            style={styles.chapterContainer}
            onLayout={(event) => {
              // Store the height of each chapter item
              const { height } = event.nativeEvent.layout;
              itemHeightsRef.current[item.chapter] = height;
            }}>
            {item.verses.map((verse) => (
              <TouchableOpacity
                key={verse.verseId}
                style={[
                  styles.verseContainer,
                  highlightedVerseIds.includes(verse.verseId) && { backgroundColor: '#fff3cd', borderRadius: 8 }
                ]}
                activeOpacity={0.7}
                onPress={() => toggleHighlight(verse.verseId)}
                onLongPress={() => setSelectedVerse({ chapter: item.chapter, verse })}
                delayLongPress={500}>
                <View style={styles.verseHeader}>
                  <Text
                    style={[
                      styles.verseId,
                      { fontSize: getFontSize().smallFontSize, lineHeight: getFontSize().smallFontSize + 4 }
                    ]}>{`${item.chapter}:${verse.verse}`}</Text>
                  {/* 操作栏出现后所有verse都显示checkbox */}
                  {actionBarVisible && (
                    <TouchableOpacity
                      style={styles.checkbox}
                      onPress={() => toggleSelection(verse.verseId)}>
                      <Text style={{ fontSize: 18, color: '#007AFF' }}>
                        {selectedVerseIds.includes(verse.verseId) ? '☑️' : '☐'}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
                {/* Display verse number and content from all translations */}
                <View style={styles.verseContentContainer}>
                  {verse.translations &&
                    Object.entries(verse.translations).map(([translation, content]) => {
                      return (
                        <View key={translation} style={styles.translationContent}>
                          <Text
                            style={[
                              styles.verseContent,
                              { fontSize: getFontSize().mediumFontSize, lineHeight: getFontSize().mediumFontSize + 4 }
                            ]}>
                            {content}
                          </Text>
                        </View>
                      );
                    })}
                </View>
                {verse.paraStart === 1 && <View style={styles.paragraphIndicator} />}
              </TouchableOpacity>
            ))}
          </View>
        )}
        contentContainerStyle={{
          paddingHorizontal: 15,
          paddingBottom: 30,
          paddingTop: verseCircleSize
        }}
        onScrollToIndexFailed={(info) => {
          // First scroll to the beginning of the list
          flatListRef.current?.scrollToOffset({
            offset: 0,
            animated: false
          });
          // Then try to scroll to the target index
          setTimeout(() => {
            if (flatListRef.current) {
              flatListRef.current.scrollToIndex({
                index: info.index,
                animated: false,
                viewPosition: 0
              });
            }
          }, 200);
        }}
      />
      {/* 自定义absolute操作栏 */}
      {actionBarVisible && (
        <Animated.View
            style={[
              styles.actionBar,
              {
                transform: [
                  {
                    translateY: actionBarAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [120, 0]
                    })
                  }
                ],
                opacity: actionBarAnim
              }
            ]}>
            <TouchableOpacity
              style={styles.actionBarBtn}
              onPress={() => {
                // 对所有选中的verses进行高亮操作
                selectedVerseIds.forEach(verseId => toggleHighlight(verseId));
              }}>
              <Text style={{ fontSize: 22, color: '#ffc107' }}>★</Text>
              <Text style={styles.actionBarBtnText}>高亮</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionBarBtn}
              onPress={() => {
                // 对所有选中的verses取消高亮
                selectedVerseIds.forEach(verseId => {
                  if (highlightedVerseIds.includes(verseId)) {
                    toggleHighlight(verseId);
                  }
                });
              }}>
              <Text style={{ fontSize: 22, color: '#888' }}>☆</Text>
              <Text style={styles.actionBarBtnText}>去高亮</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionBarBtn}
              onPress={() => {
                // 复制所有选中的verses
                if (selectedVerseIds.length > 0) {
                  const selectedVerses = chaptersData.flatMap(chapter =>
                    chapter.verses.filter(verse => selectedVerseIds.includes(verse.verseId))
                  );
                  const text = selectedVerses.map(verse => {
                    const verseText = verse.translations ? Object.values(verse.translations).join('\n') : verse.content;
                    return `${verse.verse}: ${verseText}`;
                  }).join('\n\n');
                  Clipboard.setStringAsync(text);
                }
              }}>
              <Text style={{ fontSize: 22, color: '#888' }}>📋</Text>
              <Text style={styles.actionBarBtnText}>复制</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionBarBtn}
              onPress={() => {
                setSelectedVerse(null);
                setSelectedVerseIds([]); // 清空选中状态
              }}>
              <Text style={{ fontSize: 22, color: '#888' }}>✕</Text>
              <Text style={styles.actionBarBtnText}>关闭</Text>
            </TouchableOpacity>
          </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  settingsButton: { padding: 5 },
  bibleIcon: {
    width: 32,
    height: 32
  },
  chapterSelector: {
    position: 'absolute',
    top: 0,
    width: '100%',
    zIndex: 2
  },
  chapterButton: {
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#fff'
  },
  chapterButtonText: { color: '#333' },
  activeChapterButton: { backgroundColor: '#333' },
  activeChapterButtonText: { color: '#fff' },
  chapterContainer: { paddingVertical: 15 },
  verseContainer: { marginBottom: 10 },
  verseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  verseId: { color: '#666', fontWeight: 'bold' },
  verseContentContainer: { marginTop: 5 },
  translationContent: { marginBottom: 4 },
  verseContent: { color: '#333' },
  paragraphIndicator: { height: 10 },
  actionBar: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 18,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    zIndex: 11,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8
  },
  actionBarBtn: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1
  },
  actionBarBtnText: {
    marginTop: 6,
    fontSize: 15,
    color: '#333'
  },
  checkbox: {
    padding: 4,
    justifyContent: 'center',
    alignItems: 'center'
  }
});
