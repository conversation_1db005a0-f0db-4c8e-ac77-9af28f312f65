import { <PERSON><PERSON>, Dimensions, ScrollView, Text, TouchableOpacity, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { CheckBox } from '@rneui/themed';
import { Colors } from '@/styles/colors';
import { Config } from '@/dataStorage/config';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { OkButton } from '@/components/navigation/NavigationButtons';
import React from 'react';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';

export default class BibleSelectScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = () => ({
    title: i18n2.t('BibleVer'),
    headerLeft: () => <OkButton />
  });

  state = {
    busy: false,
    selectedIndex: 0
  };

  componentDidMount() {
    const version = this.getBibleVersion(getCurrentUser().getBibleVersion());
    if (version && version.useWeb) {
      this.setState({ selectedIndex: 2 });
    }
  }

  async onSelect(index, version) {
    this.setState({ busy: true });
    try {
      console.log(`Select: ${index} => ${version}`);

      // Save
      switch (index) {
        case 0:
          await getCurrentUser().setBibleVersionAsync(version);
          if (getCurrentUser().getBibleVersion2() === version) {
            await getCurrentUser().setBibleVersion2Async(null);
          }
          break;

        case 1:
          await getCurrentUser().setBibleVersion2Async(version);
          break;

        case 2:
          await getCurrentUser().setBibleVersionAsync(version);
          await getCurrentUser().setBibleVersion2Async(null);
          break;
      }

      // // Notify selection change
      // const onSelected = this.props.route.params.onSelected;
      // if (typeof onSelected === "function") {
      //   onSelected(
      //     getCurrentUser().getBibleVersionDisplayName(),
      //     getCurrentUser().getBibleVersion()
      //   );
      // }
    } finally {
      this.setState({ busy: false });
    }
  }

  getBibleVersion(id) {
    if (!id) {
      return null;
    }

    for (const lang in Config.BibleVersions) {
      const vers = Config.BibleVersions[lang];
      for (const i in vers) {
        if (vers[i].id === id) {
          return vers[i];
        }
      }
    }

    return null;
  }

  render() {
    let keyIndex = 1;
    const bibleVersion1 = getCurrentUser().getBibleVersion();
    const bibleVersion2 = getCurrentUser().getBibleVersion2();
    let selectedBible;
    switch (this.state.selectedIndex) {
      case 0:
        selectedBible = bibleVersion1;
        break;
      case 1:
        selectedBible = bibleVersion2;
        break;
      case 2:
        selectedBible = bibleVersion1;
        break;
    }

    const windowWidth = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right;
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: 'white',
          marginTop: 10,
          paddingLeft: this.context.insets.left,
          paddingRight: this.context.insets.right,
          paddingBottom: this.context.insets.bottom
        }}>
        <ScrollView scrollIndicatorInsets={{ right: 1 }}>
          {this.state.selectedIndex === 1 ? (
            <View
              key={keyIndex++}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                borderWidth: 0.5,
                margin: 7,
                borderColor: '#cdcdcd'
              }}>
              <CheckBox
                containerStyle={{
                  width: windowWidth - 70,
                  borderWidth: 0,
                  backgroundColor: 'white'
                }}
                checkedColor={Colors.darkBlue}
                key={keyIndex++}
                title={i18n2.t('NA')}
                checked={selectedBible === null}
                onPress={() => {
                  this.onSelect(this.state.selectedIndex, null);
                }}
              />
            </View>
          ) : null}
          {Object.keys(Config.BibleVersions).map((lang) => (
            <View key={keyIndex++} style={{ alignItems: 'center' }}>
              <Text key={keyIndex++} style={{ fontSize: getCurrentUser().getLargeFontSize(), fontWeight: 'bold' }}>
                {lang}
              </Text>
              {Config.BibleVersions[lang]
                .sort((a, b) => a.useWeb - b.useWeb) // show non-web bible first
                .map((bible) => {
                  console.log(bible.name);
                  return (
                    <View
                      key={keyIndex++}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        borderWidth: 0.5,
                        margin: 7,
                        borderColor: '#cdcdcd'
                      }}>
                      <CheckBox
                        containerStyle={{
                          width: windowWidth - 100,
                          borderWidth: 0,
                          backgroundColor: 'white'
                        }}
                        key={keyIndex++}
                        checkedColor={Colors.darkBlue}
                        title={bible.name}
                        checked={selectedBible === bible.id}
                        onPress={() => {
                          this.onSelect(this.state.selectedIndex, bible.id);
                        }}
                      />
                      <View style={{ marginRight: 7, width: 26 }}>
                        {bible.useWeb ? <MaterialCommunityIcons name='web' size={23} color={Colors.yellow} /> : null}
                      </View>
                      <View style={{ marginRight: 7, width: 26 }}>
                        {bible.copyright ? (
                          <TouchableOpacity
                            activeOpacity={1}
                            onPress={() => {
                              Alert.alert('Copyright', bible.copyright);
                            }}>
                            <MaterialCommunityIcons name='copyright' size={24} color={Colors.yellow} />
                          </TouchableOpacity>
                        ) : null}
                      </View>
                    </View>
                  );
                })}
            </View>
          ))}
        </ScrollView>
      </View>
    );
  }
}
