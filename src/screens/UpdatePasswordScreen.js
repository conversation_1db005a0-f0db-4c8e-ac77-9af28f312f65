import { Button, KeyboardView } from '@/components';
import { Dimensions, Text, View } from 'react-native';

import { BackButton } from '@/components/navigation/NavigationButtons';
import { Colors } from '@/styles/colors';
import { FmBackNavIcon } from '@/fishMeet/components/FmIcons';
import { Input } from '@rneui/themed';
import React from 'react';
import { FmText } from '@/fishMeet/components/FmText';
import { getCurrentUser } from '@/utils/user';
import { getInputContainerStyle } from '@/styles/styles';
import { getUpdatePasswordScreenStyles } from '@/fishMeet/styles/screens/updatePasswordScreenStyles';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { InputField } from '@/fishMeet/components/InputField';

export default class UpdatePasswordScreen extends React.Component {
  static navigationOptions = ({ navigation, route }) => {
    const isReset = route.params?.reset ?? false;
    const onPress = () => {
      if (isReset) {
        // reset password, go to home
        navigation.popToTop();
      } else {
        navigation.pop();
      }
    };

    return {
      title: isFishMeet ? i18n2.t('UpdatePasswordScreen.Settings') : i18n2.t('UpdatePwd'),
      headerLeft: () => {
        return isFishMeet ? <FmBackNavIcon onPress={onPress} /> : <BackButton onPress={onPress} />;
      }
    };
  };

  constructor(props) {
    super(props);

    this.state = {
      password: '',
      password2: '',
      input: 'Input'
    };
  }

  componentDidMount() {
    this.passwordInput.focus();
  }

  async updatePassword() {
    if (!this.state.password || this.state.password.length < 6) {
      this.passwordInput.shake();
      this.passwordInput.focus();
      return;
    }

    if (this.state.password !== this.state.password2) {
      this.password2Input.shake();
      this.password2Input.focus();
      return;
    }
    try {
      this.setState({ busy: true });
      const body = {
        password: this.state.password
      };
      const result = await globalThis.dsObject.updateUserProfile(body);
      if (!result) {
        return;
      }

      const isReset = this.props.route.params && this.props.route.params.reset;
      if (isReset) {
        // reset password, go to home
        this.props.navigation.popToTop();
      } else {
        this.props.navigation.pop();
      }
    } finally {
      this.setState({ busy: false });
    }
  }

  iDigestRender() {
    const inputContainerStyle = getInputContainerStyle(Dimensions.get('window').width).inputContainerStyle;

    return (
      <KeyboardView>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center'
          }}>
          <View style={{ marginTop: 20 }} />

          <Text
            style={{
              marginTop: 10,
              color: Colors.darkBlue,
              fontSize: getCurrentUser().getSmallFontSize()
            }}>
            {i18n2.t('NewPwd')}
          </Text>
          <Input
            inputContainerStyle={inputContainerStyle}
            contentContainerStyle={{ alignItems: 'center' }}
            placeholder='*******'
            ref={(input) => (this.passwordInput = input)}
            defaultValue={this.state.password}
            secureTextEntry={true}
            errorStyle={{ color: 'red' }}
            onChangeText={(text) => {
              this.setState({ password: text });
            }}
            onSubmitEditing={() => {
              this.password2Input.focus();
            }}
          />
          <Text
            style={{
              marginTop: 20,
              color: Colors.darkBlue,
              fontSize: getCurrentUser().getSmallFontSize()
            }}>
            {i18n2.t('PwdAgain')}
          </Text>

          <Input
            inputContainerStyle={inputContainerStyle}
            placeholder='*******'
            ref={(input) => (this.password2Input = input)}
            errorStyle={{ color: 'red' }}
            secureTextEntry={true}
            onChangeText={(text) => {
              this.setState({ password2: text });
            }}
            onSubmitEditing={() => {
              this.updatePassword();
            }}
          />

          <View style={{ marginTop: 20 }}>
            <Button title={i18n2.t('Submit')} onPress={() => this.updatePassword()} />
          </View>
        </View>
      </KeyboardView>
    );
  }

  fmRender() {
    const fmStyles = getUpdatePasswordScreenStyles();
    return (
      <KeyboardView>
        <View style={fmStyles.emailContainer}>
          <FmText style={fmStyles.emailText}>{i18n2.t('UpdatePasswordScreen.MyEmail')}</FmText>
          <FmText style={fmStyles.loginIdText}>{getCurrentUser().getLoginId()}</FmText>
        </View>
        <View style={fmStyles.contentContainer}>
          <View style={fmStyles.sectionSpacing} />
          <FmText style={fmStyles.newPwdText}>{i18n2.t('UpdatePasswordScreen.UpdatePassword')}</FmText>
          <InputField
            contentContainerStyle={{ alignItems: 'center' }}
            placeholder={i18n2.t('UpdatePasswordScreen.NewPassword')}
            ref={(input) => (this.passwordInput = input)}
            defaultValue={this.state.password}
            secureTextEntry={true}
            errorStyle={{ color: 'red' }}
            onChangeText={(text) => {
              this.setState({ password: text });
            }}
            onSubmitEditing={() => {
              this.password2Input.focus();
            }}
          />

          <InputField
            placeholder={i18n2.t('UpdatePasswordScreen.ConfirmPassword')}
            ref={(input) => (this.password2Input = input)}
            errorStyle={{ color: 'red' }}
            secureTextEntry={true}
            onChangeText={(text) => {
              this.setState({ password2: text });
            }}
            onSubmitEditing={() => {
              this.updatePassword();
            }}
          />
          <View style={fmStyles.submitContainer}>
            <Button title={i18n2.t('Submit')} onPress={() => this.updatePassword()} />
          </View>
        </View>
      </KeyboardView>
    );
  }

  render() {
    return isFishMeet ? this.fmRender() : this.iDigestRender();
  }
}
