/* eslint-disable react-native/no-raw-text */

import * as Device from 'expo-device';
import * as Linking from 'expo-linking';

import { Platform, ScrollView, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import Constants from 'expo-constants';
import { ListItem } from '@rneui/themed';
import React from 'react';
import { WebView } from 'react-native-webview';
import { appFullVersion } from '@/dataStorage/storage';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';

export default class SystemInfoScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = () => ({
    headerTitle: i18n2.t('SystemInfoScreen.Title')
  });

  state = {
    userAgent: '...',
    canOpenJitsi: '...'
  };

  componentDidMount = async () => {
    const url = 'org.jitsi.meet://m.mycbsf.org/public';
    const canOpenJitsi = await Linking.canOpenURL(url);
    this.setState({ canOpenJitsi });
  };

  render() {
    const oneGigaByte = 1024 * 1024 * 1024;
    const { userAgent, canOpenJitsi } = this.state;
    const user = getCurrentUser();
    return (
      <ScrollView
        style={{
          marginBottom: this.context.insets.bottom,
          paddingLeft: this.context.insets.left,
          paddingRight: this.context.insets.right
        }}>
        <ListItem bottomDivider>
          <ListItem.Content>
            <ListItem.Title>App Version {Constants.expoConfig.version}</ListItem.Title>
            <ListItem.Subtitle>
              {appFullVersion} ({Constants.expoConfig.sdkVersion}), UserId: {user.getUserId()}, ExperimentFeatures:{' '}
              {user.isTest() ? '1' : '0'}
            </ListItem.Subtitle>
          </ListItem.Content>
        </ListItem>
        <ListItem bottomDivider>
          <ListItem.Content>
            <ListItem.Title>Device</ListItem.Title>
            <ListItem.Subtitle>
              {Device.brand}/{Device.manufacturer}, OS {Device.osVersion}{' '}
              {Platform.OS === 'android' ? `(API ${Device.platformApiLevel})` : ''}
              {Device.isDevice ? '' : '(Emulator)'}
            </ListItem.Subtitle>
          </ListItem.Content>
        </ListItem>
        <ListItem bottomDivider>
          <ListItem.Content>
            <ListItem.Title>RAM</ListItem.Title>
            <ListItem.Subtitle>{(Device.totalMemory / oneGigaByte).toFixed(1)}GB</ListItem.Subtitle>
          </ListItem.Content>
        </ListItem>
        <ListItem bottomDivider>
          <ListItem.Content>
            <ListItem.Title>WebView</ListItem.Title>
            <ListItem.Subtitle>{userAgent}</ListItem.Subtitle>
          </ListItem.Content>
        </ListItem>
        <ListItem bottomDivider>
          <ListItem.Content>
            <ListItem.Title>LaunchUrl</ListItem.Title>
            <ListItem.Subtitle>{this.context.launchUrl}</ListItem.Subtitle>
          </ListItem.Content>
        </ListItem>
        <ListItem bottomDivider>
          <ListItem.Content>
            <ListItem.Title>CanOpenJitsi</ListItem.Title>
            <ListItem.Subtitle>{JSON.stringify(canOpenJitsi)}</ListItem.Subtitle>
          </ListItem.Content>
        </ListItem>
        <ListItem bottomDivider>
          <ListItem.Content>
            <ListItem.Title>Proxy</ListItem.Title>
            <ListItem.Subtitle>{getCurrentUser().getHttpsServer2()}</ListItem.Subtitle>
          </ListItem.Content>
        </ListItem>
        <View style={{ position: 'absolute', width: 1, height: 1 }}>
          <WebView
            source={{
              html: '<script>window.ReactNativeWebView.postMessage(navigator.userAgent)</script>'
            }}
            onMessage={(evt) => {
              this.setState({ userAgent: evt.nativeEvent.data });
            }}
            showsVerticalScrollIndicator={true}
          />
        </View>
      </ScrollView>
    );
  }
}
