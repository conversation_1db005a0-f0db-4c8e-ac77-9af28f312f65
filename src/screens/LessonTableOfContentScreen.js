import * as Linking from 'expo-linking';
import * as WebBrowser from 'expo-web-browser';

import { Alert, Dimensions, Text, View } from 'react-native';
import { Button, Dialog } from '@rneui/themed';
import { LoadingIndicator, ModalPopup, getGroupIcon } from '@/components';
import React, { createRef } from 'react';
import { cloneDeep, isEqual } from 'lodash-es';
import { convertFileSizeToString, getClassCoverUrl, updateHtmlFontSize } from '@/utils/helper';
import { deleteObjectAsync, getObjectAsync, setObjectAsync } from '@/dataStorage/localStorage';
import { getCurrentUser, getHttpsServer } from '@/utils/user';

import { AppContext } from '@/context/AppContext';
import { Avatar2 } from '@/components/Avatar2';
import { ClassDownloadUtil } from '@/utils/lesson/ClassDownloadUtil';
import { Colors } from '@/styles/colors';
import { NavBarButton } from '@/components/navigation/NavigationButtons';
import { WebView } from 'react-native-webview';
import { checkRegisterForPushNotificationsAsync } from '@/utils/notification';
import { deleteDownloadLessonAll } from '@/utils/lesson';
import { i18n2 } from '@/utils/i18n2';

export default class LessonTableOfContentScreen extends React.Component {
  static contextType = AppContext;

  static navigationOptions = ({ route }) => {
    const title = route.params?.title ?? '⌛';
    return {
      title,
      headerRight: () =>
        typeof route.params.onShare === 'function' ? (
          <NavBarButton
            right={true}
            image='share'
            onPress={() => {
              route.params.onShare();
            }}
          />
        ) : null
    };
  };

  constructor(props) {
    super(props);
    this.lesson = this.props.route.params.lesson;
    // Class download status constants 1-Downloadable 2-Downloaded
    this.downloadState = this.props.route.params.downloadState;
    this.scrollView = null;
    this.loadAsyncPayload = {};
    this.lessonDownloadListener = null;
    this.webViewRef = createRef();

    this.state = {
      content: {
        content: []
      },
      currentIndex: 0,
      existingGroups: [],
      toBeAddedGroups: [],
      shareToUsers: [],
      showModal: false,
      showModalItems: [],
      showModalTitle: '',
      title: '',
      htmlTemplate: '',
      dialogType: ''
    };

    getObjectAsync(`lesson/${this.lesson.name}`).then((result) => {
      if (result) {
        this.setState({ ...result });
        this.props.navigation.setParams({ title: result.title });
      }
    });

    this.props.navigation.setParams({ onShare: this.share });
  }

  componentDidMount() {
    this.context.setAudioPlayerMinimized(true);
    this.props.navigation.addListener('focus', () => {
      if (this.downloadState !== 2) {
        this.loadAsync();
      }
    });

    this.answerChangedListener = globalThis.eventEmitter.addListener(
      'answerChanged',
      ({ classId, sessionIndex, percentage }) => {
        if (this.lesson.classId === classId) {
          const { content } = this.state;
          if (content.progress[sessionIndex] !== percentage) {
            content.progress[sessionIndex] = percentage;
            this.setState({ content });
            setObjectAsync(`lesson/${this.lesson.name}`, { content });
          }
        }
      }
    );

    getCurrentUser().setPropertyAsync('classOrder', { [this.lesson.name]: Date.now() });

    // register push notification
    checkRegisterForPushNotificationsAsync();
  }

  componentWillUnmount() {
    this.answerChangedListener?.remove();
    this.lessonDownloadListener?.remove();
  }

  urlify = (text) => {
    let urlRegex = /(https?:\/\/[^\s]+)/g;
    return text.replace(urlRegex, (url) => '<a href="' + url + '">' + url + '</a>');
  };

  async loadAsync() {
    try {
      const localTemplate = getCurrentUser().getProperty(`LessonTemplate.${this.lesson.classId}`) ?? {
        version: 0
      };
      const result = await globalThis.dsObject.getLesson(this.lesson.name, localTemplate.version);
      if (!result) {
        // continue render old page
        return;
      }

      const body = result.body;
      // no need to refresh if no change
      if (isEqual(body, this.loadAsyncPayload)) {
        console.log('Same content, skip refreshing UI');
        return;
      }
      this.loadAsyncPayload = cloneDeep(body);

      // Convert old `sessions` to new `content`
      if (!Array.isArray(body.content)) {
        // 'reverse' is only supported in old 'sessions'
        const isReverse = body.displayOrder === 'reverse';
        const sessionsToShow = isReverse ? cloneDeep(body.sessions).reverse() : cloneDeep(body.sessions);
        body.content = sessionsToShow.map((item) => ({
          type: 'session',
          lesson: item.lesson,
          title: item.title
        }));
      }

      // only published class (classStatus===1) can be shared
      // public class (classType===0) can be shared
      // controlled public class (classType===1) can be shared if user can see it
      // private class (classType===2) can only be shared by owner
      const canShare = body.classStatus === 1 && (body.isClassOwner || body.classType !== 2);
      this.props.navigation.setParams({ onShare: canShare ? this.share : undefined });

      const canUnshare =
        body.groupsWithClass.findIndex((it) => it.isGroupLeader === 1) !== -1 ||
        body.shareTo.findIndex((it) => it.name) !== -1;

      // Add title image and description

      body.content.unshift({
        type: 'text',
        value: `<center><b>${i18n2.t('TableOfContent')}</b></center>`,
        tableOfContent: 1 // template can know this is the heading title
      });
      body.content.unshift({
        type: 'lessonSummary',
        cover: getClassCoverUrl(this.lesson.classId),
        description: this.urlify(body.description || ''),
        sharedTo: body.groupsWithClass.map((it) => it.name).concat(body.shareTo.map((it) => it.name)),
        canShare,
        canUnshare,
        moreText: `<center><b>${i18n2.t('TableOfContent')}</b></center>`
      });

      let htmlTemplate = this.state.htmlTemplate;
      if (body.template && body.template.version && body.template.data) {
        await setObjectAsync(`LessonTemplate.${this.lesson.classId}`, body.template);
        htmlTemplate = body.template.data; // reset html
      } else if (!body.template?.data && localTemplate.version === 0) {
        await deleteObjectAsync(`LessonTemplate.${this.lesson.classId}`);
      }

      // Use default template if html is not set
      if (!htmlTemplate) {
        let template = await getObjectAsync(`LessonTemplate.${this.lesson.classId}`);
        if (!template) {
          template = await getObjectAsync('LessonTemplate.Default');
        }
        if (template?.data) {
          htmlTemplate = template.data;
        }
      }

      if (!htmlTemplate) {
        Alert.alert(i18n2.t('Common.Failed'), i18n2.t('MissingTemplate'));
        this.props.navigation.pop();
        return;
      }

      // load current lesson index
      const progress = getCurrentUser().getProperty(`LessonProgress.${this.lesson.classId}`);
      let currentIndex = progress?.sessionId || 0;
      const maxSessions = body.content.filter((it) => it.type === 'session').length;
      if (currentIndex < 0 || currentIndex >= maxSessions) {
        currentIndex = 0;
      }

      this.setState({
        content: body,
        currentIndex,
        existingGroups: body.groupsWithClass,
        toBeAddedGroups: body.groupsWithoutClass,
        shareToUsers: body.shareTo,
        title: body.title,
        htmlTemplate
      });

      this.props.navigation.setParams({ title: body.title });

      setObjectAsync(`lesson/${this.lesson.name}`, {
        content: body,
        currentIndex,
        existingGroups: body.groupsWithClass,
        toBeAddedGroups: body.groupsWithoutClass,
        shareToUsers: body.shareTo,
        title: body.title,
        htmlTemplate
      });
    } catch (error) {
      alert(error);
      this.props.navigation.pop();
    }
  }

  async goToLesson(sessionTitle, sessionLesson, title, currentIndex) {
    const { content, existingGroups } = this.state;
    const sessions = content.content.filter((it) => it.type === 'session');
    if (currentIndex < 0 || currentIndex > sessions.length) {
      alert('Invalid index!');
      return;
    }

    const currentSession = sessions[currentIndex];
    this.props.navigation.navigate('Lesson', {
      id: this.lesson.name,
      className: currentSession.navTitle || this.state.content.title,
      weekName: currentSession.navSubTitle || sessionTitle,
      file: sessionLesson,
      bookTitle: title,
      contentTitle: this.state.content.title,
      sessionIndex: currentIndex,
      classId: this.lesson.classId,
      lesson: this.lesson,
      group: existingGroups[0],
      groups: existingGroups,
      sessions,
      downloadState: this.downloadState
    });

    const progress = getCurrentUser().getProperty(`LessonProgress.${this.lesson.classId}`);
    if (progress?.sessionId !== currentIndex) {
      await getCurrentUser().setPropertyAsync(`LessonProgress.${this.lesson.classId}`, {
        sessionId: currentIndex,
        day: 1,
        scrollTop: 0
      });
    }
  }

  removeGroupClassAsync = async (groupId) => {
    const result = await globalThis.dsObject.deleteGroupClass(groupId, this.lesson.classId);
    if (result) {
      // un-associate class with group will update my classes
      this.context.checkUserUpdateAsync();
      await this.loadAsync();
    }
  };

  share = () => {
    this.props.navigation.navigate('SelectGroupOrUser', {
      groups: this.state.toBeAddedGroups,
      onSelect: async (choice) => {
        let result;
        if (choice.userId) {
          result = await globalThis.dsObject.shareUserLesson(this.lesson.classId, choice.userId);
        } else {
          result = await globalThis.dsObject.shareGroupLesson(choice.groupId, this.lesson.classId);
        }
        if (result) {
          await this.loadAsync();
        }
      }
    });
  };

  unshare = () => {
    {
      const choices = this.state.existingGroups.filter((it) => it.isGroupLeader === 1).concat(this.state.shareToUsers);
      this.props.navigation.navigate('Select', {
        choices,
        title: i18n2.t('Unshare'),
        getDisplayName: (choice) => choice.name,
        onRenderItem: (choice) => {
          return (
            <View
              style={{
                marginTop: 10,
                marginHorizontal: 10,
                backgroundColor: choice.userId ? 'transparent' : Colors.lightBlue,
                borderColor: Colors.darkBlue,
                borderWidth: choice.userId ? 0 : 1,
                borderRadius: 25,
                height: 50,
                paddingHorizontal: 10,
                alignItems: 'baseline',
                justifyContent: 'center'
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  width: Dimensions.get('window').width - 75
                }}>
                {choice.groupId ? getGroupIcon(choice) : null}
                {choice.userId ? <Avatar2 userId={choice.userId} /> : null}
                <Text
                  style={{
                    fontSize: getCurrentUser().getSmallFontSize(),
                    fontWeight: 'bold',
                    paddingLeft: 7,
                    color: '#707070'
                  }}
                  numberOfLines={1}>
                  {choice.name}
                </Text>
              </View>
            </View>
          );
        },
        onSelect: async (choice) => {
          let result;
          if (choice.userId) {
            result = await globalThis.dsObject.deleteUserLesson(this.lesson.classId, choice.userId);
          } else {
            result = await globalThis.dsObject.deleteGroupLesson(choice.groupId, this.lesson.classId);
          }

          if (result) {
            // associate class with group will update my classes
            await this.context.checkUserUpdateAsync();
            await this.loadAsync();
          }
        }
      });
    }
  };

  getShareClassDescription = () => {
    const {
      content: { isClassOwner, classStatus, classType }
    } = this.state;

    if (classStatus === 0) {
      // Unpublished class cannot be shared
      return i18n2.t('UnpubClass');
    }

    if (classStatus === 1) {
      switch (classType) {
        case 0:
          // public class can be shared to anyone
          return i18n2.t('PublicClassShare');
        case 1:
          return i18n2.t('ControlledPublicClass');
        case 2:
          if (isClassOwner) {
            // Author can share
            return i18n2.t('PrivClassAuthor');
          } else {
            // private class cannot be shared
            return i18n2.t('PrivClassNoShare');
          }
      }
    }

    return i18n2.t('Errors.Error');
  };
  // 下载课程
  download = async () => {
    this.webViewRef.current.postMessage(ClassDownloadUtil.getDownloadingMessage());
    await ClassDownloadUtil.startDownload({
      sessions: this.state.content.content.filter((item) => item.type === 'session'),
      lesson: this.lesson,
      top: this.context.insets.top
    });
  };
  cancelDownload = () => {
    ClassDownloadUtil.cancelDownload(this.lesson.classId);
    this.downloadState = 1;
    this.webViewRef.current.postMessage(ClassDownloadUtil.getDownloadReadyMessage());
  };
  deleteDownload = () => {
    deleteDownloadLessonAll({
      sessions: this.state.content.content.filter((item) => item.type === 'session'),
      classId: this.lesson.classId
    });
    this.downloadState = 1;
    this.webViewRef.current.postMessage(ClassDownloadUtil.getDownloadReadyMessage());
  };

  renderDialog = () => {
    const { dialogType } = this.state;
    const onCancel = () => {
      this.setState({
        dialogType: ''
      });
    };
    const titleStyle = { textAlign: 'center', fontSize: getCurrentUser().getMediumFontSize() };
    const buttonStyle = {
      borderColor: '#c0c0c0',
      height: 40,
      width: 100,
      borderRadius: 20,
      borderWidth: 2,
      backgroundColor: '#ececec'
    };
    const contentStyle = {
      marginTop: 10,
      lineHeight: 20,
      color: '#333333',
      fontSize: getCurrentUser().getSmallFontSize()
    };
    const buttonTextStyle = {
      color: '#666666',
      fontSize: getCurrentUser().getMediumFontSize(),
      lineHeight: getCurrentUser().getMediumFontSize() + 4
    };
    return (
      <Dialog isVisible={!!dialogType} onBackdropPress={onCancel}>
        {dialogType === 'download' && (
          <>
            <Dialog.Title titleStyle={titleStyle} title={i18n2.t('LessonTableScreen.DownloadDialogTitle')} />
            <Text style={contentStyle}>{i18n2.t('LessonTableScreen.DownloadDialogMessage')}</Text>
          </>
        )}
        {dialogType === 'deleteDownload' && (
          <>
            <Dialog.Title titleStyle={titleStyle} title={i18n2.t('LessonTableScreen.DeleteDialogTitle')} />
            <Text style={contentStyle}>{i18n2.t('LessonTableScreen.DeleteDialogMessage')}</Text>
          </>
        )}
        <View style={{ justifyContent: 'center', alignItems: 'center', flexDirection: 'row', marginTop: 20, gap: 8 }}>
          <Button titleStyle={buttonTextStyle} buttonStyle={{ ...buttonStyle }} type='outline' onPress={onCancel}>
            {i18n2.t('Common.Cancel')}
          </Button>
          <Button
            buttonStyle={{ ...buttonStyle, borderColor: '#c0c0c0' }}
            titleStyle={{ ...buttonTextStyle, color: '#202020' }}
            type='outline'
            onPress={() => {
              onCancel();
              dialogType === 'download' ? this.download() : this.deleteDownload();
            }}>
            {i18n2.t('Common.Confirm')}
          </Button>
        </View>
      </Dialog>
    );
  };
  render() {
    if (!this.state.content || !this.state.htmlTemplate) {
      return <LoadingIndicator />;
    }

    const {
      showModal,
      showModalTitle,
      showModalItems,
      currentIndex,
      content: { title, progress, hideIndex, content, newMessageCount, options },
      htmlTemplate
    } = this.state;

    const html = updateHtmlFontSize(htmlTemplate);
    let index = 0;

    const lessonProgress = getCurrentUser().getProperty(`LessonProgress.${this.lesson.classId}`);
    const scrollTop = lessonProgress && lessonProgress.tocScrollTop ? lessonProgress.tocScrollTop : 0;

    const renderContent = {
      isTableOfContent: 1, // so that template.html know it's for table of content
      options,
      session: {
        title,
        content: (content || []).map((it) => {
          if (it.type === 'session') {
            const p = progress[index] || '';
            const count = newMessageCount[index] || 0;
            index++;
            return {
              ...it,
              hideIndex,
              progress: p,
              newMessageCount: count
            };
          }
          return it;
        })
      },
      sessionIndex: currentIndex,
      answers: {},
      canShare: false,
      position: { scrollTop },
      marginTop: 0,
      accessToken: getCurrentUser().getAccessToken(),
      contentUrl: getHttpsServer(`lesson/${this.lesson.name}/content/`),
      images: {},
      i18n: {
        NotAnswered: i18n2.t('NotAnswered'),
        Share: i18n2.t('Common.Share'),
        Unshare: i18n2.t('Unshare'),
        ShareClass: i18n2.t('ShareClass'),
        ShareClassDescription: this.getShareClassDescription(),
        Download: i18n2.t('LessonTableScreen.Download'),
        OnlineModeMessage: i18n2.tValue(
          'LessonTableScreen.OnlineModeMessage',
          convertFileSizeToString(this.lesson.size)
        ),
        CancelDownload: i18n2.t('LessonTableScreen.CancelDownload'),
        DownloadingMessage: i18n2.t('LessonTableScreen.DownloadingMessage'),
        DeleteDownload: i18n2.t('LessonTableScreen.DeleteDownload'),
        DownloadCompleteMessage: i18n2.tValue(
          'LessonTableScreen.DownloadCompleteMessage',
          convertFileSizeToString(this.lesson.size)
        )
      },
      downloadState: this.downloadState
    };
    return (
      <View
        style={{
          flex: 1,
          marginLeft: this.context.insets.left,
          marginRight: this.context.insets.right
        }}>
        <WebView
          ref={this.webViewRef}
          style={{ flex: 1 }}
          allowsLinkPreview={true}
          originWhitelist={['*']}
          source={{ html: `<script>content=${JSON.stringify(renderContent)}</script>${html}` }}
          scrollEnabled={true}
          allowUniversalAccessFromFileURLs={true}
          allowFileAccessFromFileURLs={true}
          allowFileAccess={true}
          showsVerticalScrollIndicator={true}
          mixedContentMode='always'
          bounces={false}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          allowsFullscreenVideo={true}
          onLoadEnd={() => {
            if (this.downloadState === 1) {
              const isDownloading = ClassDownloadUtil.checkIsDownloading(this.lesson.classId);
              isDownloading &&
                this.webViewRef &&
                this.webViewRef.current.postMessage(ClassDownloadUtil.getDownloadingMessage());
              // set download result listener
              this.lessonDownloadListener?.remove?.();
              this.lessonDownloadListener = globalThis.eventEmitter.addListener(
                'LessonDownload',
                ({ data, classId }) => {
                  if (classId === this.lesson.classId && this.webViewRef && this.webViewRef.current) {
                    const state = JSON.parse(data || '{}').state;
                    if (state === 'downloaded') {
                      this.downloadState = 2;
                    } else if (state === 'download') {
                      this.downloadState = 1;
                    }
                    this.webViewRef.current.postMessage(data);
                  }
                }
              );
            }
          }}
          onShouldStartLoadWithRequest={(event) => {
            if (event.url.toLowerCase().endsWith('.apk')) {
              // disallow download apk
              alert(i18n2.t('OpsNotAllowed'));
              return false;
            }

            return true;
          }}
          onMessage={async (event) => {
            const data = JSON.parse(event.nativeEvent.data);
            console.log(data);
            switch (data.command) {
              case 'onScroll':
                getCurrentUser().setPropertyAsync(`LessonProgress.${this.lesson.classId}`, {
                  tocScrollTop: data.scrollTop
                });
                break;
              case 'openLink':
                this.openLink(data);
                break;
              case 'goToLesson':
                this.goToLesson(data.title, data.lesson, title, data.sessionIndex);
                break;
              case 'share':
                this.share();
                break;
              case 'unshare':
                this.unshare();
                break;
              case 'download':
                this.setState({
                  dialogType: 'download'
                });
                break;
              case 'cancelDownload':
                this.cancelDownload();
                break;
              case 'deleteDownload':
                this.setState({
                  dialogType: 'deleteDownload'
                });
                break;
              default:
                break;
            }
          }}
        />
        <ModalPopup
          hideOnPress={true}
          insets={this.context.insets}
          visible={showModal}
          showDivider={true}
          setNotVisible={() => this.setState({ showModal: false })}
          title={showModalTitle}
          items={showModalItems}
          iconsPerRow={showModalItems.length}
          showCancel={true}
        />
        {this.renderDialog()}
      </View>
    );
  }

  openLink = (data) => {
    try {
      const url = data.url;
      if (data.openInOtherApp) {
        Linking.openURL(url);
      } else {
        WebBrowser.openBrowserAsync(url, {
          controlsColor: '#ffffff',
          toolbarColor: Colors.blue,
          showTitle: true,
          collapseToolbar: true
        });
      }
    } catch (error) {
      alert(error);
    }
  };
}
