import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  ScrollView,
  Modal,
  Keyboard,
  type NativeSyntheticEvent,
  type TextInputSubmitEditingEventData
} from 'react-native';
import { useNavigation, useRoute, ParamListBase, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';
import { getObjectAsync, setObjectAsync } from '@/dataStorage/localStorage';
import { ETranslation, getDefaultTranslation, searchBibleContent } from '@/utils/bibleUtils';
import { i18n2 } from '@/utils/i18n2';
import BibleCategorySelector, { getBookRangeByCategory } from '@/components/BibleCategorySelector';
import SearchBar from '@/components/SearchBar';

interface SearchResult {
  bookId: number;
  bookName: string;
  chapter: number;
  verse: number;
  verseId: number;
  content: string;
}

interface RouteParams {
  keyword?: string;
  bookId?: number;
  categoryId?: string;
  translation?: ETranslation;
}

type BibleSearchNavigationProp = NativeStackNavigationProp<ParamListBase, 'BibleSearch'>;
type BibleSearchRouteProp = RouteProp<{ BibleSearch: RouteParams }, 'BibleSearch'>;

const BibleSearchScreen: React.FC = () => {
  const navigation = useNavigation<BibleSearchNavigationProp>();
  const route = useRoute<BibleSearchRouteProp>();
  const { keyword: initialKeyword, bookId, categoryId, translation: routeTranslation } = route.params || {};
  console.log('routeTranslation', routeTranslation);

  const [keyword, setKeyword] = useState(initialKeyword || '');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [nextToken, setNextToken] = useState<string | null>(null);
  const [noResults, setNoResults] = useState(false);
  const [selectedTranslation, setSelectedTranslation] = useState<ETranslation>(
    routeTranslation || getDefaultTranslation()
  );
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // 分类选择相关状态
  const [activeCategory, setActiveCategory] = useState(categoryId || 'all');
  const [bookIdRange, setBookIdRange] = useState<[number, number]>([1, 66]);

  // 翻译版本选择相关状态
  const [translationModalVisible, setTranslationModalVisible] = useState(false);
  const [availableTranslations, setAvailableTranslations] = useState<ETranslation[]>([]);

  // Load selected translations from settings and search history
  useEffect(() => {
    const loadData = async () => {
      setAvailableTranslations(Object.values(ETranslation));

      // 加载搜索历史
      const history = await getObjectAsync('bible.search.history');
      console.log('history', history);
      if (history && history.history) {
        setSearchHistory(history.history);
      }
      // 如果有指定的 bookId，设置相应的范围
      if (bookId) {
        setBookIdRange([bookId, bookId]);
      }
      // 如果有指定的 categoryId，设置相应的范围
      else if (categoryId && categoryId !== 'all') {
        const range = getBookRangeByCategory(categoryId);
        if (range) {
          setBookIdRange(range);
        }
      }
    };

    loadData();
  }, [bookId, categoryId]);

  // Wrapper with useCallback to maintain reference stability
  const searchBible = useCallback<
    (
      searchKeyword: string,
      token?: string | null,
      reset?: boolean,
      translation?: ETranslation,
      range?: [number, number]
    ) => Promise<void>
  >(
    async (searchKeyword, token = null, reset = true, translation = selectedTranslation, range = bookIdRange) => {
      // Moved performBibleSearch inside useCallback to avoid dependency issues
      if (!searchKeyword.trim()) {
        if (reset) {
          setResults([]);
          setNoResults(false);
        }
        return;
      }

      setLoading(true);

      try {
        // 使用本地搜索函数而不是API
        const searchResult = await searchBibleContent(
          searchKeyword,
          translation, // 使用传入的翻译版本
          range, // 使用传入的书卷范围
          token,
          20 // 每页显示20条结果
        );

        if (searchResult) {
          const { results: searchResults, nextToken: newToken } = searchResult;

          if (reset) {
            setResults(searchResults);
          } else {
            setResults((prev) => [...prev, ...searchResults]);
          }

          setNextToken(newToken);
          setNoResults(searchResults.length === 0);
        } else {
          if (reset) {
            setResults([]);
          }
          setNoResults(true);
        }
      } catch (error) {
        console.error('Bible search error:', error);
        if (reset) {
          setResults([]);
        }
        setNoResults(true);
      } finally {
        setLoading(false);
      }
    },
    [selectedTranslation, bookIdRange]
  );

  // Initial search if keyword is provided
  useEffect(() => {
    if (initialKeyword) {
      // 如果有初始关键词，自动触发搜索
      searchBible(initialKeyword, null, true, selectedTranslation, bookIdRange);
    }
  }, [initialKeyword, searchBible, selectedTranslation, bookIdRange]);

  // 处理分类变化，更新书卷范围
  const handleCategoryChange = useCallback(
    (categoryId: string) => {
      setActiveCategory(categoryId);
      const range = getBookRangeByCategory(categoryId);
      if (range) {
        setBookIdRange(range);

        // 如果关键词不为空，自动搜索
        if (keyword.trim()) {
          searchBible(keyword, null, true, selectedTranslation, range);
        }
      }
    },
    [keyword, searchBible, selectedTranslation]
  );

  // 切换翻译版本
  const handleTranslationChange = useCallback(
    (translation: ETranslation) => {
      setSelectedTranslation(translation);
      setTranslationModalVisible(false);

      // 将选择的翻译版本保存到本地存储
      setObjectAsync('bible.search.translation', { translation });
      console.log('已保存翻译版本选择:', translation);

      // 切换版本后重新搜索
      if (keyword.trim()) {
        searchBible(keyword, null, true, translation, bookIdRange);
      }
    },
    [keyword, searchBible, bookIdRange]
  );

  // Save search term to history
  const saveToHistory = useCallback<(term: string) => Promise<void>>(
    async (term: string) => {
      if (!term.trim()) {
        return;
      }

      // Create a new history array with the current term at the beginning
      // and remove any duplicates
      const updatedHistory = [term, ...searchHistory.filter((item) => item !== term)];

      // Limit history to 10 items
      const limitedHistory = updatedHistory.slice(0, 10);

      // Update state and save to storage
      setSearchHistory(limitedHistory);
      await setObjectAsync('bible.search.history', { history: limitedHistory });
    },
    [searchHistory]
  );

  // Handle search button press
  const handleSearch = (event: NativeSyntheticEvent<TextInputSubmitEditingEventData>) => {
    const searchText = event?.nativeEvent?.text || keyword;
    if (searchText.trim()) {
      searchBible(searchText, null, true, selectedTranslation, bookIdRange);
      saveToHistory(searchText);
      Keyboard.dismiss();
    }
  };

  // Handle history item click
  const handleHistoryItemClick = (term: string): void => {
    setKeyword(term);
    searchBible(term, null, true, selectedTranslation, bookIdRange);
  };

  // Load more results
  const loadMore = (): void => {
    if (nextToken && !loading) {
      searchBible(keyword, nextToken, false, selectedTranslation, bookIdRange);
    }
  };

  // Navigate to chapter view
  const navigateToChapter = (result: SearchResult): void => {
    navigation.navigate('BibleChapter', {
      bookId: result.bookId,
      bookName: result.bookName,
      chapter: result.chapter,
      verse: result.verse
    });
  };

  // Highlight search term in content
  const highlightContent = (content: string, term: string): React.ReactNode => {
    if (!term.trim()) {
      return content;
    }

    const parts = content.split(new RegExp(`(${term})`, 'gi'));

    return parts.map((part, index) => {
      if (part.toLowerCase() === term.toLowerCase()) {
        return (
          <Text key={index} style={styles.highlight}>
            {part}
          </Text>
        );
      }
      return part;
    });
  };

  useSetNavigationOptions({
    title: bookId ? `搜索: ${bookId}卷` : i18n2.t('screens:BibleSearchScreen.Title')
  });

  return (
    <View style={styles.container}>
      {/* 搜索框和分类选择器 */}
      <View style={styles.searchHeader}>
        <View style={styles.searchContainer}>
          <SearchBar
            autoFocus={!initialKeyword}
            value={keyword}
            isCloseVisible={isSearching}
            onChangeText={setKeyword}
            onFocus={() => setIsSearching(true)}
            onClose={() => {
              Keyboard.dismiss();
              setKeyword('');
              setIsSearching(false);
              setResults([]);
              setNoResults(false);
            }}
            onSubmitEditing={handleSearch}
          />
        </View>

        {/* 翻译版本和分类选择器 */}
        <View style={styles.filterContainer}>
          {/* 翻译版本切换按钮 - 放在左侧 */}
          <TouchableOpacity style={styles.versionSelectorButton} onPress={() => setTranslationModalVisible(true)}>
            <Text style={styles.versionSelectorText}>
              {selectedTranslation === ETranslation.cuvs
                ? '和合本'
                : selectedTranslation === ETranslation.cuvt
                  ? '和合本(繁)'
                  : selectedTranslation === ETranslation.asv
                    ? 'ASV'
                    : selectedTranslation}
            </Text>
            <Ionicons name='chevron-down' size={16} color='#666' />
          </TouchableOpacity>

          {/* 分类选择器 */}
          <View style={styles.categoryContainer}>
            <BibleCategorySelector
              activeCategory={activeCategory}
              onCategoryChange={handleCategoryChange}
              containerStyle={styles.categorySelector}
            />
          </View>
        </View>
      </View>

      {/* 搜索历史 - 只在关键词为空时显示 */}
      {searchHistory.length > 0 && !keyword.trim() && (
        <View style={styles.historyContainer}>
          <Text style={styles.historyTitle}>{i18n2.t('screens:BibleSearchScreen.SearchHistory')}</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.historyScrollContent}>
            {searchHistory.map((term, index) => (
              <TouchableOpacity
                key={`${term}-${index}`}
                style={styles.historyItem}
                onPress={() => handleHistoryItemClick(term)}>
                <Ionicons name='time-outline' size={14} color='#666' style={styles.historyIcon} />
                <Text style={styles.historyText} numberOfLines={1}>
                  {term}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* 翻译版本选择模态框 */}
      <Modal
        visible={translationModalVisible}
        transparent={true}
        animationType='fade'
        onRequestClose={() => setTranslationModalVisible(false)}>
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setTranslationModalVisible(false)}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{i18n2.t('screens:BibleSearchScreen.SelectTranslation')}</Text>
            {availableTranslations.map((translation) => (
              <TouchableOpacity
                key={translation}
                style={[styles.translationItem, selectedTranslation === translation && styles.selectedTranslationItem]}
                onPress={() => handleTranslationChange(translation)}>
                <Text
                  style={[
                    styles.translationItemText,
                    selectedTranslation === translation && styles.selectedTranslationItemText
                  ]}>
                  {translation === ETranslation.cuvs
                    ? '和合本（简体）'
                    : translation === ETranslation.cuvt
                      ? '和合本（繁体）'
                      : translation === ETranslation.asv
                        ? 'ASV'
                        : translation}
                </Text>
                {selectedTranslation === translation && <Ionicons name='checkmark' size={20} color='#4CAF50' />}
              </TouchableOpacity>
            ))}
            <TouchableOpacity style={styles.closeButton} onPress={() => setTranslationModalVisible(false)}>
              <Text style={styles.closeButtonText}>{i18n2.t('screens:BibleSearchScreen.Cancel')}</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>

      {loading && results.length === 0 && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color='#333' />
          <Text style={styles.loadingText}>{i18n2.t('screens:BibleSearchScreen.Searching')}</Text>
        </View>
      )}

      {noResults && !loading && (
        <View style={styles.noResultsContainer}>
          <Ionicons name='search-outline' size={50} color='#ccc' />
          <Text style={styles.noResultsText}>{i18n2.t('screens:BibleSearchScreen.NoResults')}</Text>
        </View>
      )}

      <FlatList
        data={results}
        keyExtractor={(item: SearchResult) => item.verseId.toString()}
        renderItem={({ item }: { item: SearchResult }) => (
          <TouchableOpacity style={styles.resultItem} onPress={() => navigateToChapter(item)}>
            <Text style={styles.resultReference}>{`${item.bookName} ${item.chapter}:${item.verse}`}</Text>
            <Text style={styles.resultContent}>{highlightContent(item.content, keyword)}</Text>
          </TouchableOpacity>
        )}
        contentContainerStyle={styles.resultsList}
        onEndReached={loadMore}
        onEndReachedThreshold={0.3}
        ListFooterComponent={
          loading && results.length > 0 ? (
            <View style={styles.footerLoading}>
              <ActivityIndicator size='small' color='#333' />
              <Text style={styles.footerLoadingText}>{i18n2.t('screens:BibleSearchScreen.LoadMore')}</Text>
            </View>
          ) : null
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff'
  },
  searchHeader: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0'
  },
  searchContainer: {
    paddingTop: 15,
    paddingBottom: 10
  },
  filterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 5,
    paddingHorizontal: 15
  },
  versionSelectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginRight: 10,
    minWidth: 80
  },
  versionSelectorText: {
    fontSize: 13,
    fontWeight: '500',
    color: '#333',
    marginRight: 5
  },
  categoryContainer: {
    flex: 1,
    paddingBottom: 5
  },
  categorySelector: {
    height: 50,
    paddingHorizontal: 0
  },
  // Search history styles
  historyContainer: {
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0'
  },
  historyTitle: {
    fontSize: 14,
    color: '#666',
    marginLeft: 15,
    marginBottom: 8
  },
  historyScrollContent: {
    paddingHorizontal: 10
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginHorizontal: 5,
    borderRadius: 16,
    maxWidth: 150
  },
  historyIcon: {
    marginRight: 5
  },
  historyText: {
    fontSize: 14,
    color: '#333'
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalContent: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center'
  },
  translationItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0'
  },
  selectedTranslationItem: {
    backgroundColor: '#f9f9f9'
  },
  translationItemText: {
    fontSize: 16,
    color: '#333'
  },
  selectedTranslationItemText: {
    fontWeight: 'bold',
    color: '#4CAF50'
  },
  closeButton: {
    marginTop: 15,
    paddingVertical: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 5,
    alignItems: 'center'
  },
  closeButtonText: {
    fontSize: 16,
    color: '#333'
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666'
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  noResultsText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666'
  },
  resultsList: {
    padding: 15
  },
  resultItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0'
  },
  resultReference: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#333'
  },
  resultContent: {
    fontSize: 16,
    lineHeight: 24,
    color: '#666'
  },
  highlight: {
    backgroundColor: '#FFEB3B',
    fontWeight: 'bold'
  },
  footerLoading: {
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row'
  },
  footerLoadingText: {
    marginLeft: 10,
    fontSize: 14,
    color: '#666'
  }
});

export default BibleSearchScreen;
