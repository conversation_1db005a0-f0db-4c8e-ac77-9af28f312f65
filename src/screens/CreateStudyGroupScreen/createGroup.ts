import { Id, InviteUser } from '@/components/MemberInvite/MemberInvite';

import { Alert } from 'react-native';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';

function iDigestMergeMembersToAdd(iDigestUserEnteredMembers: string, inviteUsers: InviteUser[]) {
  const members: { [index: string]: number } = {};
  for (const id of iDigestUserEnteredMembers.split(',')) {
    if (id.trim().length > 0) {
      members[id.trim().toLowerCase()] = 1;
    }
  }
  for (const user of inviteUsers) {
    if (user.isSelected) {
      members[user.id.trim().toLowerCase()] = 1;
    }
  }

  return members;
}

export function fmMergeMembersToAdd(fmUserEnteredMembers: Id[], inviteUsers: InviteUser[]) {
  const members: { [index: Id]: number } = {};
  for (const id of fmUserEnteredMembers) {
    members[id] = 1;
  }
  for (const user of inviteUsers) {
    if (user.isSelected) {
      members[user.id.trim().toLowerCase()] = 1;
    }
  }

  return members;
}

export async function createGroup(
  groupName: string,
  iDigestUserEnteredMembers: string,
  fmUserEnteredMembers: Id[],
  inviteUsers: InviteUser[],
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  navigation: any,
  classId: number,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  lesson: any
) {
  groupName = groupName.trim();

  // merge user entered members and selected members
  const members = isFishMeet
    ? fmMergeMembersToAdd(fmUserEnteredMembers, inviteUsers)
    : iDigestMergeMembersToAdd(iDigestUserEnteredMembers, inviteUsers);

  if (groupName === '' && Object.keys(members).length === 0) {
    Alert.alert(i18n2.t('CreateStudyGroupScreen.NoGroupName'));
    return;
  }

  const body = {
    classId,
    groupName,
    members: Object.keys(members)
  };

  const result = await globalThis.dsObject.createGroup(classId, body);
  if (!result) {
    return;
  }

  const { response } = result.body;
  if (!response) {
    Alert.alert(i18n2.t('CreateStudyGroupScreen.CreateGroupFailed'));
    return;
  }

  // notify groupCreated
  globalThis.eventEmitter.emit('groupCreated', {
    name: response.studyGroupName,
    groupId: response.studyGroupId,
    classId: response.classId,
    isGroupLeader: true
  });

  // if succeed, navigate to group list screen.
  navigation.navigate('Group', {
    studyGroupName: response.studyGroupName,
    group: {
      name: response.studyGroupName,
      groupId: response.studyGroupId,
      classId: response.classId,
      isGroupLeader: true
    },
    isOrgGroup: false,
    fromCreateGroupScreen: true,
    lesson
  });
}
