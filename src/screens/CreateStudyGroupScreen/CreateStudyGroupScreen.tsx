/* eslint-disable react-native/no-unused-styles */

import { Keyboard, Text, TextInput, View, useWindowDimensions } from 'react-native';
import React, { useCallback, useContext, useState } from 'react';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import { FmStyles } from '@/fishMeet/styles/fmStyles';
import { InputField } from '@/fishMeet/components/InputField';
import { MemberInvite } from '@/components/MemberInvite/MemberInvite';
import { OkButton } from '@/components/navigation/NavigationButtons';
import { StyleSheet } from 'react-native';
import { createGroup } from '@/screens/CreateStudyGroupScreen/createGroup';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { logEvent } from '@/utils/logger';
import { useFocusEffect } from '@react-navigation/native';
import { useMemberInviteState } from '@/components/MemberInvite/useMemberInviteState';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';

type CreateStudyGroupParams = {
  classId: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  lesson: any;
};

type AppContextType = {
  insets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function CreateStudyGroupScreen({ route, navigation }: any) {
  const { classId, lesson }: CreateStudyGroupParams = route.params;

  const { insets } = useContext(AppContext) as AppContextType;

  const {
    iDigestUserEnteredMembers,
    iDigestSetUserEnteredMembers,
    fmUserEnteredMembers,
    setFmUserEnteredMembers,
    inviteUsers,
    setInviteUsers
  } = useMemberInviteState();

  const [groupName, setGroupName] = useState('');
  const [showSave, setShowSave] = useState<boolean>(true);

  useSetNavigationOptions({
    title: i18n2.t('CreateStudyGroupScreen.Title'),
    headerRight: () =>
      showSave ? (
        <OkButton
          right={true}
          onPress={async () => {
            setShowSave(false);
            await createGroup(
              groupName,
              iDigestUserEnteredMembers,
              fmUserEnteredMembers,
              inviteUsers,
              navigation,
              classId,
              lesson
            );
            setShowSave(true);
          }}
        />
      ) : null
  });

  const loadInviteUsers = useCallback(() => {
    async function getInviteUsers() {
      const inviteUsers = await globalThis.dsObject.getInviteUsers(getCurrentUser().getUserId());
      if (!inviteUsers) {
        navigation.pop();
        return;
      }

      setInviteUsers(inviteUsers);
    }

    logEvent('createStudyGroup');
    getInviteUsers();
  }, [navigation, setInviteUsers]);

  useFocusEffect(loadInviteUsers);

  const { width } = useWindowDimensions();

  function iDigestRenderHeader() {
    const styles = getCreateStudyGroupScreenStyles(width, insets);

    return (
      <>
        <Text style={styles.groupInputLabel}>{i18n2.t('GroupsScreen.GroupName')}</Text>
        <TextInput style={styles.groupInput} textAlign={'left'} onChangeText={setGroupName} />
        <Text style={styles.memberInputLabel}>{i18n2.t('CreateStudyGroupScreen.AddMembers')}</Text>
      </>
    );
  }

  function fmRenderHeader() {
    return (
      <>
        <InputField
          label={i18n2.t('GroupsScreen.GroupName')}
          value={groupName}
          onChangeText={setGroupName}
          onSubmitEditing={() => Keyboard.dismiss()}
        />
        <View style={FmStyles.sectionSpacer} />
      </>
    );
  }

  return isFishMeet ? (
    <MemberInvite
      isFishMeet={isFishMeet}
      header={fmRenderHeader()}
      inviteUsers={inviteUsers}
      userEnteredMembers={fmUserEnteredMembers}
      setInviteUsers={setInviteUsers}
      setUserEnteredMembers={setFmUserEnteredMembers}
    />
  ) : (
    <MemberInvite
      isFishMeet={isFishMeet}
      header={iDigestRenderHeader()}
      inviteUsers={inviteUsers}
      userEnteredMembers={iDigestUserEnteredMembers}
      setInviteUsers={setInviteUsers}
      setUserEnteredMembers={iDigestSetUserEnteredMembers}
    />
  );
}

export default CreateStudyGroupScreen;

function getCreateStudyGroupScreenStyles(width: number, insets: AppContextType['insets']) {
  return StyleSheet.create({
    groupInputLabel: {
      fontSize: getCurrentUser().getMediumFontSize(),
      color: Colors.darkBlue,
      textAlign: 'center',
      marginTop: 30
    },
    groupInput: {
      borderColor: '#cccccc',
      width: width - insets.left - insets.right - 50,
      fontSize: getCurrentUser().getSmallFontSize(),
      marginHorizontal: 7,
      padding: 7,
      borderWidth: 1,
      textAlignVertical: 'top'
    },
    memberInputLabel: {
      fontSize: getCurrentUser().getMediumFontSize(),
      color: Colors.darkBlue,
      textAlign: 'center',
      marginTop: 20
    }
  });
}
