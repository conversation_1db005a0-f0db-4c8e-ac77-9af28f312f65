import '@/global';
import 'react-native-url-polyfill/auto';

import * as Device from 'expo-device';
import * as Font from 'expo-font';
import * as Icon from '@expo/vector-icons';
import * as ScreenOrientation from 'expo-screen-orientation';
import * as Sentry from '@sentry/react-native';
import * as SplashScreen from 'expo-splash-screen';
import * as Updates from 'expo-updates';

import { Alert, LogBox, StyleSheet } from 'react-native';
import React, { useCallback, useEffect, useState } from 'react';
import { SafeAreaInsetsContext, SafeAreaProvider } from 'react-native-safe-area-context';
import { getCurrentUser, getHttpsServer } from '@/utils/user';

import { ActionSheetProvider } from '@expo/react-native-action-sheet';
import { AppContextProvider } from '@/context/AppContext';
import { AppNavigator } from '@/navigation/AppNavigator';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AudioPlayer from '@/components/AudioPlayer';
import FlashMessage from 'react-native-flash-message';
import FloatingJitsiMeeting from '@/components/FloatingJitsiMeeting';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Loading } from '@/components/EasyLoading';
import { StatusBar } from 'expo-status-bar';
import { checkToShowGoogleSignin } from '@/utils/GoogleSignInHelper';
import { getHttpHeaders } from '@/dataStorage/storage';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { logEvent } from '@/utils/logger';
import { registerRootComponent } from 'expo';

SplashScreen.setOptions({ duration: 100 });
// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);

Sentry.init({
  dsn: isFishMeet
    ? 'https://<EMAIL>/4508204258885632'
    : 'https://<EMAIL>/4508185446514688',
  debug: false // If `true`, Sentry will try to print out useful debugging information if something goes wrong with sending the event. Set it to `false` in production
});

// Ignore known warnings
LogBox.ignoreLogs([
  /^Non-serializable values were found in the navigation state.*/s,
  /^Warning: TextElement: Support for defaultProps will be removed from function components.*/s
]);

// Set up global error handler
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const myErrorHandler = (error: any) => {
  Sentry.captureException(error);
  const message = JSON.stringify(error, Object.getOwnPropertyNames(error));
  Alert.alert(
    'Oops, we got a problem!',
    `Sorry for the inconvenience!\n\nPlease restart the app to see if it works, if not, please try again later with Internet connection on\n\n${message}`,
    [
      {
        text: 'Ok',
        onPress: () => {
          if (!Device.isDevice || __DEV__) {
            return;
          }

          setTimeout(() => {
            Updates.reloadAsync();
          }, 100);
        }
      }
    ]
  );

  if (!Device.isDevice || __DEV__) {
    // don't log for emulator or dev mode
    console.log(message);
    return;
  }

  fetch(getHttpsServer(`crash/${message}`), { method: 'GET', headers: getHttpHeaders() });
};

ErrorUtils.setGlobalHandler(myErrorHandler);

// Overwrite console.log
// NOTE: we need to call getCurrentUser() before calling logEvent, otherwise it will be infinite loop
getCurrentUser().logUserInfo();
const _consoleLog = console.log;
console.log = (...args) => {
  logEvent(...args);
  _consoleLog(...args);
};

async function getAsyncStorage() {
  const persistedAppInfo = await AsyncStorage.getItem('persistedAppInfo');
  if (persistedAppInfo) {
    console.log(persistedAppInfo);
    const parsedAppInfo = JSON.parse(persistedAppInfo);
    globalThis.dsObject.setLastHttpErrorTime(parsedAppInfo.lastHttpErrorTime);
  }
}

function App() {
  const [appIsReady, setAppIsReady] = useState(false);

  globalThis.eventEmitter.addListener('appInForeground', async () => {
    await getAsyncStorage();
  });

  globalThis.eventEmitter.addListener('appInBackground', async () => {
    await AsyncStorage.setItem(
      'persistedAppInfo',
      JSON.stringify({
        lastHttpErrorTime: globalThis.dsObject.lastHttpErrorTime
      })
    );
  });

  useEffect(() => {
    async function prepare() {
      try {
        try {
          await getAsyncStorage();
        } catch {
          console.error('failed to load async storage');
        }
        try {
          await getCurrentUser().loadExistingUserAsync();
        } catch {
          console.log('no user');
        }
        // console.log('User loaded');

        // set i18n language after user loaded
        i18n2.changeLanguage(getCurrentUser().getLanguage());

        // Load icons now to prevent showing invisible icons on first app load
        const icons = {
          ...Icon.Entypo.font,
          ...Icon.FontAwesome.font,
          ...Icon.MaterialCommunityIcons.font,
          ...Icon.AntDesign.font,
          ...Icon.Ionicons.font
        };
        const fmFonts = {
          'AlbertSans-Medium': require('@/fishMeet/assets/fonts/AlbertSans-Medium.otf'),
          'AlbertSans-Bold': require('@/fishMeet/assets/fonts/AlbertSans-Bold.otf')
        };
        try {
          await Font.loadAsync(isFishMeet ? { ...icons, ...fmFonts } : icons);
        } catch {
          console.log('no fonts');
        }
        console.log('Fonts loaded');

        try {
          await checkToShowGoogleSignin();
        } catch {
          console.log('failed to detect if we should show Google signin or not.');
        }
      } catch (e) {
        console.warn(e);
      } finally {
        setAppIsReady(true);
      }
    }

    prepare();
  }, []);

  const onReadyAppNavigator = useCallback(() => {
    if (appIsReady) {
      // Hide splash screen after app navigator has mounted to prevent showing empty screen
      SplashScreen.hide();
    }
  }, [appIsReady]);

  if (!appIsReady) {
    return null;
  }

  return (
    <>
      <ActionSheetProvider>
        <SafeAreaProvider>
          <SafeAreaInsetsContext.Consumer>
            {(insets) => {
              const statusBarHeight = insets?.top ?? 0;

              return (
                <AppContextProvider insets={insets}>
                  <GestureHandlerRootView>
                    <StatusBar translucent={true} style='dark' />
                    <AppNavigator onReady={onReadyAppNavigator} />
                    <AudioPlayer />
                    <FloatingJitsiMeeting />

                    {/* Keep on bottom so FlashMessage displays over all other components */}
                    <FlashMessage
                      position='top'
                      statusBarHeight={statusBarHeight}
                      titleStyle={styles.flashMessageTitleText}
                      textStyle={styles.flashMessageDescriptionText}
                    />
                  </GestureHandlerRootView>
                </AppContextProvider>
              );
            }}
          </SafeAreaInsetsContext.Consumer>
        </SafeAreaProvider>
      </ActionSheetProvider>
      <Loading key={'singleton'} loadingText={i18n2.t('App.Connecting')} />
    </>
  );
}

registerRootComponent(Sentry.wrap(App));

const styles = StyleSheet.create({
  flashMessageTitleText: {
    fontFamily: isFishMeet ? 'AlbertSans-Medium' : undefined,
    fontSize: getCurrentUser().getMediumFontSize(),
    lineHeight: getCurrentUser().getMediumFontSize() + 3
  },
  flashMessageDescriptionText: {
    fontFamily: isFishMeet ? 'AlbertSans-Medium' : undefined,
    fontSize: getCurrentUser().getSmallFontSize(),
    lineHeight: getCurrentUser().getSmallFontSize() + 3
  }
});
