import { GoogleSignin } from '@/utils/GoogleSignInHelper';
import { getCurrentUser } from '@/utils/user';
import { googleLoginUser } from '@/dataStorage/storage';

export function getMediumFontSize(): number {
  return getCurrentUser().getMediumFontSize();
}

export function getLanguage(): string {
  return getCurrentUser().getLanguage();
}

export async function setLanguage(language: string): Promise<void> {
  await getCurrentUser().setLanguageAsync(language);
}

export async function loginWithGoogle(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    await GoogleSignin.hasPlayServices();
    const userInfo = await GoogleSignin.signIn();
    console.log('User info is: ', userInfo);

    if (!userInfo.idToken) {
      return { success: false, error: 'NoAccessToken' };
    }

    const idToken = userInfo.idToken;
    const response = await googleLoginUser(idToken);

    if (!response || !response.accessToken) {
      console.log('Response failed, no accessToken found!');
      return { success: false, error: 'LoginFailed' };
    }

    await getCurrentUser().setUserInfoAsync({
      accessToken: response.accessToken,
      nickname: response.displayName
    });
    await getCurrentUser().loginAsync(true); // thirdPartyLogin = true
    return { success: true };
  } catch (error) {
    console.error('Google Sign-In error:', error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

export async function loginUser(
  loginId: string,
  password: string
): Promise<{ success: boolean; resetRequired?: boolean; error?: string }> {
  try {
    const result = await globalThis.dsObject.loginUser(loginId, password);

    if (!result) {
      return { success: false, error: 'LoginFailed' };
    }

    if (!result.body.accessToken) {
      return { success: false, error: 'NoAccessToken' };
    }

    await getCurrentUser().setUserInfoAsync({
      accessToken: result.body.accessToken,
      nickname: result.body.displayName
    });
    await getCurrentUser().loginAsync();

    return { success: true, resetRequired: !!result.body.reset };
  } catch (error) {
    console.error('Login error:', error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

export * as UserLoginModel from '@/models/UserLoginModel';
