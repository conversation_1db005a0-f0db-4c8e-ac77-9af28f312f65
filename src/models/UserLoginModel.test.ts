import { TYPES } from '@/ioc/types';
import User from '@/utils/user';
import { UserLoginModel } from '@/models/UserLoginModel';
import { dataServices } from '@/ioc/interfaces';
import { iDigestContainer } from '@/ioc/inversify.config';
import { mockDS } from '@/dataStorage/dataServicesMock';

jest.mock('@react-native-google-signin/google-signin', () => ({
  GoogleSignin: {
    configure: jest.fn()
  }
}));

beforeAll(async () => {
  // rebind the data service to the mock object
  iDigestContainer.snapshot();
  iDigestContainer.rebind<dataServices>(TYPES.dataServices).to(mockDS).inSingletonScope;
  globalThis.dsObject = iDigestContainer.get<dataServices>(TYPES.dataServices);
});

afterAll(async () => {
  // restore the real data service object
  iDigestContainer.restore();
  globalThis.dsObject = iDigestContainer.get<dataServices>(TYPES.dataServices);
});

describe('Login user using an email and password', () => {
  describe('no accesstoken', () => {
    test('should return success:false with non access token error', async () => {
      (globalThis.dsObject.loginUser as jest.Mock).mockResolvedValue({
        headers: 'headers',
        body: 'body',
        status: 200
      });

      const response = await UserLoginModel.loginUser('<EMAIL>', 'password');
      const { success, error } = response;
      expect(success).toBe(false);
      expect(error).toBe('NoAccessToken');
    });
  });

  describe('login successful', () => {
    test('should return success:true', async () => {
      (globalThis.dsObject.loginUser as jest.Mock).mockResolvedValue({
        headers: 'headers',
        body: {
          accessToken:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjI3NzQyLCJsb2dpbklkIjoidGVzdDg5Mzc0MDBAZ21haWwuY29tIiwiZGlzcGxheU5hbWUiOiLosLfmrYzmtYvor5UiLCJpYXQiOjE3MTIwOTgzODIsImV4cCI6MTc0MzYzNDM4Mn0._w8H2Ao7jAcYeYD03DVz9HXi5_SMhwRzV2KutRJOjNw',
          displayName: 'John Doe'
        },
        status: 200
      });

      jest.spyOn(User.prototype, 'loginAsync').mockResolvedValue(true);

      const response = await UserLoginModel.loginUser('<EMAIL>', 'password');
      expect(response.success).toBe(true);
    });
  });
});
