import { getCurrentUser } from '@/utils/user';

export function isCurrentUser(userId: number): boolean {
  return `${getCurrentUser().getUserId()}` === `${userId}`;
}

export function getMediumFontSize(): number {
  return getCurrentUser().getMediumFontSize();
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function initiateChat(friendId: string): Promise<{ chatId: string; title: string; group: any } | null> {
  const result = await globalThis.dsObject.createOneOnOne(friendId);
  if (result) {
    return {
      chatId: result.body.studyGroupId,
      title: result.body.studyGroupName,
      group: {
        groupId: result.body.studyGroupId,
        isOneOnOneGroup: true,
        isGroupLeader: false,
        friendId: friendId
      }
    };
  }
  return null;
}

export async function setTag(userId: number, tag: string): Promise<boolean> {
  try {
    const currentStatus = await fetchTagAndBlock(userId);
    const response = await globalThis.dsObject.setUserTagOrBlock(userId, tag, currentStatus.block);
    if (response?.status === 200) {
      console.log('Set user tag successed');
      return true;
    } else {
      console.error('Failed to set user tag:', response);
      return false;
    }
  } catch (error) {
    console.error('Error during set tag:', error);
    return false;
  }
}

export async function fetchTagAndBlock(userId: number) {
  return await globalThis.dsObject.fetchUserTagAndBlock(userId);
}

export async function setBlock(userId: number, blockStatus: boolean): Promise<boolean> {
  try {
    const currentStatus = await fetchTagAndBlock(userId);
    const response = await globalThis.dsObject.setUserTagOrBlock(userId, currentStatus.tag, blockStatus ? 1 : 0);
    if (response?.status === 200) {
      console.log('Block status updated successfully');
      return true;
    } else {
      console.error('Failed to update block status:', response);
      return false;
    }
  } catch (error) {
    console.error('Error during set block:', error);
    return false;
  }
}

export * as MemberModel from '@/models/MemberModel';
