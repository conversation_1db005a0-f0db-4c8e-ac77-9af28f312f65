{"data": "<html>\n\n<head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0\" />\n    <style>\n        body {\n            margin: 0px;\n            -webkit-hyphens: auto;\n            -ms-hyphens: auto;\n            hyphens: auto;\n            scrollbar-width: thin;\n            overflow-x: hidden;\n            font-family: -apple-system, Segoe UI, Helvetica, Arial, sans-serif;\n            -webkit-text-size-adjust: none;\n            text-size-adjust: none;\n            color: #505050;\n            user-select: none;\n            -webkit-user-select: none;\n        }\n\n        textarea {\n            width: 100%;\n            overflow: hidden;\n            font-size: 18px;\n        }\n\n        input {\n            width: calc(2em);\n            height: calc(2em);\n        }\n\n        a {\n            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n        }\n\n        .section {\n            margin: 8px;\n            font-size: 18px;\n            line-height: 1.85em;\n            margin-top: 10px;\n            max-width: 100%;\n            box-sizing: border-box;\n            width: calc(100% - 16px);\n            -webkit-hyphens: auto;\n            -ms-hyphens: auto;\n            hyphens: auto;\n        }\n\n        .title {\n            padding-top: 10px;\n            font-weight: bold;\n            font-size: 18px;\n            text-align: center;\n        }\n\n        .subtitle {\n            font-size: 18px;\n            font-weight: bold;\n        }\n\n        .text {\n            font-size: 18px;\n            letter-spacing: 0.05em;\n        }\n\n        .bible {\n            margin: 1px 2px 1px 2px;\n            padding: 0px 8px 0px 8px;\n            color: white;\n            background-color: black;\n            display: inline-block;\n            border-radius: 30px;\n        }\n\n        .link {\n            padding-left: 40px;\n            padding-right: 40px;\n            padding-top: 3px;\n            padding-bottom: 3px;\n            color: #202020;\n            background-color: #ececec;\n            border-radius: 32px;\n            font-size: 18px;\n            border: 2px solid #c0c0c0;\n        }\n\n        .answer {\n            color: #202020;\n            background-color: #ecf0f1;\n            overflow-y: hidden;\n            border-radius: 7px;\n            border: 1px solid #626262;\n            padding: 7px;\n        }\n\n        .slider {\n            width: calc(100% - 12px);\n            height: 4px;\n            background: #d3d3d3;\n            outline: none;\n            opacity: 0.7;\n            -webkit-appearance: none;\n        }\n\n        .slider::-webkit-slider-thumb {\n            width: 35px;\n            height: 35px;\n            border-radius: 35px;\n            background: #626262;\n            -webkit-appearance: none;\n        }\n\n        .discussion {\n            font-size: 16px;\n            color: #202020;\n            background-color: #ececec;\n            margin: 2px;\n            margin-right: 0.5em;\n            padding: 0px 8px 0px 8px;\n            display: inline-block;\n            border-radius: 30px;\n            border: 1px solid #c0c0c0;\n        }\n\n        .redDot {\n            width: 10px;\n            height: 10px;\n            background-color: red;\n            border: 1px solid white;\n            position: absolute;\n            border-radius: 8px;\n        }\n\n        .inlineQuestion {\n            font-size: 18px;\n            border-width: 0px;\n            border-bottom-width: 1px;\n            margin-left: 0.5em;\n            margin-right: 0.5em;\n            width: 6em;\n            height: 1.5em;\n            text-align: center;\n            border-radius: 0px;\n        }\n    </style>\n    <script>\n        function sendCmd(cmd, json) {\n            if (event) {\n                event.stopPropagation();\n            }\n            var payload = JSON.parse(json || '{}');\n            if (Array.isArray(payload)) {\n                alert('Invalid sendCmd ' + cmd);\n                return;\n            }\n            payload.command = cmd;\n            payload.scrollTop = document.body.scrollTop;\n            if (window.ReactNativeWebView) {\n                window.ReactNativeWebView.postMessage(JSON.stringify(payload));\n            } else {\n                console.log(payload);\n            }\n        }\n\n        function onAnswerChange(tx) {\n            tx.style.height = 'auto';\n            tx.style.height = tx.scrollHeight + 'px';\n            setAnswer(tx.id, tx.value);\n        }\n\n        function resizeTextAreas() {\n            var tx = document.getElementsByTagName('textarea');\n            for (var i = 0; i < tx.length; i++) {\n                var item = tx[i];\n                item.style.height = 'auto';\n                item.style.height = `${item.scrollHeight}px`;\n            }\n        }\n\n        function onTextAreaBlur(tx) {\n            tx.style.color = '#202020';\n            tx.style.backgroundColor = '#ecf0f1';\n        }\n\n        function onTextAreaFocus(tx) {\n            tx.style.color = '#004cb0';\n            tx.style.backgroundColor = 'white';\n        }\n\n        function getAccessUrl(src, isLocal) {\n            if (/^https:\\/\\/[-.\\w]+.idigest.app/.test(src)) {\n                return `${src}?token=${content.accessToken}`;\n            } else if (src.toLowerCase().startsWith('http://') || src.toLowerCase().startsWith('https://') || isLocal) {\n                return src;\n            }\n\n            return encodeURI(`${content.contentUrl}${src}?token=${content.accessToken}`);\n        }\n\n        function setAnswer(id, value) {\n            sendCmd('setAnswer', JSON.stringify({ id, value }));\n            if (value === undefined || value === null || value === '') {\n                delete content.answers[id];\n            } else {\n                content.answers[id] = value;\n            }\n\n            // update js content if needed\n            var nodes = document.getElementsByClassName('js');\n            if (nodes.length > 0) {\n                if (evalJs) {\n                    eval(evalJs);\n                }\n                var index = 0;\n                for (var i = 0; i < content.session.content.length; i++) {\n                    var item = content.session.content[i];\n                    if (item.type === 'js') {\n                        nodes[index++].innerHTML = eval(item.value);\n                    }\n                }\n            }\n        }\n\n        function createTextNode(textNode, text, className) {\n            var node = document.createElement(textNode);\n            node.setAttribute('class', className);\n            node.innerHTML = text.replace(/\\n/g, '<br>');\n            return node;\n        }\n\n        function createInputNode(id, className, rows, value) {\n            var tx = document.createElement('textarea');\n            tx.setAttribute('id', id);\n            tx.setAttribute('class', `${className} noClick`);\n            tx.setAttribute('rows', rows);\n            tx.setAttribute('oninput', 'onAnswerChange(this)');\n            tx.setAttribute('onfocus', 'onTextAreaFocus(this)');\n            tx.setAttribute('onblur', 'onTextAreaBlur(this)');\n            tx.value = value;\n            return tx;\n        }\n\n        function createShareNode(id) {\n            var div = document.createElement('div');\n            div.style = 'display: flex; justify-content: flex-end; margin-top: 3px';\n\n            if (content.discussions) {\n                var btn = document.createElement('button');\n                var count = 0;\n                var hasNewMessage = false;\n                var discussion = content.discussions[id];\n                if (discussion) {\n                    count = discussion.count;\n                    hasNewMessage = discussion.hasNewMessage;\n                }\n\n                btn.innerHTML = content.i18n.TotalShare.replace('{count}', count);\n                btn.setAttribute('questionId', id);\n                btn.setAttribute('class', 'discussion');\n                btn.setAttribute('onclick', `sendCmd('goToDiscussion', '${JSON.stringify({ questionId: id })}')`);\n\n                var div2 = document.createElement('div');\n                div2.style = 'display: flex; justify-content: flex-end;';\n\n                div2.appendChild(btn);\n                if (hasNewMessage) {\n                    var redDot = document.createElement('div');\n                    redDot.setAttribute('class', 'redDot');\n                    div2.appendChild(redDot);\n                }\n\n                div.appendChild(div2);\n            }\n\n            var shareBtn = document.createElement('button');\n            shareBtn.innerHTML = content.i18n.Share;\n            shareBtn.setAttribute('class', 'discussion');\n            shareBtn.setAttribute('onclick', `sendCmd('shareAnswer', '${JSON.stringify({ questionId: id })}')`);\n            div.appendChild(shareBtn);\n            return div;\n        }\n\n        function createLinkNode(linkNode, text, onClick, className) {\n            var node = document.createElement(linkNode);\n            node.setAttribute('class', `${className} noClick`);\n            node.setAttribute('onclick', onClick);\n            node.innerHTML = text;\n            return node;\n        }\n\n        function createHtmlNode(html) {\n            var e = document.createElement('div');\n            e.innerHTML = html;\n            if (e.childNodes.length === 1) {\n                return e.firstChild;\n            }\n            return e;\n        }\n\n        function createImageNode(p, item, id) {\n            var center = document.createElement('center');\n            var img = document.createElement('img');\n            img.id = id;\n\n            // Set the image source\n            if (item.base64) {\n                img.src = item.base64;\n            } else {\n                img.src = content.delayLoadImage ? ' ' : getAccessUrl(item.value, item.isLocal);\n            }\n\n            // Set the image style\n            img.style = item.style || 'width: 100%;';\n\n            // Add click event to the image\n            img.onclick = function (event) {\n                event.stopPropagation();  // Prevent any parent handlers from being triggered\n                sendCmd('showMedia', JSON.stringify({ type: item.type, value: item.value }));  // Call sendCmd with the item data\n            };\n\n            center.appendChild(img);\n            p.appendChild(center);\n        }\n\n        function createVideoNode(p, item) {\n            if (item.title) {\n                p.appendChild(createTextNode('span', item.title, 'text'));\n            }\n\n            // Define the cover image\n            var cover = 'data:image/gif;base64,R0lGODlhAQABAIAAAAUEBAAAACwAAAAAAQABAAACAkQBADs=';\n            if (item.cover) {\n                cover = item.cover.startsWith(\"http\") ? item.cover : getAccessUrl(item.cover);\n            }\n\n            // Create the video HTML with or without controls based on `item.isLocal`\n            var videoHtml = `\n        <video class=\"noClick\" controls preload=\"none\" controlsList=\"nodownload\"\n               poster=\"${cover}\" style=\"width: 100%; height: calc(50vw);\">\n            <source src=\"${getAccessUrl(item.value, item.isLocal)}\" type=\"video/mp4\">\n        </video>\n    `;\n\n            // Create a wrapper div for the video\n            var wrapper = document.createElement('div');\n            wrapper.innerHTML = videoHtml;\n            wrapper.style.position = 'relative';\n\n            // If item.isLocal is true, create an overlay to intercept clicks\n            if (item.isLocal) {\n                var overlay = document.createElement('div');\n                overlay.style.position = 'absolute';\n                overlay.style.top = 0;\n                overlay.style.left = 0;\n                overlay.style.width = '100%';\n                overlay.style.height = '100%';\n                overlay.style.backgroundColor = 'rgba(0, 0, 0, 0)';  // Fully transparent overlay\n                overlay.style.cursor = 'pointer';\n\n                // Add click event to the overlay\n                overlay.onclick = function (event) {\n                    event.stopPropagation();  // Prevent video from playing\n                    sendCmd('showMedia', JSON.stringify(item));  // Trigger the sendCmd function\n                };\n\n                // Append overlay to the wrapper\n                wrapper.appendChild(overlay);\n            }\n\n            // Append the wrapper to the parent\n            p.appendChild(wrapper);\n        }\n\n        function createQuestionNode(p, item, rows) {\n            if (item.title) {\n                p.appendChild(createTextNode('span', item.title, 'text'));\n            }\n\n            var id = item.value;\n            var value = getAnswer(id, '');\n            var div = document.createElement('div');\n            div.style.position = 'relative';\n            div.appendChild(createInputNode(id, 'answer', rows, value));\n\n            p.appendChild(div);\n            if (content.canShare) {\n                p.appendChild(createShareNode(id));\n            }\n        }\n\n        function createInlineQuestionNode(p, item, rows) {\n            var value = item.value;\n            if (!value) {\n                return;\n            }\n\n            var html = '';\n            var pos = 0;\n            var answers = value.match(/{[A-Za-z_0-9]*}/g);\n            if (!answers) {\n                // it's possible that the value doesn't contain any answer(s)\n                answers = [];\n            }\n\n            answers.forEach(function (it) {\n                var startPos = value.indexOf(it, pos);\n                html += value.substring(pos, startPos);\n                pos = startPos + it.length;\n                var id = `iq_${it.substring(1, it.length - 1)}`;\n                html += `<input class='inlineQuestion' id='${id}' oninput='onInlineQuestionChange(\"${id}\")'>`;\n            });\n            html += value.substring(pos);\n\n            var result = document.createElement('div');\n            result.innerHTML = html;\n            p.appendChild(result);\n\n            answers.forEach(function (it) {\n                var id = `iq_${it.substring(1, it.length - 1)}`;\n                document.getElementById(id).value = getAnswer(id, '');\n            });\n        }\n\n        function createSliderChoiceNode(p, item) {\n            var answer = getAnswer(item.value);\n\n            var result = document.createElement('div');\n            result.setAttribute('style', 'width: 100%; text-align: center; margin-bottom: 20px; font-size: 25px;');\n            result.innerHTML = answer !== undefined ? answer : content.i18n.NotAnswered;\n\n            var slider = document.createElement('input');\n            slider.setAttribute('type', 'range');\n            slider.setAttribute('class', 'slider noClick');\n            slider.setAttribute('min', item.minValue);\n            slider.setAttribute('max', item.maxValue);\n            slider.setAttribute('value', answer !== undefined ? answer : (item.minValue + item.maxValue) / 2);\n            slider.addEventListener('input', function () {\n                result.innerHTML = slider.value;\n                setAnswer(item.value, slider.value);\n            });\n\n            var titles = document.createElement('p');\n            titles.setAttribute('style', 'width: 100%; display: flex; justify-content: space-between; font-size: 18px;');\n            var html = '';\n            item.titles.forEach(function (it) {\n                html += `<span style=\"text-align: center\">${it.title}<br>${it.value}</span>`;\n            });\n            titles.innerHTML = html;\n\n            var div = document.createElement('div');\n            div.appendChild(result);\n            div.appendChild(slider);\n            div.appendChild(titles);\n\n            p.appendChild(div);\n        }\n\n        function createSingleChoiceNode(p, item) {\n            var title = document.createElement('span');\n            title.innerHTML = item.title;\n            p.appendChild(title);\n\n            var answer = parseInt(getAnswer(item.value, 0));\n            var div = document.createElement('div');\n            var html = '';\n            for (var i = 0; i <= item.options.length; i++) {\n                title = i === 0 ? content.i18n.NotAnswered : item.options[i - 1];\n                var id = `${item.value}_${i}`;\n                html += '<span style=\"display: flex; margin: 0.25em; margin-left: 1em; align-items: center;\">';\n                html += `<input type=\"radio\" id=\"${id}\" name=\"${item.value}\" value=\"${i}\" ${answer === i ? 'checked' : ''\n                    } onclick=\"onSingleChoiceAnswerChange(this)\"><label class=\"noClick\" for=\"${id}\">${title}</label>`;\n                html += '</span>';\n            }\n\n            div.innerHTML = html;\n            p.appendChild(div);\n        }\n\n        function onSingleChoiceAnswerChange(input) {\n            if (parseInt(input.value) === 0) {\n                setAnswer(input.name, '');\n            } else {\n                setAnswer(input.name, input.value);\n            }\n        }\n\n        function createMultipleChoiceNode(p, item) {\n            var answers = getAnswer(item.value, { selected: [] });\n\n            var options = document.createElement('p');\n            options.setAttribute('style', 'width: 100%; display: grid; font-size: 18px;');\n            var html = '';\n            for (var i = 0; i < item.options.length; i++) {\n                var id = `${item.value}_${i}`;\n                html += '<span style=\"margin-bottom: 3px;\">';\n                html += `<input type=\"checkbox\" id=\"${id}\" max=\"${item.max}\" name=\"${item.value}\" value=\"${i}\" ${answers.selected.indexOf(i) !== -1 ? 'checked' : ''\n                    } onclick=\"onMultipleChoiceAnswerChange('${item.value}', ${i})\"><label class=\"noClick\" for=\"${id}\">${item.options[i].title\n                    }</label>`;\n                html += '</span>';\n                var textInputId = item.options[i].textInput;\n                if (textInputId) {\n                    html += `<textarea id=${textInputId} class=\"answer\" rows=2 oninput=\"onMultipleChoiceAnswerChange('${item.value\n                        }', ${i}, this)\" onfocus=\"onTextAreaFocus(this)\" onblur=\"onTextAreaBlur(this)\">${answers[textInputId] || ''\n                        }</textarea>`;\n                }\n            }\n\n            options.innerHTML = html;\n            p.appendChild(createTextNode('span', item.title, 'text'));\n            p.appendChild(options);\n        }\n\n        function onInlineQuestionChange(id) {\n            setAnswer(id, document.getElementById(id).value);\n        }\n\n        function hexToRgb(hex) {\n            var shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\n            hex = hex.replace(shorthandRegex, function (m, r, g, b) {\n                return r + r + g + g + b + b;\n            });\n\n            var result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n            return result\n                ? {\n                    r: parseInt(result[1], 16),\n                    g: parseInt(result[2], 16),\n                    b: parseInt(result[3], 16)\n                }\n                : { r: 255, g: 255, b: 255 };\n        }\n\n        function rgbToHex(rgb) {\n            return '#' + ((1 << 24) + (rgb.r << 16) + (rgb.g << 8) + rgb.b).toString(16).slice(1);\n        }\n\n        function darken(color) {\n            color -= 50;\n            return color < 0 ? 0 : color;\n        }\n\n        function goToLesson(sessionIndex) {\n            // update session content for sessionIndex\n            content.sessionIndex = sessionIndex;\n            var sessionItem = null;\n            var nodes = document.getElementsByClassName('session');\n            if (nodes.length > 0) {\n                var index = 0;\n                for (var i = 0; i < content.session.content.length; i++) {\n                    var item = content.session.content[i];\n                    if (item.type === 'session') {\n                        var background = item.background || '#EFEFEF';\n                        if (index === sessionIndex) {\n                            sessionItem = item;\n                            var color = hexToRgb(background);\n                            color.r = darken(color.r);\n                            color.g = darken(color.g);\n                            color.b = darken(color.b);\n                            background = rgbToHex(color);\n                        }\n                        nodes[index].style.background = background;\n                        index++;\n                    }\n                }\n            }\n            if (sessionItem) {\n                sendCmd('goToLesson', JSON.stringify({ ...sessionItem, sessionIndex }));\n            }\n        }\n\n        function createSessionNode(p, item, sessionIndex, displayIndex) {\n            var index = '';\n            if (!item.hideIndex) {\n                index = `<div style=\"margin-left: 5px; width:40px; height:40px; line-height:40px; border: solid 2px #626262; border-radius: 100%; font-size: 18px; text-align: center;\">${displayIndex}</div>`;\n            }\n            var title = `<span style=\" overflow: hidden; text-overflow: ellipsis; margin-left: 5px; white-space: nowrap; width: calc(100vw - 130px);\">${item.title}</span>`;\n            var line = `<span style=\"display: flex; justify-content: center; align-items: center;\">${index}${title}</span><span style=\"padding-right: 0.5em; background: inherit;\">${item.progress}</span>`;\n            var a = document.createElement('a');\n            a.setAttribute('onclick', `goToLesson(${sessionIndex})`);\n            var background = item.background || '#EFEFEF';\n            if (content.sessionIndex === sessionIndex) {\n                var color = hexToRgb(background);\n                color.r = darken(color.r);\n                color.g = darken(color.g);\n                color.b = darken(color.b);\n                background = rgbToHex(color);\n            }\n            a.innerHTML = `<div class=\"session\" style=\"background: ${background}; height: 50px; width: 100%; border: solid 1px #626262; border-radius: 25px; display: flex; justify-content: space-between; align-items: center; overflow: hidden;\">${line}</div>`;\n\n            // TODO: Show numbers\n            if (item.newMessageCount > 0) {\n                var redDot = document.createElement('div');\n                redDot.setAttribute('class', 'redDot');\n                p.appendChild(redDot);\n            }\n            return p.appendChild(a);\n        }\n\n        function updateDownloadState(state, title, message) {\n            var button = document.querySelector('.download-button');\n            var messageContainer = document.querySelector('.download-message');\n            button.innerText = title;\n            messageContainer.innerText = message;\n            button.id = `downloadButtonId_${state}`;\n        }\n\n        function handleDownloadClick() {\n            var commandMap = {\n                \"downloadButtonId_download\": { cmd: \"download\" },\n                \"downloadButtonId_downloading\": {\n                    cmd: \"cancelDownload\",\n                    update: { state: 'download', label: content.i18n.Download, message: content.i18n.OnlineModeMessage }\n                },\n                \"downloadButtonId_downloaded\": { cmd: \"deleteDownload\" }\n            };\n            var button = document.querySelector('.link.noClick');\n            var action = commandMap[button.id];\n            if (action) {\n                sendCmd(action.cmd);\n                if (action.update) {\n                    updateDownloadState(action.update.state, action.update.label, action.update.message);\n                }\n            }\n        }\n\n        function createLessonSummaryDownload() {\n            // class download state 1-downloadable 2-downloaded\n            if (content.downloadState) {\n                var downloadButton = `<button class=\"link noClick download-button\" style=\"padding: 3px; width: 6.5em;\" onClick=\"handleDownloadClick()\" id=\"${content.downloadState === 1 ? 'downloadButtonId_download' : 'downloadButtonId_downloaded'}\">${content.downloadState === 1 ? content.i18n.Download : content.i18n.DeleteDownload}</button>`;\n                var downloadMessage = `<div class=\"download-message\" style=\"line-height: 1.2em; margin-top: 0.5em;min-height: 2.4em; margin-bottom: -1.2em\">${content.downloadState === 1 ? content.i18n.OnlineModeMessage : content.i18n.DownloadCompleteMessage}</div>`;\n\n                return `\n            <center style=\"margin-top: 1em;\">\n                ${downloadButton}\n            </center>\n            <center style=\"margin-top: 0.5em;line-height: 1.3em; font-size: 14px;\">\n                ${downloadMessage}\n            </center>\n        `;\n            } else {\n                return '';\n            }\n        }\n\n        function createLessonSummaryNode(p, item) {\n            var canShare = '';\n            if (item.canShare) {\n                canShare = `<button class=\"link noClick\" style=\"padding: 3px; width: 5.5em;\" onClick=\"sendCmd('share')\">${content.i18n.Share}</button>`;\n            }\n            var canUnshare = '';\n            if (item.canUnshare) {\n                canUnshare = `<button class=\"link noClick\" style=\"padding: 3px; width: 5.5em; margin-left: 1em;\" onClick=\"sendCmd('unshare')\">${content.i18n.Unshare}</button>`;\n            }\n            var sharedTo = '';\n            if (canShare || canUnshare) {\n                if (!Array.isArray(item.sharedTo)) {\n                    sharedTo += `<div style=\"line-height: 1.3em; text-decoration: underline; font-size: 14px;\">${item.sharedTo}</div>`;\n                } else {\n                    sharedTo = '<div style=\"line-height: 1.3em; font-size: 14px;\">';\n                    for (var i = 0; i < item.sharedTo.length; i++) {\n                        sharedTo += `<span style=\"text-decoration: underline;\">${item.sharedTo[i]}</span>`;\n                        if (i !== item.sharedTo.length - 1) {\n                            sharedTo += `<span>, </span>`;\n                        }\n                    }\n                    sharedTo += '</div>';\n                }\n                sharedTo += `<center style=\"margin-top: 1em; margin-bottom: 2em;\">${canShare} ${canUnshare}</center>`;\n            }\n            var download = createLessonSummaryDownload();\n            return p.appendChild(createHtmlNode(\n                `<div>\n            <center><img style=\"width: 120px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.2);\" src=\"${item.cover}\" /></center>\n            ${download}\n            <p style=\"line-height: 1.3em; font-size: 18px;\">${item.description.replace(/\\n/g, '<br>')}</p>\n            <p>\n              <center style=\"margin-top: 1em;\">\n                <b>${content.i18n.ShareClass}</b>\n              </center>\n              <div style=\"line-height: 1.3em; margin-bottom: 0.2em;\">${content.i18n.ShareClassDescription}</div>\n              ${sharedTo}\n            </p>\n          </div>`\n            ));\n        }\n\n        function onMultipleChoiceAnswerChange(id, index, tx) {\n            var checkbox = document.getElementById(`${id}_${index}`);\n\n            var value = content.answers[id];\n            if (!value || !Array.isArray(value.selected)) {\n                value = { selected: [] };\n            }\n\n            if (tx) {\n                tx.style.height = 'auto';\n                tx.style.height = tx.scrollHeight + 'px';\n\n                if (tx.value) {\n                    value[tx.id] = tx.value;\n                    checkbox.checked = true;\n                } else {\n                    delete value[tx.id];\n                    checkbox.checked = false;\n                }\n            }\n\n            if (checkbox.checked) {\n                if (value.selected.indexOf(index) === -1) {\n                    if (value.selected.length < checkbox.max) {\n                        value.selected.push(index);\n                    } else {\n                        checkbox.checked = false;\n                    }\n                }\n            } else {\n                var index = value.selected.indexOf(index);\n                if (index !== -1) {\n                    value.selected.splice(index, 1);\n                    // if no answer selected and no text input, we will remove the answer\n                    if (value.selected.length === 0 && Object.keys(value).length === 1) {\n                        value = undefined;\n                    }\n                }\n            }\n\n            setAnswer(id, value);\n        }\n\n        function addTextNode(p, item) {\n            var d = document.createElement('div');\n            d.setAttribute('style', item.style || 'width: 100%');\n            d.appendChild(createTextNode('span', item.value, item.type));\n            p.appendChild(d);\n        }\n\n        function getAnswer(id, defaultValue) {\n            if (!id || !content || !content.answers || content.answers[id] === undefined || content.answers[id] === null) {\n                return defaultValue;\n            }\n\n            return content.answers[id];\n        }\n\n        function loadContent() {\n            var div = document.getElementById('content');\n            div.setAttribute(\n                'style',\n                `margin-top:${content.marginTop !== undefined ? content.marginTop : 44}px; margin-bottom: ${content.marginBottom !== undefined ? content.marginBottom : 100\n                }px;`\n            );\n            var rows = 5;\n\n            evalJs = '';\n            var answerIds = {};\n            alert(JSON.stringify(content) )\n            content.session.content.forEach(function (item) {\n                switch (item.type) {\n                    case 'sliderChoice':\n                    case 'singleChoice':\n                    case 'multipleChoice':\n                    case 'question':\n                        // make sure id is not duplicate\n                        if (answerIds[item.value]) {\n                            alert(`Warning\\n\\nYour content has duplicate ID [${item.value}] \\n\\nPlease contact the book author.`);\n                        }\n                        answerIds[item.value] = 1;\n                        break;\n                    case 'lib':\n                        evalJs += item.value;\n                        break;\n                    default:\n                        break;\n                }\n            });\n            if (evalJs) {\n                eval(evalJs);\n            }\n\n            var sessionIndex = 0;\n            var displayIndex = 0;\n            var id = 0;\n            content.session.content.forEach(function (item) {\n                var p = document.createElement('p');\n                p.setAttribute('class', 'section');\n                div.appendChild(p);\n                switch (item.type) {\n                    case 'title':\n                    case 'subTitle':\n                    case 'text':\n                        addTextNode(p, item);\n                        break;\n                    case 'html':\n                        p.appendChild(createHtmlNode(item.value));\n                        break;\n                    case 'question':\n                        createQuestionNode(p, item, rows);\n                        break;\n                    case 'inlineQuestion':\n                        createInlineQuestionNode(p, item, rows);\n                        break;\n                    case 'link':\n                        p.appendChild(\n                            createLinkNode('button', item.value, `sendCmd('openLink', '${JSON.stringify({ ...item, url: getAccessUrl(item.url) })}')`, 'link')\n                        );\n                        break;\n                    case 'image':\n                        createImageNode(p, item, `image-${id++}`);\n                        break;\n                    case 'audio':\n                        if (item.title) {\n                            p.appendChild(createTextNode('span', item.title, 'text'));\n                        }\n                        if (content.playAudioInApp) {\n                            p.appendChild(\n                                createLinkNode('button', content.i18n.Play ? content.i18n.Play : 'Play', `sendCmd('playAudio', '${JSON.stringify({ ...item, value: getAccessUrl(item.value, item.isLocal) })}')`, 'link')\n                            );\n                        } else {\n                            p.appendChild(\n                                createHtmlNode(\n                                    `<audio class=\"noClick\" controls preload=\"none\" controlsList=\"nodownload\" style=\"width: 100%;\"> <source src=\"${getAccessUrl(\n                                        item.value, item.isLocal\n                                    )}\" type=\"audio/mpeg\"></audio>`\n                                )\n                            );\n                        }\n                        break;\n                    case 'video':\n                        createVideoNode(p, item)\n                        break;\n                    case 'bible':\n                        item.value.forEach(function (i) {\n                            p.appendChild(\n                                createLinkNode(\n                                    'a',\n                                    i.book + ' ' + i.verse,\n                                    `sendCmd('goToBible', '${JSON.stringify({\n                                        book: i.book,\n                                        verse: i.verse\n                                    })}')`,\n                                    'bible'\n                                )\n                            );\n                        });\n                        break;\n                    case 'js':\n                        p.appendChild(createTextNode('span', eval(item.value), 'text js'));\n                        break;\n                    case 'sliderChoice':\n                        createSliderChoiceNode(p, item);\n                        break;\n                    case 'singleChoice':\n                        createSingleChoiceNode(p, item);\n                        break;\n                    case 'multipleChoice':\n                        createMultipleChoiceNode(p, item);\n                        break;\n                    case 'session':\n                        if (typeof item.resetIndex === 'number') {\n                            displayIndex = item.resetIndex;\n                        } else {\n                            displayIndex++;\n                        }\n                        createSessionNode(p, item, sessionIndex++, displayIndex);\n                        break;\n                    case 'lessonSummary':\n                        createLessonSummaryNode(p, item);\n                        break;\n                    default:\n                        break;\n                }\n            });\n\n            // For some reason, on Android the operation doesn't succeed the first time, so we re-try several times\n            for (var i = 0; i < 5; i++) {\n                setTimeout(function () {\n                    resizeTextAreas();\n                }, [0, 50, 100, 400, 1000][i]);\n            }\n\n            document.body.scrollTop = content.position.scrollTop;\n\n            setTimeout(function () {\n                window.onscroll = function (e) {\n                    sendCmd('onScroll');\n                };\n\n                var nodes = document.getElementsByClassName('noClick');\n                for (var i = 0; i < nodes.length; i++) {\n                    nodes[i].addEventListener('click', function (e) {\n                        e.stopPropagation();\n                    });\n                }\n            }, 500);\n        }\n\n        function goToBible(book, verse) {\n            sendCmd('goToBible', JSON.stringify({ book, verse }));\n        }\n\n        function onload() {\n            loadContent();\n\n            setTimeout(function () {\n                var value = [];\n                getAllText(document.getElementsByClassName('text'), value);\n                getAllText(document.getElementsByClassName('title'), value);\n                getAllText(document.getElementsByClassName('subTitle'), value);\n                sendCmd('loaded', JSON.stringify({ value }));\n            }, 0);\n\n            checkBodyHeight();\n\n            var linkList = document.getElementsByTagName('a');\n            for (var i = 0; i < linkList.length; i++) {\n                // only apply to '<a href=..>', not goToLessons with '<a onClick=...>'\n                if (linkList[i].href) {\n                    linkList[i].onclick = function (evnt) {\n                        evnt.preventDefault();\n                        sendCmd('openLink', JSON.stringify({ url: evnt.srcElement.text }));\n                        return false;\n                    }\n                }\n            }\n        }\n\n        var bodyHeight = 0;\n        function checkBodyHeight() {\n            setTimeout(function () {\n                var value = document.documentElement.scrollHeight;\n                if (value !== bodyHeight) {\n                    bodyHeight = value;\n                    sendCmd('setHeight', JSON.stringify({ value }));\n                }\n                checkBodyHeight();\n            }, 100);\n        }\n\n        var globalTextNodeId = 1;\n        function getAllText(nodes, result) {\n            for (var i = 0; i < nodes.length; i++) {\n                var node = nodes[i];\n                for (var j = 0; j < node.childNodes.length; j++) {\n                    var childNode = node.childNodes[j];\n                    if (childNode.nodeType === Node.TEXT_NODE) {\n                        if (childNode.nodeValue && childNode.nodeValue.trim().length > 0) {\n                            if (!node.id) {\n                                node.id = `text-${globalTextNodeId++}`;\n                            }\n                            result.push({\n                                id: node.id,\n                                index: j,\n                                value: childNode.nodeValue\n                            });\n                        }\n                    } else if (childNode.nodeType === Node.ELEMENT_NODE) {\n                        if (childNode.childElementCount > 0) {\n                            getAllText([childNode], result);\n                        } else if (childNode.innerText && childNode.innerText.trim().length > 0) {\n                            childNode.id = `text-${globalTextNodeId++}`;\n                            result.push({ id: childNode.id, value: childNode.innerText });\n                        }\n                    }\n                }\n            }\n        }\n\n        function onMessage(data) {\n            switch (data.type) {\n                case 'setImage':\n                    var element = document.getElementById(data.id);\n                    if (element) {\n                        element.src = data.src;\n                    }\n                    break;\n                case 'setBible':\n                    data.value.forEach(function (item) {\n                        var element = document.getElementById(item.id);\n                        if (!element) {\n                            return;\n                        }\n\n                        var bibleHtml = '';\n                        item.value.forEach(function (i) {\n                            switch (i.type) {\n                                case 'text':\n                                    bibleHtml += i.value;\n                                    break;\n                                case 'bible':\n                                    bibleHtml += `<a class=\"bible noClick\" onclick=\"goToBible('${i.book}', '${i.verse}')\">${i.value}</a>`;\n                                    break;\n                            }\n                        });\n\n                        if (typeof item.index === 'number') {\n                            var html = [];\n                            for (var i = 0; i < element.childNodes.length; i++) {\n                                var childNode = element.childNodes[i];\n                                if (i === item.index) {\n                                    html.push(bibleHtml);\n                                } else if (childNode.nodeType === Node.TEXT_NODE) {\n                                    html.push(childNode.nodeValue);\n                                } else if (childNode.nodeType === Node.ELEMENT_NODE) {\n                                    html.push(childNode.outerHTML);\n                                }\n                            }\n                            element.innerHTML = html.join('');\n                        } else {\n                            element.innerHTML = bibleHtml;\n                        }\n                    });\n                    break;\n                case 'updateDownloadState':\n                    updateDownloadState(data.state, data.title, data.message)\n                    break;\n                default:\n                    break;\n            }\n        }\n\n        document.addEventListener('message', function (message) {\n            onMessage(JSON.parse(message.data));\n        });\n\n        window.addEventListener('message', function (message) {\n            onMessage(JSON.parse(message.data));\n        });\n    </script>\n</head>\n\n<body onload=\"onload()\" onclick=\"sendCmd('showNavBar')\">\n    <div id=\"content\" style=\"background: #6c6c6c\"></div>\n    <div style=\"height:70px\"></div>\n</body>\n\n</html>\n"}