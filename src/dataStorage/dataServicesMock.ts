import { dataServices } from '@/ioc/interfaces';
import { injectable } from 'inversify';

@injectable()
class mockDS implements dataServices {
  myHttpsServer = '';
  lastHttpErrorTime = null;
  isSwitchingToBackup = false;
  setUpHttpsServer = jest.fn();
  getHttpsServer = jest.fn();
  switchToBackupServer = jest.fn();
  setLastHttpErrorTime = jest.fn();
  getLastHttpErrorTime = jest.fn();
  getBackupHttpsServer = jest.fn();
  getCountryFromIP = jest.fn();
  loginUser = jest.fn();
  getUserAvatars = jest.fn();
  getMeetingLink = jest.fn();
  checkHttpsServer = jest.fn();
  setLaunchUrl = jest.fn();
  joinGroupViaCode = jest.fn();
  refreshAccessToken = jest.fn();
  getGroups = jest.fn();
  checkUserUpdate = jest.fn();
  deleteMessage = jest.fn();
  uploadMoments = jest.fn();
  getMoments = jest.fn();
  loadMoment = jest.fn();
  deleteMoment = jest.fn();
  likeMoment = jest.fn();
  unlikeMoment = jest.fn();
  setSeenMoment = jest.fn();
  loadMomentFrom = jest.fn();
  setUserLastSeenClass = jest.fn();
  getUserProfile = jest.fn();
  setUserProfile = jest.fn();
  inviteUsers = jest.fn();
  getStudyProgress = jest.fn();
  registerDevice = jest.fn();
  unregisterDevice = jest.fn();
  sendMessage = jest.fn();
  deleteUser = jest.fn();
  updateUserProfile = jest.fn();
  getUserData = jest.fn();
  setUserData = jest.fn();
  postFeedback = jest.fn();
  getInviteUsers = jest.fn();
  addUserEmail = jest.fn();
  respondToRequest = jest.fn();
  getGroup = jest.fn();
  createUser = jest.fn();
  resetPassword = jest.fn();
  inviteUser = jest.fn();
  createOneOnOne = jest.fn();
  clickMoment = jest.fn();
  createMoment = jest.fn();
  getMomentPreview = jest.fn();
  saveAnswer = jest.fn();
  setUserTagOrBlock = jest.fn();
  fetchUserTagAndBlock = jest.fn();
  getAllContactAsync = jest.fn();
  createDiscussion = jest.fn();
  deleteGroup = jest.fn();
  leaveGroup = jest.fn();
  createGroup = jest.fn();
  getClassStudyProgress = jest.fn();
  userHideMessages = jest.fn();
  removeUserFromGroup = jest.fn();
  groupUserDemote = jest.fn();
  groupUserPromote = jest.fn();
  getAttendance = jest.fn();
  deleteAttendance = jest.fn();
  setAttendance = jest.fn();
  deleteCollection = jest.fn();
  setCollection = jest.fn();
  getCollectionCategory = jest.fn();
  getChatMessage = jest.fn();
  deleteChatMessage = jest.fn();
  getChatTagMessage = jest.fn();
  setChatTagMessage = jest.fn();
  deleteChatTagMessage = jest.fn();
  setUserAvatars = jest.fn();
  sendChatFileMessage = jest.fn();
  getLesson = jest.fn();
  getClassCreateGuidePreview = jest.fn();
  shareGroupLesson = jest.fn();
  shareUserLesson = jest.fn();
  deleteGroupLesson = jest.fn();
  deleteUserLesson = jest.fn();
  getGroupViewTopList = jest.fn();
  setGroupViewTopList = jest.fn();
  getChannelOwned = jest.fn();
  getChannelSubscribes = jest.fn();
  getChannel = jest.fn();
  putChannelSubscribe = jest.fn();
  putChannelUnsubscribe = jest.fn();
  getProgramsRecommend = jest.fn();
  getPrograms = jest.fn();
  getChannelProgram = jest.fn();
  putChannelProgram = jest.fn();
  deleteChannelProgram = jest.fn();
  getChannelProgramComments = jest.fn();
  putChannelProgramComment = jest.fn();
  deleteChannelProgramComment = jest.fn();
  getChannelProgramLikes = jest.fn();
  putChannelProgramLike = jest.fn();
  getUserProgramsByType = jest.fn();
  getProgram = jest.fn();
  postProgramLike = jest.fn();
  postProgramUnlike = jest.fn();
  postProgramCollect = jest.fn();
  postProgramUncollect = jest.fn();
  putProgramShare = jest.fn();
  putProgramViews = jest.fn();
  getBibleData = jest.fn();
  getBibleChapters = jest.fn();
  getBibleSearch = jest.fn();
  getPreMeetingData = jest.fn();
  setPreMeetingData = jest.fn();
}

export { mockDS };
