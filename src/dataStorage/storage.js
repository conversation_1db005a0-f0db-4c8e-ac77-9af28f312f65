import { URLSearchParams } from 'react-native-url-polyfill';
import { cryptoRequest } from './cryptoproxy/cryptoAxios';
import cryptoUtils from './cryptoproxy/cryptoUtil';

import * as Device from 'expo-device';
import * as FileSystem from 'expo-file-system';
import * as Updates from 'expo-updates';

import { getCurrentUser, getHttpsServer } from '@/utils/user';

import { Asset } from 'expo-asset';
import Constants from 'expo-constants';
import { EasyLoading } from '@/components/EasyLoading';
import { Platform } from 'react-native';
import axios from 'axios';
import axiosRetry from 'axios-retry';
import { cloneDeep } from 'lodash-es';
import { i18n2 } from '@/utils/i18n2';
import { showMessage } from '@/components/MessageBox';

let connectionFailCount = 0;
let cryptoProxyFailCount = 0;

// const sleep = (time) => new Promise((resolve) => setTimeout(resolve, time));
axiosRetry(axios, { retries: 3, retryDelay: axiosRetry.exponentialDelay });

export const appFullVersion = Updates.createdAt ? Updates.createdAt.toISOString() : Constants.expoConfig.version;

export const appVersion = Updates.createdAt
  ? Updates.createdAt.toISOString().split('T')[0].replace(/-/g, '.')
  : Constants.expoConfig.version;

export function getHttpHeaders() {
  const headers = {
    'cache-control': 'no-cache',
    sessionId: Constants.sessionId,
    app: Constants.expoConfig.slug,
    platformOS: `${Platform.OS} ${Device.osVersion}${
      Platform.OS === 'android' ? ` API${Device.platformApiLevel}` : ''
    } ${Device.deviceYearClass} ${getHttpsServer()}`,
    version: `${
      Updates.createdAt ? Updates.createdAt.toISOString().replace(/-/g, '.') : Constants.expoConfig.version
    } ${Constants.expoConfig.version} SDK${Constants.expoConfig.sdkVersion}`,
    lang: getCurrentUser().getLanguage(),
    Accept: 'application/json, text/html',
    'Accept-Encoding': 'gzip'
  };

  if (getCurrentUser().getAccessToken().length > 0) {
    headers.Authorization = 'Bearer ' + getCurrentUser().getAccessToken();
  }

  return headers;
}

function getVersionNumber(versionString) {
  let result = 0;
  for (const item of versionString.split('.')) {
    result += item * 1000;
  }

  return result;
}

async function callWebServiceAsyncInternal(url, method = 'GET', data = undefined) {
  console.log(`+${method} ${url}`);
  let duration = 0;
  let response;
  let useCryptoProxy = false;

  try {
    const startTime = Date.now();
    let headers = getHttpHeaders();
    const baseURL = getHttpsServer();
    let reqConfig = { method, url: url.replace(baseURL, ''), baseURL, data, headers };

    //only add crypto on url that's under baseURL. SOS for example, doesn't
    if (!reqConfig.url.startsWith('http')) {
      //switch between crypto and httpsserver based on failure count
      if (cryptoProxyFailCount >= 2) {
        //proxy also failed, get back to use
        console.log('Switch to https');
        connectionFailCount = 0;
        getCurrentUser().setUseCryptoProxy(false);
      }
      if (connectionFailCount >= 2) {
        // after failures, use cryptoproxy
        console.log('Switch to cryptoproxy');
        cryptoProxyFailCount = 0;
        getCurrentUser().setUseCryptoProxy(true);
      }

      useCryptoProxy = getCurrentUser().getUseCryptoProxy();
      if (useCryptoProxy) {
        if (!cryptoUtils.getSessionSecurityToken()) {
          if (reqConfig.url !== '/user/refreshAccessToken') {
            // need to use accesstoken to get the crypto's security token.
            console.log('need to refresh accesstoken:', useCryptoProxy, getCurrentUser().getAccessToken());
            const result = await globalThis.dsObject.refreshAccessToken();
            if (result && result.body?.accessToken) {
              await getCurrentUser().setAccessTokenAsync(result.body.accessToken);
            }
          }
        }

        const cryptoProxy = getCurrentUser().getCryptoProxy();
        reqConfig = await cryptoRequest(reqConfig, cryptoProxy);
      }
    }
    console.log('useCryptoProxy: ', useCryptoProxy, getCurrentUser().getCryptoProxy());

    response = await axios(reqConfig);

    duration = Date.now() - startTime;
    if (useCryptoProxy) {
      cryptoProxyFailCount = 0;
    } else {
      connectionFailCount = 0;
    }
  } catch (err) {
    console.log(`${method} ${url} - ${JSON.stringify(err)}`, err);

    const resp = err.response;

    if (!resp || [400, 401].indexOf(resp.status) !== -1) {
      //this is connection error. This is in memory count, not persisted.
      //Relaunching app will be set to zero. This is intentional
      if (useCryptoProxy) {
        cryptoProxyFailCount++;
      } else {
        connectionFailCount++;
      }
    }

    if (resp) {
      return { headers: resp.headers, body: resp.data, status: resp.status };
    }

    return { headers: {}, body: {}, status: -1 };
  }

  const responseStatus = response.status;
  const responseHeader = response.headers ?? {};

  try {
    // do the best effort to check x-app-info response header for SDK update check
    const appInfoHeader = responseHeader['x-app-info'];
    if (appInfoHeader) {
      const info = JSON.parse(appInfoHeader);
      // v is sdkVersion, v2 is version (app version in store)
      if (info) {
        // check if we need to update proxy setting from server
        if (info.proxy) {
          console.log(`set proxy => ${info.proxy}`);
          getCurrentUser().setUpHttpsServer(info.proxy, true);
        }

        // check if we need to update download setting from server
        if (info.downloadServer) {
          console.log(`set downloadServer => ${info.downloadServer}`);
          getCurrentUser().setUpDownloadServer(info.downloadServer);
        }

        // check if we need to update cryptoproxy from server
        if (info.cryptoProxy) {
          console.log(`set cryptoProxy => ${info.cryptoProxy}`);
          getCurrentUser().setCryptoProxy(info.cryptoProxy, true);
        }

        // emit a global notification on inStoreUpdateNeeded
        if (
          (info.v && getVersionNumber(info.v) > getVersionNumber(Constants.expoConfig.sdkVersion)) ||
          (info.v2 && getVersionNumber(info.v2) > getVersionNumber(Constants.expoConfig.version))
        ) {
          globalThis.eventEmitter.emit('inStoreUpdateNeeded');
        }
      }
    }
  } catch (error) {
    console.log(error);
  }

  console.log(
    `[${duration}ms] ${method} ${url} [${responseStatus}] => ${
      data !== undefined ? '<= ' + JSON.stringify(data) : ''
    } (response: ${responseHeader['content-length'] ?? -1})`
  );

  return {
    headers: responseHeader,
    body: response.data,
    status: responseStatus
  };
}

export async function callWebServiceAsync(url, method = 'GET', body = undefined) {
  try {
    EasyLoading.show();
    return await callWebServiceAsyncInternal(url, method, body);
  } finally {
    EasyLoading.dismiss();
  }
}

export async function showWebServiceCallErrorsAsync(result, acceptStatus, showUI = true, onDismissUI = undefined) {
  if (!result || !result.status || result.status === -1) {
    if (showUI) {
      showMessage({
        message: i18n2.t('Error'),
        description: i18n2.t('Errors.CheckNetwork'),
        type: 'danger',
        onDismissUI
      });
    }

    console.log(JSON.stringify(result, undefined, 2));
    return false;
  }

  if (
    acceptStatus &&
    (Array.isArray(acceptStatus) ? acceptStatus.indexOf(result.status) === -1 : result.status !== acceptStatus)
  ) {
    // 401 - Unauthorized (access token expired, user is logged out on server side)
    if (result.status === 401) {
      globalThis.eventEmitter.emit('serverLogout');
    }

    let message = '';
    const body = cloneDeep(result.body);
    if (body) {
      if (body.error) {
        message += body.error;
        delete body.error;
      }

      if (Object.keys(result.body) > 0) {
        message += '\n\n' + JSON.stringify(body);
      }
    }
    if (showUI) {
      showMessage({
        message: i18n2.t('Error') + ` ${result.status}`,
        description: message,
        type: 'danger',
        onDismissUI
      });
    }

    console.log(JSON.stringify(result, undefined, 2));
    return false;
  }

  if (onDismissUI) {
    onDismissUI();
  }

  return true;
}

function failedForEnoughTime(currentTime, lastHttpErrorTime) {
  if (typeof lastHttpErrorTime !== Date) {
    return;
  }

  const msDifference = currentTime.getTime() - lastHttpErrorTime.getTime();
  const daysDifference = msDifference / (1000 * 60 * 60 * 24);
  console.log(`daysDifference is ${daysDifference}`);
  return daysDifference >= 7;
}

export async function callWebApi(
  url,
  method = 'GET',
  acceptStatus = 200,
  body = undefined,
  showUI = true,
  onDismissUI = undefined
) {
  const result = await callWebServiceAsync(url, method, body);
  const succeed = await showWebServiceCallErrorsAsync(result, acceptStatus, showUI, onDismissUI);
  if (!succeed) {
    console.log('web api call failed');
    if (globalThis.dsObject.isSwitchingToBackup) {
      // We are in the middle of switching to the backup server, so we just return to avoid infinite loop
      console.log('In the middle of switching to the backup server.');
      return null;
    }
    const currentTime = new Date();
    if (globalThis.dsObject.getLastHttpErrorTime()) {
      // http call has been failing. Let's see how long it has been failing.
      // if the http call has been failing for more than 7 days, then switch to the backup server.
      if (failedForEnoughTime(currentTime, globalThis.dsObject.getLastHttpErrorTime())) {
        console.log('switch to backup server');
        globalThis.dsObject.switchToBackupServer();
      }
    } else {
      // http call just started to fail, set the time
      console.log('set lastHttpErrorTime: ' + currentTime);
      globalThis.dsObject.setLastHttpErrorTime(currentTime);
    }
    return null;
  }

  // reset the time if http call succeeded.
  console.log('setLastHttpErrorTime to null');
  globalThis.dsObject.setLastHttpErrorTime(null);
  return result;
}

export async function getAssetLocalUriAsync(assetModule) {
  const asset = Asset.fromModule(assetModule);
  if (!asset.localUri) {
    await asset.downloadAsync();
  }

  return asset.localUri;
}

export async function uploadFileAsync(remoteUri, localUri, httpMethod = 'POST', fieldName = 'chatFile') {
  let result = {
    headers: {},
    body: {},
    status: 0
  };

  let duration = 0;
  try {
    const startTime = Date.now();
    EasyLoading.show();
    const info = await FileSystem.getInfoAsync(localUri);

    let uploadResult = {};
    if (info.size < 1024 * 1024 && getCurrentUser().getUseCryptoProxy()) {
      const formData = new FormData();

      const options = { encoding: 'base64' };
      const data = await FileSystem.readAsStringAsync(localUri, options);
      const urlParts = new URL(remoteUri);
      const params = new URLSearchParams(urlParts.search);

      const fileData = {
        filename: params.get('displayName'),
        type: params.get('type'),
        lastModified: info.modificationTime,
        size: info.size,
        data
      };

      formData.append(fieldName, fileData);
      uploadResult = await callWebApi(remoteUri, httpMethod, 201, formData);
    } else {
      const uriParts = localUri.split('.');
      const fileType = uriParts[uriParts.length - 1];
      uploadResult = await FileSystem.uploadAsync(remoteUri, localUri, {
        headers: {
          Authorization: 'Bearer ' + getCurrentUser().getAccessToken()
        },
        httpMethod,
        uploadType: FileSystem.FileSystemUploadType.MULTIPART,
        fieldName,
        mimeType: `image/${fileType}`
      });
    }

    duration = Date.now() - startTime;
    const { status, headers, body } = uploadResult;

    result = {
      status,
      headers,
      body
    };

    console.log(`[${duration}ms] ${httpMethod} ${remoteUri} [${result.status}] (FileSize: ${info.size})`);
  } catch (err) {
    console.log(`fileupload: ${localUri} ${remoteUri} - ${JSON.stringify(err)}`);
    throw err;
  } finally {
    EasyLoading.dismiss();
  }

  return result;
}

export async function googleLoginUser(idToken) {
  const url = `${getHttpsServer()}/user/oauthLogin?googleToken=${idToken}`;
  try {
    const response = await axios.get(url, { headers: getHttpHeaders() });

    if (response.status === 201) {
      return response.data;
    } else {
      return { success: false, error: 'UnexpectedResponse', details: response.data };
    }
  } catch (error) {
    console.error('Error during Google OAuth login:', error);
    return { success: false, error: error };
  }
}
