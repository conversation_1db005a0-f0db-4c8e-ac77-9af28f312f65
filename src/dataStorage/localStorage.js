import * as FileSystem from 'expo-file-system';

import { Platform } from 'react-native';
import { md5 } from '@/utils/md5';

const localFolder = 'Local';

async function initAsync() {
  const folder = `${FileSystem.documentDirectory}${localFolder}`;
  const dirInfo = await FileSystem.getInfoAsync(folder);
  if (!dirInfo.exists) {
    console.log('Creating ', folder);
    await FileSystem.makeDirectoryAsync(folder, { intermediates: true });
  }
}

export async function getFileInfoAsync(key) {
  const file = getFilePath(key);
  return await FileSystem.getInfoAsync(file);
}

export function getFilePath(key) {
  return `${FileSystem.documentDirectory}${localFolder}/${key}`;
}

export async function downloadAsync(key, url, headers) {
  const link = Platform.OS === 'ios' ? encodeURI(url) : url;
  const file = getFilePath(key);
  console.log('downloadAsync', key, link);

  let result = false;
  try {
    await initAsync();
    const info = await FileSystem.getInfoAsync(file);
    const downloadResult = await FileSystem.downloadAsync(link, file, { headers });
    console.log(`[${downloadResult.status}] Finished downloading ${url}`, JSON.stringify(downloadResult));
    const { uri, status } = downloadResult;
    if (status === 200) {
      return true;
    }

    if (!info.exists) {
      console.log(`Delete newly created file since it's invalid: ${uri}`);
      try {
        await FileSystem.deleteAsync(file, { idempotent: true });
      } catch {
        // do the best effort
      }
    }
  } catch (e) {
    console.log(e);
  }

  return result;
}

export async function deleteAsync(key) {
  const file = getFilePath(key);
  console.log('deleteAsync', key);

  try {
    await initAsync();
    await FileSystem.deleteAsync(file, { idempotent: true });
  } catch (e) {
    console.log(e);
  }
}

export async function deleteFilesAsync(shouldDeleteFunc) {
  try {
    await initAsync();

    const folder = `${FileSystem.documentDirectory}${localFolder}`;
    const files = await FileSystem.readDirectoryAsync(folder);
    for (const file of files) {
      if (shouldDeleteFunc(file)) {
        try {
          const filePath = `${folder}/${file}`;
          console.log('delete file', filePath);
          await FileSystem.deleteAsync(filePath, { idempotent: true });
        } catch (error) {
          console.log(error);
        }
      }
    }
  } catch (error) {
    console.log(error);
  }
}

export async function getFilesAsync() {
  try {
    await initAsync();

    const folder = `${FileSystem.documentDirectory}${localFolder}`;
    return await FileSystem.readDirectoryAsync(folder);
  } catch (error) {
    console.log(error);
    return [];
  }
}

function getUserObjectFile(key) {
  return getFilePath(`user-${md5(key)}`);
}

const setAndGetObjectPromise = {};

const addToPromiseChainAsync = async (promiseList, key, execAsync) => {
  if (!promiseList[key]) {
    promiseList[key] = execAsync();
  } else {
    promiseList[key] = promiseList[key].then(execAsync);
  }

  return promiseList[key];
};

export async function getObjectAsync(key) {
  return addToPromiseChainAsync(setAndGetObjectPromise, key, async () => {
    const file = getUserObjectFile(key);
    try {
      const info = await FileSystem.getInfoAsync(file);
      if (!info.exists) {
        return undefined;
      }

      const value = await FileSystem.readAsStringAsync(file, { encoding: 'utf8' });
      const result = JSON.parse(value);
      console.log('getObjectAsync', key, Object.keys(result));
      return result;
    } catch (e) {
      console.log(e);
      return undefined;
    }
  });
}

export async function setObjectAsync(key, value) {
  return addToPromiseChainAsync(setAndGetObjectPromise, key, async () => {
    const file = getUserObjectFile(key);
    try {
      await initAsync();

      try {
        const info = await FileSystem.getInfoAsync(file);
        if (info.exists) {
          const existingValue = await FileSystem.readAsStringAsync(file, { encoding: 'utf8' });
          value = { ...JSON.parse(existingValue), ...value };
        }
      } catch (error) {
        console.log(error);
      }

      const content = JSON.stringify(value);
      console.log('setObjectAsync', key, Object.keys(value));
      await FileSystem.writeAsStringAsync(file, content, { encoding: 'utf8' });
    } catch (e) {
      console.log(e);
      return false;
    }

    return true;
  });
}

export async function resetObjectAsync(key, value) {
  return addToPromiseChainAsync(setAndGetObjectPromise, key, async () => {
    const file = getUserObjectFile(key);
    try {
      await initAsync();

      const content = JSON.stringify(value);
      console.log('resetObjectAsync', key, Object.keys(value));
      await FileSystem.writeAsStringAsync(file, content, { encoding: 'utf8' });
    } catch (e) {
      console.log(e);
      return false;
    }

    return true;
  });
}

export async function deleteObjectAsync(key) {
  const file = getUserObjectFile(key);
  try {
    await initAsync();

    // console.log('deleteObjectAsync', key);
    const info = await FileSystem.getInfoAsync(file);
    if (info.exists) {
      await FileSystem.deleteAsync(file, { idempotent: true });
    }
  } catch (e) {
    console.log(e);
    return false;
  }

  return true;
}

export async function getStringAsync(key, encoding = 'utf8') {
  const file = getFilePath(key);
  try {
    return await FileSystem.readAsStringAsync(file, { encoding });
  } catch (e) {
    console.log(e);
    return '';
  }
}

export async function setStringAsync(key, content, encoding = 'utf8') {
  const file = getFilePath(key);
  try {
    await initAsync();
    await FileSystem.writeAsStringAsync(file, content, { encoding });
  } catch (e) {
    console.log(e);
    return false;
  }

  return true;
}

export async function removeLocalFilesAsync() {
  try {
    await FileSystem.deleteAsync(`${FileSystem.documentDirectory}${localFolder}`, { idempotent: true });
  } catch (e) {
    console.log(e);
    return false;
  }

  return true;
}
