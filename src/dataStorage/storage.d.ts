/* eslint-disable @typescript-eslint/no-explicit-any */

export declare const appFullVersion: string;

export declare const appVersion: string;

export declare function getHttpHeaders(): any;

export declare function callWebServiceAsyncInternal(url, method = 'GET', data = undefined): any;

export declare function callWebServiceAsync(url, method = 'GET', data = undefined): any;

export declare function showWebServiceCallErrorsAsync(
  result,
  acceptStatus,
  showUI = true,
  onDismissUI = undefined
): any;

export declare function callWebApi(
  url: string,
  method: string = 'GET',
  acceptStatus: number | number[] = 200,
  body: any = undefined,
  showUI: boolean = true,
  onDismissUI: boolean | undefined = undefined
): any;

export declare function getAssetLocalUriAsync(assetModule: string | number): string;

export declare function uploadFileAsync(
  remoteUri: string,
  localUri: string,
  httpMethod: string = 'POST',
  fieldName: string = 'chatFile'
): any;

export declare function googleLoginUser(idToken: string): any;
