import * as Expo from 'expo';

import { type EventEmitter } from 'expo-modules-core/types';
import { TYPES } from '@/ioc/types';
import { dataServices } from '@/ioc/interfaces';
import { iDigestContainer } from '@/ioc/inversify.config';

declare global {
  // eslint-disable-next-line no-var
  var dsObject: dataServices;
  // eslint-disable-next-line no-var, @typescript-eslint/no-explicit-any
  var eventEmitter: EventEmitter<Record<string, (...args: any[]) => void>>;
}

globalThis.dsObject = iDigestContainer.get<dataServices>(TYPES.dataServices);
globalThis.eventEmitter = new Expo.EventEmitter();
