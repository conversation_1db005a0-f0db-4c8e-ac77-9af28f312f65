const { withGradleProperties } = require('@expo/config-plugins');

const withAndroidConfigureGradle = (config) => {
  return withGradleProperties(config, (config) => {
    const gradleProperties = [
      {
        type: 'comment',
        value:
          'Jetifier must be enabled to build on Expo SDK 52, otherwise task :app:checkDebugDuplicateClasses will fail with errors such as:'
      },
      {
        type: 'comment',
        value: '> A failure occurred while executing com.android.build.gradle.internal.tasks.CheckDuplicatesRunnable'
      },
      {
        type: 'comment',
        value:
          '   > Duplicate class android.support.v4.app.INotificationSideChannel found in modules core-1.13.1.aar -> core-1.13.1-runtime (androidx.core:core:1.13.1) and support-compat-26.1.0.aar -> support-compat-26.1.0-runtime (com.android.support:support-compat:26.1.0)'
      },
      { type: 'property', key: 'android.enableJetifier', value: 'true' },

      { type: 'comment', value: 'Configure gradle for faster builds' },
      {
        type: 'property',
        key: 'org.gradle.jvmargs',
        value: '-Xmx14g -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8'
      },
      { type: 'property', key: 'org.gradle.parallel', value: 'true' },
      { type: 'property', key: 'org.gradle.configureondemand', value: 'true' }
    ];

    gradleProperties.forEach((property) => config.modResults.push(property));

    return config;
  });
};

module.exports = withAndroidConfigureGradle;
