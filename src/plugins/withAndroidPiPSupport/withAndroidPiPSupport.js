const { withAndroidManifest } = require('@expo/config-plugins');

const withAndroidPiPSupport = (config) => {
  return withAndroidManifest(config, async (config) => {
    const androidManifest = config.modResults;
    const mainApplication = androidManifest.manifest.application[0];
    const mainActivity = mainApplication['activity'].find(
      (activity) => activity['$']['android:name'] === '.MainActivity'
    );

    if (mainActivity) {
      mainActivity['$']['android:supportsPictureInPicture'] = 'true';
    }

    return config;
  });
};

module.exports = withAndroidPiPSupport;
