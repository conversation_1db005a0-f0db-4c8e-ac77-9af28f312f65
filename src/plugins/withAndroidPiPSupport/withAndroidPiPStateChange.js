const { withMainActivity } = require('@expo/config-plugins');

const importToAdd =
  'import com.facebook.react.modules.core.DeviceEventManagerModule\nimport android.app.ActivityManager\nimport android.content.Context\nimport com.facebook.react.bridge.Arguments';
const methodToAdd = `
  override fun onPictureInPictureModeChanged(isInPiPMode: Boolean) {
    super.onPictureInPictureModeChanged(isInPiPMode)
    val reactContext = reactNativeHost.reactInstanceManager.currentReactContext
    reactContext?.let {
        val emitter = it.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
        val map = Arguments.createMap()
        map.putBoolean("isInPiPMode", isInPiPMode)
        map.putBoolean("isAppInBackground", isAppInBackground())
        emitter.emit("PiPStatusChanged", map)
    }
  }

  private fun isAppInBackground(): Boolean {
    val am = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    val appProcesses = am.runningAppProcesses
    for (appProcess in appProcesses) {
      if (appProcess.processName == packageName) {
        if (appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_BACKGROUND) {
          return true
        }
      }
    }
    return false
  }
`;

const withAndroidPiPStateChange = (config) => {
  return withMainActivity(config, (config) => {
    let fileContent = config.modResults.contents;

    if (!fileContent.includes('import com.facebook.react.modules.core.DeviceEventManagerModule')) {
      fileContent = fileContent.replace(/import android.os.Bundle/, `import android.os.Bundle\n${importToAdd}`);
    }
    if (!fileContent.includes('onPictureInPictureModeChanged')) {
      fileContent = fileContent.replace(
        /class MainActivity : ReactActivity\(\) \{/,
        `class MainActivity : ReactActivity() {\n${methodToAdd}`
      );
    }
    config.modResults.contents = fileContent;

    return config;
  });
};

module.exports = withAndroidPiPStateChange;
