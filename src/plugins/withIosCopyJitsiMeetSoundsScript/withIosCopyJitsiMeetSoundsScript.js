const fs = require('fs');
const path = require('path');
const { withDangerousMod } = require('expo/config-plugins');
const xcode = require('xcode');

const withIosCopyJitsiMeetSoundsScript = (config) => {
  return withDangerousMod(config, [
    'ios',
    (config) => {
      const projectRoot = config.modRequest.projectRoot;
      const soundsDir = path.join(projectRoot, 'node_modules/@jitsi/react-native-sdk/sounds');
      const targetDir = path.join(projectRoot, 'ios', 'Resources');
      const projectPath = path.join(projectRoot, 'ios', 'app.xcodeproj', 'project.pbxproj');

      try {
        console.log('***** iOS begin copy JitsiMeet sounds *******');
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
          console.log(`Created target directory: ${targetDir}`);
        }
        const project = xcode.project(projectPath);
        project.parseSync();
        console.log(`Reading Xcode project file: ${projectPath}`);

        const mainGroup = project.getFirstProject().firstProject.mainGroup;
        let resourceGroup = project.pbxGroupByName('Resources');
        if (!resourceGroup) {
          resourceGroup = project.addPbxGroup([], 'Resources', 'Resources');
          project.addToPbxGroup(resourceGroup.uuid, mainGroup);
          console.log('Created and added Resources group to mainGroup');
        }

        fs.readdirSync(soundsDir).forEach((file) => {
          const sourceFilePath = path.join(soundsDir, file);
          const targetFilePath = path.join(targetDir, file);

          if (fs.existsSync(sourceFilePath)) {
            if (file.endsWith('.mp3')) {
              fs.copyFileSync(sourceFilePath, targetFilePath);
              project.addResourceFile(targetFilePath, {
                target: project.getFirstTarget().uuid,
                group: resourceGroup.uuid
              });
            }
          } else {
            console.warn(`Source file does not exist: ${sourceFilePath}`);
          }
        });
        fs.writeFileSync(projectPath, project.writeSync());
        console.log('Xcode project updated successfully.');
      } catch (error) {
        console.error('An error occurred:', error);
      }
      return config;
    }
  ]);
};
module.exports = withIosCopyJitsiMeetSoundsScript;
