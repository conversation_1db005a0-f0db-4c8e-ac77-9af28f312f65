const { AndroidConfig, withAndroidManifest } = require('@expo/config-plugins');
const { Paths } = require('@expo/config-plugins/build/android');
const path = require('path');
const fs = require('fs');
const fsPromises = fs.promises;

const { getMainApplicationOrThrow } = AndroidConfig.Manifest;

const withDisableCleartextTraffic = (config) => {
  return withAndroidManifest(config, async (config) => {
    config.modResults = await setCustomConfigAsync(config, config.modResults);
    return config;
  });
};

async function setCustomConfigAsync(config, androidManifest) {
  const mainApplication = getMainApplicationOrThrow(androidManifest);
  if (process.env.Country === 'China') {
    mainApplication.$['android:usesCleartextTraffic'] = 'true';
  } else {
    const src_file_pat = path.join(__dirname, 'network_security_config.xml');
    const res_file_path = path.join(
      await Paths.getResourceFolderAsync(config.modRequest.projectRoot),
      'xml',
      'network_security_config.xml'
    );

    const res_dir = path.resolve(res_file_path, '..');

    if (!fs.existsSync(res_dir)) {
      await fsPromises.mkdir(res_dir);
    }
    await fsPromises.copyFile(src_file_pat, res_file_path);

    mainApplication.$['android:networkSecurityConfig'] = '@xml/network_security_config';
  }

  return androidManifest;
}

module.exports = withDisableCleartextTraffic;
