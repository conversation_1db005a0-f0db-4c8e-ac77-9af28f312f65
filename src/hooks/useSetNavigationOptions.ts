import { BottomTabNavigationOptions } from '@react-navigation/bottom-tabs';
import { NativeStackNavigationOptions } from '@react-navigation/native-stack';
import { useLayoutEffect } from 'react';
import { useNavigation } from '@react-navigation/core';

export function useSetNavigationOptions(options: NativeStackNavigationOptions | BottomTabNavigationOptions) {
  const navigation = useNavigation();

  useLayoutEffect(() => {
    navigation.setOptions(options);
  }, [navigation, options]);
}
