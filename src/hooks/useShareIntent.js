import Constants, { ExecutionEnvironment } from 'expo-constants';
import { useEffect, useRef, useState } from 'react';

import { AppState } from 'react-native';
import ReceiveSharingIntent from 'react-native-receive-sharing-intent';

const isExpoGo = Constants.executionEnvironment === ExecutionEnvironment.StoreClient;

export default function useShareIntent() {
  const appState = useRef(AppState.currentState);
  const [shareIntent, setShareIntent] = useState(null);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      if (appState.current === 'active' && ['inactive', 'background'].includes(nextAppState)) {
        console.log('useShareIntent[to-background] reset intent');
        setShareIntent(null);
      }

      appState.current = nextAppState;
    });
    return () => {
      subscription.remove();
    };
  }, []);

  useEffect(() => {
    console.log('useShareIntent[mount]', Constants.expoConfig.scheme);
    if (isExpoGo) {
      console.log('no sharing intent when running in expo go');
      return;
    }
    ReceiveSharingIntent?.getReceivedFiles(
      (data) => {
        const intent = data[0];
        if (intent.weblink || intent.text) {
          const stringData = intent.weblink || intent.text || '';
          console.log('useShareIntent[text/url]', stringData);
          setShareIntent(JSON.stringify(stringData));
        } else if (intent.filePath) {
          const fileData = {
            uri: intent.contentUri || intent.filePath,
            mimeType: intent.mimeType,
            fileName: intent.fileName
          };
          console.log('useShareIntent[file]', fileData);
          setShareIntent(fileData);
        } else {
          console.log('useShareIntent[mount] share type not handled', data);
        }
      },
      (err) => {
        console.log('useShareIntent[mount] error', err);
      },
      Constants.expoConfig.scheme
    );
    return () => {
      ReceiveSharingIntent?.clearReceivedFiles();
    };
  }, []);

  return {
    shareIntent,
    resetShareIntent: () => setShareIntent(null)
  };
}
