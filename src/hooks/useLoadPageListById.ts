/* eslint-disable @typescript-eslint/no-explicit-any */
import { useRef, useState } from 'react';

interface IUseLoadPageListById<T> {
  request: (lastId: number, filter?: Record<string, any>) => Promise<T[]>;
  idKey: string;
}

export const useLoadPageListById = <T extends Record<string, any>>({ request, idKey }: IUseLoadPageListById<T>) => {
  const lastId = useRef<number>(-1);
  const noMore = useRef<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [list, setList] = useState<T[]>([]);
  const load = async (reload: boolean, filter?: Record<string, any>) => {
    if (reload) {
      lastId.current = -1;
      noMore.current = false;
    }
    if (loading || noMore.current) {
      return;
    }
    setLoading(true);
    try {
      const resultList = await request(lastId.current, filter);
      if (resultList?.length > 0) {
        const sortedItems = [...(reload ? [] : list), ...resultList].sort((pre, next) => next[idKey] - pre[idKey]);
        const uniqueIds = new Set<number>();
        const newList = sortedItems.filter((item) => {
          if (!uniqueIds.has(item[idKey])) {
            uniqueIds.add(item[idKey]);
            return true; // keep
          }
          return false; // remove
        });
        setList(newList);
        lastId.current = newList?.[newList.length - 1]?.[idKey];
      } else {
        noMore.current = true;
      }
    } finally {
      setLoading(false);
    }
  };
  return {
    list: list || [],
    setList,
    noMore: noMore.current,
    load
  };
};
