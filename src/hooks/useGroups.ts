import { useCallback, useState } from 'react';

import { useFocusEffect } from '@react-navigation/native';

export interface IGroupItemProps {
  groupId: string;
  name: string;
  status?: number;
  isOneOnOneGroup?: boolean;
}

export const useGroups = (isIncludeOneOneGroup?: boolean) => {
  const [groups, setGroups] = useState<IGroupItemProps[]>([]);

  const initGroups = useCallback(() => {
    async function getGroups() {
      const result = await globalThis.dsObject.getGroups();
      if (!result) {
        return;
      }
      const choices = result.body.filter(
        (studyGroup: IGroupItemProps) => studyGroup.status && (isIncludeOneOneGroup || !studyGroup.isOneOnOneGroup)
      );
      setGroups(choices);
    }

    getGroups();
  }, [isIncludeOneOneGroup]);

  useFocusEffect(initGroups);

  return { groups, initGroups };
};
