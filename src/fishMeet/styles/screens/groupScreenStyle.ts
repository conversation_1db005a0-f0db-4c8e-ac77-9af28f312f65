import { StyleSheet } from 'react-native';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { getCurrentUser } from '@/utils/user';
import { FM_SCREEN_PADDING_HORIZONTAL } from '@/fishMeet/styles/fmStyles';

export function getFmGroupScreenStyles() {
  return StyleSheet.create({
    cardTitleLeft: {
      alignSelf: 'flex-start',
      paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL,
      fontSize: getCurrentUser().getXLargeFontSize(),
      lineHeight: getCurrentUser().getXLargeFontSize() + 3
    },
    sectionHeadLeft: {
      textAlign: 'left',
      alignSelf: 'flex-start',
      paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL,
      fontSize: getCurrentUser().getXLargeFontSize(),
      lineHeight: getCurrentUser().getXLargeFontSize() + 3,
      textTransform: 'uppercase',
      marginBottom: 10
    },
    showMoreLessText: {
      color: FmColors.darkGray,
      textDecorationLine: 'underline',
      fontSize: getCurrentUser().getSmallFontSize()
    },
    hiddenMembersMessage: {
      color: 'red',
      fontSize: getCurrentUser().getSmallFontSize()
    },
    divider: {
      height: 1,
      backgroundColor: FmColors.mediumGray50,
      marginBottom: 35,
      width: '100%'
    },
    shareContainer: {
      alignItems: 'flex-start',
      paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL
    },
    shareRowContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL,
      marginTop: 10
    },
    hintTextLeft: {
      textAlign: 'left',
      alignSelf: 'flex-start',
      marginBottom: 12,
      color: FmColors.mediumGray100,
      fontSize: getCurrentUser().getSmallFontSize(),
      paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL
    },
    groupSettingTitleLeft: {
      alignSelf: 'flex-start',
      paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL
    },
    groupNameCardTitleLeft: {
      alignSelf: 'flex-start',
      paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL,
      fontSize: getCurrentUser().getXLargeFontSize(),
      lineHeight: getCurrentUser().getXLargeFontSize() + 3,
      marginBottom: 0
    }
  });
}
