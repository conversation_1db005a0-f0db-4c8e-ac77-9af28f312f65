import { FM_SCREEN_PADDING_HORIZONTAL } from '@/fishMeet/styles/fmStyles';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { StyleSheet } from 'react-native';
import { getCurrentUser } from '@/utils/user';

export function getFmSettingsScreenStyles() {
  return StyleSheet.create({
    userInfoContainer: {
      marginTop: 20,
      marginHorizontal: FM_SCREEN_PADDING_HORIZONTAL,
      padding: 10
    },
    userNameStyle: {
      fontWeight: '600',
      fontSize: getCurrentUser().getX9LargeFontSize(),
      lineHeight: getCurrentUser().getX9LargeFontSize() + 3,
      letterSpacing: 0,
      color: FmColors.accent
    },
    userIdStyle: {
      fontWeight: '400',
      fontSize: getCurrentUser().getXLargeFontSize(),
      lineHeight: getCurrentUser().getXLargeFontSize() + 3,
      letterSpacing: 0,
      color: FmColors.darkGray
    },
    groupContainer: {
      borderRadius: 20,
      borderWidth: 1,
      backgroundColor: FmColors.lightBackground,
      borderColor: FmColors.mediumGray50,
      marginTop: 20,
      marginHorizontal: FM_SCREEN_PADDING_HORIZONTAL,
      padding: 10
    }
  });
}
