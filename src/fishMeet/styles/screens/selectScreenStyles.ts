import { FM_BUTTON_HEIGHT, FM_ROUNDED_CORNER_RADIUS, FM_SCREEN_PADDING_HORIZONTAL } from '@/fishMeet/styles/fmStyles';

import { FmColors } from '@/fishMeet/styles/fmColors';
import { StyleSheet } from 'react-native';
import { getCurrentUser } from '@/utils/user';

export const getFmSelectScreenStyles = (insets: { top: number; bottom: number; left: number; right: number }) =>
  StyleSheet.create({
    screenContainer: {
      flex: 1,
      paddingLeft: Math.max(insets.left, FM_SCREEN_PADDING_HORIZONTAL),
      paddingRight: Math.max(insets.right, FM_SCREEN_PADDING_HORIZONTAL)
    },
    titleText: {
      marginVertical: 15,
      fontSize: getCurrentUser().getMediumFontSize()
    }
  });

export const getFmSelectScreenChoicesStyles = (isCurrent: boolean) => {
  const borderWidth = 2;
  return StyleSheet.create({
    choiceView: {
      marginTop: 15,
      backgroundColor: isCurrent ? FmColors.background : FmColors.accent,
      borderColor: FmColors.accent,
      borderWidth,
      borderRadius: FM_ROUNDED_CORNER_RADIUS,
      height: FM_BUTTON_HEIGHT - borderWidth,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    choiceText: {
      fontSize: getCurrentUser().getMediumFontSize(),
      paddingLeft: 7,
      fontWeight: 'bold',
      color: FmColors.darkGray
    }
  });
};
