import { FmColors } from '@/fishMeet/styles/fmColors';
import { FM_SCREEN_PADDING_HORIZONTAL } from '@/fishMeet/styles/fmStyles';
import { StyleSheet } from 'react-native';
import { getCurrentUser } from '@/utils/user';

export function getLoginInfoSettingScreenStyles() {
  return StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL,
      paddingVertical: 20
    },
    buttonContainer: {
      alignItems: 'center'
    },
    button: {
      width: '100%'
    },
    buttonText: {
      fontWeight: '600',
      fontSize: getCurrentUser().getXLargeFontSize(),
      lineHeight: getCurrentUser().getXLargeFontSize() + 3,
      letterSpacing: 0,
      textTransform: 'none',
      textAlign: 'left',
      paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL
    },
    groupContainer: {
      borderRadius: 20,
      borderWidth: 1,
      backgroundColor: FmColors.lightBackground,
      borderColor: FmColors.mediumGray50,
      marginTop: 20,
      padding: 10
    },
    itemBox: {
      flexDirection: 'row',
      alignItems: 'center',
      borderBottomWidth: 1,
      borderColor: FmColors.mediumGray50
    },
    titleBox: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1
    },
    itemTitle: {
      flex: 1
    },
    imagePlaceholder: {
      width: 41,
      height: 41,
      backgroundColor: '#D9D9D9',
      marginRight: 10
    },
    linkButton: {
      width: '100%',
      borderRadius: 22,
      paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL
    },
    linkButtonText: {
      fontWeight: '600',
      fontSize: getCurrentUser().getXLargeFontSize(),
      lineHeight: getCurrentUser().getXLargeFontSize() + 3,
      letterSpacing: 0,
      textTransform: 'none',
      textAlign: 'center'
    }
  });
}
