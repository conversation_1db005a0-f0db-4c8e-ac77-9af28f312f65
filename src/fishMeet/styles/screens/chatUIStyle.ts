import { StyleSheet } from 'react-native';
import { getCurrentUser } from '@/utils/user';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { FM_SCREEN_PADDING_HORIZONTAL, FM_ROUNDED_CORNER_RADIUS } from '@/fishMeet/styles/fmStyles';

export function getChatUIStyles(disablePostMessage = false, replyHeight?: number) {
  return StyleSheet.create({
    dayText: {
      color: FmColors.darkGray,
      fontSize: getCurrentUser().getXSmallFontSize()
    },
    messageTextLeft: {
      color: FmColors.darkGray,
      fontSize: getCurrentUser().getSmallFontSize(),
      lineHeight: getCurrentUser().getSmallFontSize() * 1.25
    },
    messageTextRight: {
      color: FmColors.darkGray,
      fontSize: getCurrentUser().getSmallFontSize(),
      lineHeight: getCurrentUser().getSmallFontSize() * 1.25
    },
    linkText: {
      color: FmColors.darkGray
    },
    fmBubbleContainer: {
      maxWidth: '75%',
      marginBottom: 3
    },
    nameText: {
      fontSize: getCurrentUser().getXSmallFontSize(),
      color: FmColors.darkGray
    },
    tagContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL
    },
    tagText: {
      paddingHorizontal: 2,
      color: FmColors.darkGray,
      fontWeight: 'bold',
      fontSize: getCurrentUser().getXSmallFontSize()
    },
    parsedText: {
      padding: FM_SCREEN_PADDING_HORIZONTAL,
      color: FmColors.darkGray,
      fontSize: getCurrentUser().getSmallFontSize(),
      lineHeight: getCurrentUser().getSmallFontSize() + 3
    },
    quoteParsedText: {
      padding: FM_SCREEN_PADDING_HORIZONTAL,
      color: FmColors.lightBackground,
      fontSize: getCurrentUser().getXSmallFontSize(),
      lineHeight: getCurrentUser().getXSmallFontSize() + 3
    },
    urlPreviewTextContainer: {
      flex: 1,
      paddingLeft: 7
    },
    urlPreviewTitle: {
      color: FmColors.darkGray
    },
    urlPreviewDescription: {
      color: FmColors.darkGray
    },
    timestamp: {
      color: FmColors.darkGray,
      fontSize: getCurrentUser().getXXSmallFontSize(),
      textAlign: 'right',
      marginLeft: 10,
      marginRight: 10,
      marginBottom: 5
    },
    quoteContainer: {
      marginTop: 2,
      borderTopLeftRadius: 0,
      borderTopRightRadius: 0,
      backgroundColor: FmColors.mediumGray50
    },
    fileText: {
      color: FmColors.darkGray,
      textDecorationLine: 'underline',
      fontSize: getCurrentUser().getSmallFontSize(),
      paddingHorizontal: 7
    },
    audioText: {
      color: FmColors.darkGray,
      fontSize: getCurrentUser().getSmallFontSize()
    },
    contactNameText: {
      marginLeft: 7,
      fontSize: getCurrentUser().getMediumFontSize(),
      textAlignVertical: 'center'
    },
    contactCardText: {
      width: 64,
      fontSize: getCurrentUser().getSmallFontSize(),
      textAlign: 'center',
      color: FmColors.mediumGray50
    },
    fmTagBaseButtonOuter: {
      borderWidth: 2,
      borderRadius: 24,
      borderColor: 'transparent',
      marginVertical: 5,
      marginHorizontal: 0.5
    },
    fmTagBaseButtonInner: {
      borderRadius: 24,
      padding: 5
    },
    fmSearchButtonOuter: {
      borderWidth: 2,
      borderRadius: 24,
      borderColor: FmColors.lightAccent,
      marginVertical: 5,
      marginHorizontal: 0.5
    },
    fmSearchButtonInner: {
      borderRadius: 24,
      padding: 5,
      backgroundColor: 'transparent',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between'
    },
    searchBarInputContainer: {
      backgroundColor: FmColors.lightBackground,
      borderColor: FmColors.accent,
      borderWidth: 1,
      paddingVertical: 3,
      alignSelf: 'center',
      borderRadius: FM_ROUNDED_CORNER_RADIUS,
      paddingLeft: 15,
      paddingRight: 45,
      marginTop: 5
    },
    fmInputBarContainerBase: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      zIndex: 1
    },
    fmInputBarDisabledOverlay: {
      position: 'absolute',
      zIndex: 99
    },
    fmInputBarDisabledInner: {
      justifyContent: 'center',
      alignContent: 'center'
    },
    fmInputBarDisabledText: {
      color: 'red',
      textAlign: 'center'
    },
    fmInputBarLeftActionWrapper: {
      flex: 1,
      bottom: 4
    },
    fmInputBarVoiceButton: {
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderRadius: 7,
      borderColor: FmColors.accent,
      paddingHorizontal: 9,
      paddingVertical: 7,
      bottom: 16
    },
    fmInputBarTextInput: {
      borderWidth: 1,
      borderRadius: 7,
      borderColor: FmColors.darkGray,
      paddingHorizontal: 9,
      paddingVertical: 6,
      fontSize: getCurrentUser().getSmallFontSize()
    },
    fmInputBarReplyContainer: {
      backgroundColor: FmColors.mediumGray25,
      margin: 1,
      borderRadius: 5,
      justifyContent: 'center',
      height: replyHeight
    },
    fmInputBarToggleButton: {
      paddingHorizontal: 7,
      bottom: 3
    },
    fmInputBarSendButton: {
      paddingHorizontal: 7
    },
    fmModalDivider: {
      marginVertical: 10,
      height: 1,
      backgroundColor: FmColors.mediumGray50
    },
    fmModalTagContainer: {
      flexDirection: 'row',
      justifyContent: 'center'
    },
    fmInputBarPlusIconContainer: {
      marginHorizontal: 7,
      bottom: 24,
      opacity: disablePostMessage ? 0.1 : 1
    },
    fmReplyText: {
      fontSize: getCurrentUser().getMediumFontSize(),
      color: FmColors.lightBackground,
      paddingRight: 30,
      paddingVertical: replyHeight != null ? (replyHeight - getCurrentUser().getMediumFontSize()) / 2 : 0
    },
    fmReplyCloseIconWrapper: {
      position: 'absolute',
      top: replyHeight != null ? (replyHeight - 24) / 2 : 0,
      right: replyHeight != null ? (replyHeight - 24) / 2 : 0,
      bottom: 24
    },
    fmInputBarToggleWrapper: {
      bottom: 22,
      opacity: disablePostMessage ? 0.1 : 1
    },
    fmInputBarSendWrapper: {
      bottom: 24
    },
    chatHeaderContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8
    },
    chatHeaderTitle: {
      fontSize: getCurrentUser().getMediumFontSize(),
      fontWeight: 'bold',
      color: FmColors.darkGray
    },
    chatHeaderActionsContainer: {
      flexDirection: 'row'
    },
    chatHeaderActionButton: {
      marginHorizontal: 8
    }
  });
}
