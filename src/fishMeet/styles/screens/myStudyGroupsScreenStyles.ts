import { StyleSheet } from 'react-native';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { getCurrentUser } from '@/utils/user';
import { FM_SCREEN_PADDING_HORIZONTAL, FM_ROUNDED_CORNER_RADIUS } from '@/fishMeet/styles/fmStyles';

export function getFmMyStudyGroupsScreenStyles(insets: { top: number; bottom: number; left: number; right: number }) {
  return StyleSheet.create({
    groupContainer: {
      paddingLeft: Math.max(insets.left, FM_SCREEN_PADDING_HORIZONTAL),
      paddingRight: Math.max(insets.right, FM_SCREEN_PADDING_HORIZONTAL),
      paddingVertical: 10,
      marginVertical: 4,
      borderRadius: FM_ROUNDED_CORNER_RADIUS,
      flexDirection: 'row'
    },
    groupHeader: {
      flex: 1,
      flexDirection: 'column',
      justifyContent: 'flex-start'
    },
    groupNameText: {
      fontSize: getCurrentUser().getLargeFontSize(),
      lineHeight: getCurrentUser().getLargeFontSize() + 3,
      color: FmColors.mediumGray100
    },
    metioned: {
      paddingTop: 1,
      fontSize: getCurrentUser().getXSmallFontSize(),
      color: 'red'
    },
    latestMessage: {
      fontSize: getCurrentUser().getSmallFontSize(),
      color: FmColors.mediumGray50,
      fontWeight: '400'
    },
    acceptedGroupsContainer: {
      flex: 1,
      paddingLeft: Math.max(insets.left, FM_SCREEN_PADDING_HORIZONTAL),
      paddingRight: Math.max(insets.right, FM_SCREEN_PADDING_HORIZONTAL)
    },
    footerText: {
      fontSize: getCurrentUser().getLargeFontSize(),
      color: 'red',
      margin: 10,
      textAlign: 'left'
    }
  });
}
