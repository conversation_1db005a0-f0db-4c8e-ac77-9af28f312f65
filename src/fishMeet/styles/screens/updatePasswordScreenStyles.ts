import { StyleSheet } from 'react-native';
import { getCurrentUser } from '@/utils/user';

import { FM_SCREEN_PADDING_HORIZONTAL } from '@/fishMeet/styles/fmStyles';
import { FmColors } from '@/fishMeet/styles/fmColors';

export function getUpdatePasswordScreenStyles() {
  const xLargeFontSize = getCurrentUser().getXLargeFontSize();
  return StyleSheet.create({
    emailContainer: {
      marginTop: 20,
      marginHorizontal: FM_SCREEN_PADDING_HORIZONTAL,
      padding: 20,
      alignItems: 'flex-start'
    },
    emailText: {
      fontWeight: 600,
      fontSize: xLargeFontSize,
      color: FmColors.mediumGray75,
      textTransform: 'uppercase'
    },
    loginIdText: {
      fontWeight: 600,
      fontSize: getCurrentUser().getX3LargeFontSize(),
      lineHeight: getCurrentUser().getX3LargeFontSize() + 3,
      color: FmColors.darkGray
    },
    contentContainer: {
      justifyContent: 'center',
      alignItems: 'flex-start',
      marginHorizontal: FM_SCREEN_PADDING_HORIZONTAL,
      padding: 20
    },
    submitContainer: {
      alignSelf: 'center',
      marginTop: 20
    },
    newPwdText: {
      fontSize: xLargeFontSize,
      marginTop: 10,
      color: FmColors.mediumGray75,
      textTransform: 'uppercase'
    }
  });
}
