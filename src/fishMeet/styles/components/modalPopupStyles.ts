import { FmColors } from '@/fishMeet/styles/fmColors';
import { StyleSheet } from 'react-native';
import { getCurrentUser } from '@/utils/user';

export const getFmModalPopupStyles = (
  sheetContainerWidth: number,
  contentContainerWidth: number,
  contentContainerMarginHorizontal: number,
  contentContainerBorderRadius: number,
  contentContainerPadding: number,
  showDivider: boolean,
  itemsLength: number,
  iconsPerRow: number,
  contentItemWidth: number
) =>
  StyleSheet.create({
    backdrop: {
      color: FmColors.mediumGray100,
      opacity: 0.5
    },
    sheetContainer: {
      width: sheetContainerWidth,
      backgroundColor: FmColors.background,
      alignItems: 'center',
      borderTopLeftRadius: 30,
      borderTopRightRadius: 30
    },
    headerText: {
      fontSize: getCurrentUser().getMediumFontSize(),
      marginVertical: 12
    },
    contentContainer: {
      width: contentContainerWidth,
      marginHorizontal: contentContainerMarginHorizontal,
      borderTopLeftRadius: contentContainerBorderRadius,
      borderTopRightRadius: contentContainerBorderRadius,
      padding: contentContainerPadding,
      flexDirection: 'row',
      borderBottomWidth: showDivider ? 1 : 0,
      justifyContent: itemsLength <= iconsPerRow ? 'center' : 'flex-start',
      flexWrap: 'wrap'
    },
    contentItem: {
      width: contentItemWidth,
      marginVertical: 5
    },
    contentItemText: {
      fontSize: getCurrentUser().getSmallMinusFontSize(),
      paddingTop: 5,
      textAlign: 'center'
    }
  });
