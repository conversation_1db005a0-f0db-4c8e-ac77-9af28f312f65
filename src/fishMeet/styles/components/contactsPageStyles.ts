import { FM_SCREEN_PADDING_HORIZONTAL } from '@/fishMeet/styles/fmStyles';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { StyleSheet } from 'react-native';
import { getCurrentUser } from '@/utils/user';

export function getFmContactsPageStyles(insets: { top: number; bottom: number; left: number; right: number }) {
  return StyleSheet.create({
    pageContainer: {
      flex: 1,
      paddingLeft: Math.max(insets.left, FM_SCREEN_PADDING_HORIZONTAL),
      paddingRight: Math.max(insets.right, FM_SCREEN_PADDING_HORIZONTAL)
    },
    sectionHeaderContainer: {
      flex: 1,
      backgroundColor: FmColors.background,
      marginLeft: 5
    },
    sectionHeaderText: {
      fontSize: getCurrentUser().getLargeFontSize(),
      fontWeight: 'bold'
    }
  });
}
