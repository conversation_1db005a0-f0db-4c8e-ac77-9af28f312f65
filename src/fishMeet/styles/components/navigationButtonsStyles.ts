import { FmColors } from '@/fishMeet/styles/fmColors';
import { StyleSheet } from 'react-native';
import { type NavBarButtonProps } from '@/components/navigation/NavigationButtons';

export function getFmNavBarButtonStyles(
  right: NavBarButtonProps['right'],
  backgroundColor: NavBarButtonProps['backgroundColor'],
  disabled: NavBarButtonProps['disabled']
) {
  return right
    ? StyleSheet.create({
        buttonOpacity: {
          right: -7,
          backgroundColor: backgroundColor ?? FmColors.background,
          opacity: disabled ? 0.3 : 1
        }
      })
    : StyleSheet.create({
        buttonOpacity: {
          left: -7,
          backgroundColor: backgroundColor ?? FmColors.background,
          opacity: disabled ? 0.3 : 1
        }
      });
}
