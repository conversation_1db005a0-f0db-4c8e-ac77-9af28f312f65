import { StyleSheet } from 'react-native';

import { FM_ROUNDED_CORNER_RADIUS, FM_BUTTON_HEIGHT } from '@/fishMeet/styles/fmStyles';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { getCurrentUser } from '@/utils/user';
import { type ButtonProps } from '@/components';
import { type FmTextStyle } from '@/fishMeet/components/FmText';

const fontSize = getCurrentUser().getMediumFontSize();
// Vertically center text and prevent Chinese text from being cut off on the bottom
const FM_BUTTON_LINE_HEIGHT = fontSize + 6.5;

export function getFmButtonStyles(width: ButtonProps['width'], style: ButtonProps['style'], textStyle: FmTextStyle) {
  return StyleSheet.create({
    disabledButton: {
      borderRadius: FM_ROUNDED_CORNER_RADIUS,
      backgroundColor: FmColors.mediumGray50,
      marginVertical: 10,
      height: FM_BUTTON_HEIGHT,
      width,
      ...style
    },
    rectButton: {
      borderRadius: FM_ROUNDED_CORNER_RADIUS,
      backgroundColor: FmColors.accent,
      marginVertical: 10,
      height: FM_BUTTON_HEIGHT,
      width,
      ...style
    },
    textContainer: {
      flex: 1,
      justifyContent: 'center'
    },
    text: {
      color: FmColors.darkGray,
      fontWeight: 'bold',
      textAlign: 'center',
      textTransform: 'uppercase',
      fontSize,
      lineHeight: FM_BUTTON_LINE_HEIGHT,
      ...textStyle
    }
  });
}
