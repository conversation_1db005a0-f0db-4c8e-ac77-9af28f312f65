import { FM_ROUNDED_CORNER_RADIUS, FM_SCREEN_PADDING_HORIZONTAL } from '@/fishMeet/styles/fmStyles';

import { FmColors } from '@/fishMeet/styles/fmColors';
import { StyleSheet } from 'react-native';
import { getCurrentUser } from '@/utils/user';

export function getFmMemberInviteStyles(expandOpacityTextFontSize: number) {
  return StyleSheet.create({
    flashListContentContainer: {
      paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL
    },
    headerContainer: {
      alignItems: 'flex-start'
    },
    selectedInviteUsersListContainer: {
      flex: 1,
      flexDirection: 'row',
      flexWrap: 'wrap',
      rowGap: 10,
      marginVertical: 10
    },
    selectedInviteUsersOpacityContainer: {
      width: 85,
      alignItems: 'center'
    },
    selectedInviteUsersOpacity: {
      paddingTop: 5,
      paddingBottom: 3,
      marginLeft: 15,
      paddingRight: 15
    },
    selectedInviteUsersCloseIcon: {
      position: 'absolute',
      left: 45
    },
    selectedInviteUsersText: {
      fontSize: getCurrentUser().getSmallFontSize()
    },
    fmUserEnteredMemberChipListContainer: {
      flex: 1,
      flexDirection: 'row',
      flexWrap: 'wrap',
      columnGap: 10,
      marginBottom: 5
    },
    expandOpacityText: {
      fontSize: expandOpacityTextFontSize,
      marginHorizontal: 15
    }
  });
}

export const fmUserEnteredMemberChipStyles = StyleSheet.create({
  chipOpacity: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: FmColors.accent,
    paddingLeft: 20,
    borderRadius: FM_ROUNDED_CORNER_RADIUS,
    marginVertical: 6,
    paddingVertical: 7
  },
  text: {
    fontSize: getCurrentUser().getMediumFontSize()
  },
  closeIcon: {
    marginLeft: 15,
    marginRight: 10
  }
});
