import { FmColors } from '@/fishMeet/styles/fmColors';
import { StyleSheet } from 'react-native';

export const FM_SCREEN_PADDING_HORIZONTAL = 15;
export const FM_ROUNDED_CORNER_RADIUS = 24;
// Keep synced with InputField height
export const FM_BUTTON_HEIGHT = 48;

export const FmStyles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    paddingHorizontal: FM_SCREEN_PADDING_HORIZONTAL,
    alignItems: 'center',
    backgroundColor: FmColors.background
  },
  sectionSpacer: {
    marginTop: 20
  },
  buttonContainer: {
    marginTop: 20
  }
});
