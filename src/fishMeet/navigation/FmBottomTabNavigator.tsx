import { BottomTabNavigationOptions, createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import React, { useContext } from 'react';
import { TouchableOpacity, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { FmColors } from '@/fishMeet/styles/fmColors';
import MyStudyGroupsScreen from '@/screens/MyStudyGroupsScreen/MyStudyGroupsScreen';
import { NativeStackNavigationOptions } from '@react-navigation/native-stack';
import { NavBarTitle } from '@/components';
import SettingsScreen from '@/screens/SettingsScreen';
import { TabIcon } from '@/navigation/BottomTabNavigator';
import { i18n2 } from '@/utils/i18n2';
import useIsKeyboardShown from '@react-navigation/bottom-tabs/src/utils/useIsKeyboardShown';
import { goToMeetingTab } from '@/utils/goToScreen';
import { FmBackNavIcon } from '@/fishMeet/components/FmIcons';

export const fmDefaultTheme = {
  dark: false,
  colors: {
    primary: FmColors.accent,
    background: FmColors.background,
    card: FmColors.background,
    text: FmColors.mediumGray100,
    border: FmColors.mediumGray50,
    notification: 'red'
  }
};

const FmNavBarTitle = (props: { title: string }) => (
  <NavBarTitle
    title={props.title}
    boldTitle={true}
    titleSize={20}
    titleStyle={{ color: FmColors.darkGray, textTransform: 'uppercase' }}
  />
);

export const fmScreenOptionsLoggedIn = {
  headerShadowVisible: false,
  headerBackVisible: false, // This is needed for Android, otherwise it shows two back buttons
  headerTitle: ({ children }) => <FmNavBarTitle title={children} />,
  headerLeft: () => <FmBackNavIcon />
} satisfies NativeStackNavigationOptions;

const fmScreenOptionsBottomTab = {
  headerShadowVisible: false,
  headerTitle: ({ children }) => <FmNavBarTitle title={children} />
} satisfies BottomTabNavigationOptions;

export const fmScreenOptionsNotLoggedIn = {
  ...fmScreenOptionsLoggedIn,
  headerTitle: ''
} satisfies NativeStackNavigationOptions;

const FmBottomTab = createBottomTabNavigator();

export const FmBottomTabNavigator = () => {
  const context = useContext(AppContext) as {
    insets: { top: number; bottom: number; left: number; right: number };
    hasNewChatMessage: boolean;
    inAppUpdateNeeded: boolean;
    inStoreUpdateNeeded: boolean;
    canOpenJitsi: boolean;
    canOpenFishMeet: boolean;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    groups: any;
  };

  const isKeyboardShown = useIsKeyboardShown();

  return (
    <FmBottomTab.Navigator
      initialRouteName='GroupTab'
      screenOptions={fmScreenOptionsBottomTab}
      tabBar={({ state, navigation }) => {
        return (
          !isKeyboardShown && (
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                marginBottom: context.insets.bottom
              }}>
              {state.routes.map((route, index) => {
                const isFocused = state.index === index;

                const onLongPress = () => {
                  navigation.emit({
                    type: 'tabLongPress',
                    target: route.key
                  });
                };

                let showRedDot = false;
                let focusedImage = null;
                let unfocusedImage = null;
                let text = '';
                switch (route.name) {
                  case 'GroupTab':
                    showRedDot = context.hasNewChatMessage;
                    focusedImage = require('@/assets/images/icon-Group-active.png');
                    unfocusedImage = require('@/assets/images/icon-Group.png');
                    text = i18n2.t('GroupsUI.Title');
                    break;
                  case 'MeetingTab':
                    showRedDot = false;
                    focusedImage = require('@/assets/images/icon-Conference.png');
                    unfocusedImage = require('@/assets/images/icon-Conference.png');
                    text = i18n2.t('MeetingUI.Title');
                    break;
                  case 'SettingsTab':
                    showRedDot = context.inAppUpdateNeeded || context.inStoreUpdateNeeded;
                    focusedImage = require('@/assets/images/icon-Settings-active.png');
                    unfocusedImage = require('@/assets/images/icon-Settings.png');
                    text = i18n2.t('SettingsUI.Title');
                    break;
                }

                return (
                  <TouchableOpacity
                    key={route.name}
                    activeOpacity={1}
                    onPress={() => {
                      if (route.name === 'MeetingTab') {
                        console.log('meeting tab clicked.');
                        goToMeetingTab(context.canOpenJitsi, context.canOpenFishMeet, context.groups, navigation);
                        return;
                      }

                      const event = navigation.emit({
                        type: 'tabPress',
                        target: route.key,
                        canPreventDefault: true
                      });

                      if (!isFocused && !event.defaultPrevented) {
                        // The `merge: true` option makes sure that the params inside the tab screen are preserved
                        navigation.navigate({ name: route.name, params: {}, merge: true });
                      }
                    }}
                    onLongPress={onLongPress}
                    style={{ marginBottom: 5 }}>
                    <TabIcon
                      focused={isFocused}
                      showRedDot={showRedDot}
                      focusedImage={focusedImage}
                      unfocusedImage={unfocusedImage}
                      text={text}
                    />
                  </TouchableOpacity>
                );
              })}
            </View>
          )
        );
      }}>
      <FmBottomTab.Screen
        name='GroupTab'
        component={MyStudyGroupsScreen}
        options={MyStudyGroupsScreen.navigationOptions}
      />
      <FmBottomTab.Screen
        name='MeetingTab'
        component={MyStudyGroupsScreen}
        options={MyStudyGroupsScreen.navigationOptions}
      />
      <FmBottomTab.Screen name='SettingsTab' component={SettingsScreen} options={SettingsScreen.navigationOptions} />
    </FmBottomTab.Navigator>
  );
};
