/* eslint-disable react-native/no-unused-styles */
import { FmText, type FmTextStyle } from '@/fishMeet/components/FmText';
import { type Input as BaseInput } from '@rneui/base';
import { Input, type InputProps } from '@rneui/themed';
import React, { forwardRef } from 'react';
import { StyleSheet, type TextInput, View } from 'react-native';

import { FM_ROUNDED_CORNER_RADIUS, FM_BUTTON_HEIGHT } from '@/fishMeet/styles/fmStyles';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { getCurrentUser } from '@/utils/user';
import { i18n2 } from '@/utils/i18n2';
import { FmPlusIcon, FmCloseIcon, FmSearchIcon } from '@/fishMeet/components/FmIcons';

type InputFieldBaseProps = Omit<InputProps, 'labelStyle'> & {
  inputType?: 'email' | 'password';
  labelStyle?: FmTextStyle;
  showClearButton?: boolean;
  onClearButton?: () => void;
  showAddButton?: boolean;
  onAddButton?: () => void;
};

type InputFieldProps =
  | (InputFieldBaseProps & {
      showClearButton: true;
      value: string;
    })
  | (InputFieldBaseProps & {
      showClearButton?: false;
    });

// Workaround for https://github.com/react-native-elements/react-native-elements/issues/3202
export type InputFieldRef = TextInput & BaseInput;

export const InputField = forwardRef<InputFieldRef, InputFieldProps>(function InputField(
  { inputType, labelStyle, showClearButton, onClearButton, showAddButton, onAddButton, value, ...inputProps },
  ref
) {
  const styles = getInputFieldStyles(showClearButton);

  const inputTypePropOverrides = {} as InputProps;
  switch (inputType) {
    case 'email':
      inputTypePropOverrides.placeholder = i18n2.t('EmailExample');
      inputTypePropOverrides.returnKeyType = 'next';
      break;
    case 'password':
      inputTypePropOverrides.placeholder = i18n2.t('Common.Pwd');
      inputTypePropOverrides.returnKeyType = 'go';
      inputTypePropOverrides.secureTextEntry = true;
      break;
    default:
      break;
  }

  const clearButtonProps = {
    rightIconContainerStyle: styles.clearButtonContainer
  } as InputProps;
  if (showClearButton) {
    if (value.length) {
      clearButtonProps.rightIcon = <FmCloseIcon size={26} onPress={onClearButton} />;
    } else {
      clearButtonProps.rightIcon = <FmSearchIcon enabled={false} />;
    }
  }

  return (
    <View style={styles.inputFieldContainer}>
      <Input
        containerStyle={styles.container}
        inputContainerStyle={styles.inputContainer}
        autoCorrect={false}
        autoCapitalize='none'
        submitBehavior='submit'
        errorStyle={styles.error}
        inputStyle={styles.input}
        placeholderTextColor={FmColors.mediumGray50}
        {...clearButtonProps}
        {...inputProps}
        {...inputTypePropOverrides}
        value={value}
        label={
          inputProps.label ? (
            <FmText style={{ ...styles.inputFieldLabelText, ...labelStyle }}>{inputProps.label}</FmText>
          ) : null
        }
        ref={ref}
      />
      {showAddButton && <InputAddButton label={inputProps.label} onAddButton={onAddButton} />}
    </View>
  );
});

type InputFieldBoxProps = InputFieldProps & {
  expanded: boolean;
  expandedHeight?: number;
};

export const InputFieldBox = forwardRef<InputFieldRef, InputFieldBoxProps>(function InputFieldBox(
  { expanded, expandedHeight = 250, ...props },
  ref
) {
  const styles = getInputFieldStyles(props.showClearButton);

  let height;
  if (expanded) {
    height = expandedHeight;
  }

  const paddingBottom = expanded ? BOX_INPUT_PADDING_TOP : 0;

  return (
    <InputField
      inputStyle={{ ...styles.input, ...styles.boxInputOverrides, height, paddingBottom }}
      // Originally, the intention was to pass the expanded prop into the InputField multiline prop.
      // However, the React Native TextInput component has a bug in 0.76.6 where changing the multiline prop
      // will cause entered text to disappear on iOS, so multiline is set to true to get around this issue.
      // To prohibit entering newline characters when not expanded, provide a function to onChangeText when calling
      // this component that will only update the value if the changed text does not end with a newline character.
      multiline={true}
      returnKeyType='done'
      {...props}
      ref={ref}
    />
  );
});

function getInputFieldStyles(showClearButton: InputFieldProps['showClearButton']) {
  return StyleSheet.create({
    inputFieldContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start'
    },
    container: {
      flex: 1,
      paddingHorizontal: 0
    },
    inputContainer: {
      backgroundColor: FmColors.lightBackground,
      borderColor: FmColors.mediumGray50,
      borderWidth: 1,
      paddingVertical: 3,
      alignSelf: 'center',
      borderRadius: FM_ROUNDED_CORNER_RADIUS,
      paddingLeft: 15,
      paddingRight: showClearButton ? 45 : 15,
      marginTop: 5
    },
    error: {
      height: 0
    },
    input: {
      fontFamily: 'AlbertSans-Medium',
      color: FmColors.mediumGray100
    },
    boxInputOverrides: {
      textAlignVertical: 'top',
      paddingTop: BOX_INPUT_PADDING_TOP
    },
    clearButtonContainer: {
      position: 'absolute',
      top: -1,
      right: 10
    },
    inputFieldLabelText
  });
}

const BOX_INPUT_PADDING_TOP = 9;

export const inputFieldLabelText = {
  alignSelf: 'flex-start',
  marginHorizontal: 15,
  color: FmColors.darkGray,
  fontWeight: 'bold',
  fontSize: getCurrentUser().getMediumFontSize(),
  textTransform: 'uppercase'
} satisfies FmTextStyle;

type InputAddButtonProps = {
  label: InputFieldProps['label'];
  onAddButton: InputFieldProps['onAddButton'];
};

function InputAddButton({ label, onAddButton }: InputAddButtonProps) {
  return (
    <View style={getInputAddButtonStyles(label).container}>
      <FmPlusIcon
        onPress={() => {
          onAddButton?.();
        }}
        size={30}
      />
    </View>
  );
}

function getInputAddButtonStyles(label: InputAddButtonProps['label']) {
  return StyleSheet.create({
    container: {
      top: label ? 30.5 : 5,
      borderColor: FmColors.accent,
      borderRadius: FM_ROUNDED_CORNER_RADIUS,
      borderWidth: 3,
      height: FM_BUTTON_HEIGHT,
      width: FM_BUTTON_HEIGHT,
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: 10
    }
  });
}
