import { Entypo, FontAwesome6, Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';

import { FmColors } from '@/fishMeet/styles/fmColors';
import { type IconProps } from '@expo/vector-icons/build/createIconSet';
import React from 'react';
import { BorderlessButton, type BorderlessButtonProps } from 'react-native-gesture-handler';
import { useNavigation } from '@react-navigation/native';

const FM_ICON_COLOR = FmColors.mediumGray100;
const FM_NAV_ICON_COLOR = FmColors.darkGray;
const FM_ICON_SIZE = 24;

type FmIconButtonProps = {
  size?: number;
  style?: BorderlessButtonProps['style'];
  onPress?: (() => void) | undefined;
  enabled?: boolean;
};

// Wrap icons in BorderlessButton component for visual feedback when pressed
function useWrapBorderlessButton(props: FmIconButtonProps, children: React.ReactNode) {
  return (
    <BorderlessButton
      hitSlop={5}
      style={props.style}
      onPress={() => {
        props.onPress?.();
      }}
      enabled={props.enabled ?? true}>
      {children}
    </BorderlessButton>
  );
}

export function FmCloseIcon(props: FmIconButtonProps) {
  return useWrapBorderlessButton(props, <Ionicons name='close' color={FM_ICON_COLOR} size={props.size ?? 20} />);
}

export function FmPlusIcon(props: FmIconButtonProps) {
  return useWrapBorderlessButton(props, <Entypo name='plus' color={FM_ICON_COLOR} size={props.size ?? FM_ICON_SIZE} />);
}

export function FmSearchIcon(props: FmIconButtonProps) {
  return useWrapBorderlessButton(
    props,
    <Ionicons name='search-sharp' color={FM_ICON_COLOR} size={props.size ?? FM_ICON_SIZE} />
  );
}

export function FmSendIcon(props: FmIconProps<'send'>) {
  return <MaterialCommunityIcons name='send' color={FmColors.darkGray} size={props.size ?? FM_ICON_SIZE} {...props} />;
}

//////////////////////////
// Navigation buttons - has default navigation behavior

export function FmBackNavIcon(props: FmIconButtonProps) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const navigation = useNavigation<any>();

  return useWrapBorderlessButton(
    {
      ...props,
      onPress:
        props.onPress ??
        (() => {
          navigation.pop();
        }),
      style: [props.style, { marginRight: 20 }] // Prevent title from being too close to icon
    },
    <FontAwesome6 name='arrow-left-long' color={FM_NAV_ICON_COLOR} size={props.size ?? FM_ICON_SIZE} />
  );
}

//////////////////////////
// Checkbox icons are not wrapped in BorderlessButton as they are not meant to have visual feedback

type FmIconProps<T extends string> = Omit<IconProps<T>, 'name'>;

export function FmCheckBoxUncheckedIcon(props: FmIconProps<'checkbox-blank-circle-outline'>) {
  return (
    <MaterialCommunityIcons
      name='checkbox-blank-circle-outline'
      color={FmColors.accent}
      size={FM_ICON_SIZE}
      {...props}
    />
  );
}

export function FmCheckBoxCheckedIcon(props: FmIconProps<'checkbox-blank-circle'>) {
  return <MaterialCommunityIcons name='checkbox-blank-circle' color={FmColors.accent} size={FM_ICON_SIZE} {...props} />;
}
