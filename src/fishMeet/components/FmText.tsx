/* eslint-disable react-native/no-unused-styles */

import { StyleSheet, Text, TextProps, TextStyle } from 'react-native';

import { FmColors } from '@/fishMeet/styles/fmColors';
import React from 'react';

export type FmTextStyle = Omit<TextStyle, 'fontFamily' | 'fontWeight'> & {
  fontWeight?: 'normal' | 'bold';
};

export type FmTextProps = Omit<TextProps, 'style'> & {
  style?: FmTextStyle;
};

export function FmText(props: FmTextProps) {
  const style = getFmTextStyles(props.style);

  return (
    <Text {...props} style={style.fmText}>
      {props.children}
    </Text>
  );
}

function getFmTextStyles(style: FmTextProps['style']) {
  return StyleSheet.create({
    fmText: {
      color: FmColors.mediumGray100,
      ...style,
      // Set default lineheight to prevent Chinese text from being cut off due to vertical misalignment
      lineHeight: style?.lineHeight ?? (style?.fontSize ?? 0) + 4,
      // Custom fonts do not handle fontWeight well, so change fontFamily based on fontWeight
      fontFamily: style?.fontWeight === 'bold' ? 'AlbertSans-Bold' : 'AlbertSans-Medium',
      fontWeight: undefined // Always set to undefined to prevent style problems with custom fonts
    }
  });
}
