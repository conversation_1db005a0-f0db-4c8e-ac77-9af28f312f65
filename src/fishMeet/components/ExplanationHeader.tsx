/* eslint-disable react-native/no-unused-styles */

import { StyleSheet, View } from 'react-native';

import { FmColors } from '@/fishMeet/styles/fmColors';
import { FmText } from '@/fishMeet/components/FmText';
import React from 'react';
import { getCurrentUser } from '@/utils/user';

type ExplanationHeaderProps = {
  titleText?: string;
  explanationText?: string;
  titleFontSize?: number;
  explanationFontSize?: number;
};

export function ExplanationHeader({
  titleText,
  explanationText,
  titleFontSize,
  explanationFontSize
}: ExplanationHeaderProps) {
  const styles = getExplanationHeaderStyles(titleFontSize, explanationFontSize);

  return (
    <View style={styles.explanationHeaderContainer}>
      <FmText style={styles.titleText}>{titleText}</FmText>
      <FmText style={styles.explanationText}>{explanationText}</FmText>
      <View style={styles.sectionSpacer} />
    </View>
  );
}

function getExplanationHeaderStyles(
  titleFontSize: ExplanationHeaderProps['titleFontSize'] = getCurrentUser().getX2LargeFontSize(),
  explanationFontSize: ExplanationHeaderProps['explanationFontSize'] = getCurrentUser().getMediumFontSize()
) {
  return StyleSheet.create({
    explanationHeaderContainer: {
      alignSelf: 'stretch'
    },
    titleText: {
      fontWeight: 'bold',
      color: FmColors.accent,
      fontSize: titleFontSize
    },
    explanationText: {
      marginTop: 20,
      fontSize: explanationFontSize
    },
    sectionSpacer: {
      marginTop: 30
    }
  });
}
