import { FmCheckBoxCheckedIcon, FmCheckBoxUncheckedIcon } from '@/fishMeet/components/FmIcons';
import { StyleSheet, View } from 'react-native';

import { Avatar2 } from '@/components/Avatar2';
import { FmText } from '@/fishMeet/components/FmText';
import React from 'react';
import { RectButton } from 'react-native-gesture-handler';
import { getCurrentUser } from '@/utils/user';

type AvatarAndNameListItemProps = {
  userId: number;
  text: string;
  onPress: () => void;
  showCheckBox?: boolean;
  checkBoxToggled?: boolean;
};

export function AvatarAndNameListItem({
  userId,
  text,
  onPress,
  showCheckBox = false,
  checkBoxToggled
}: AvatarAndNameListItemProps) {
  return (
    <RectButton
      onPress={() => {
        onPress();
      }}>
      <View style={styles.memberListItemContainer}>
        {showCheckBox &&
          (checkBoxToggled ? (
            <FmCheckBoxCheckedIcon style={styles.memberListCheckBox} />
          ) : (
            <FmCheckBoxUncheckedIcon style={styles.memberListCheckBox} />
          ))}
        <Avatar2 userId={userId} />
        <FmText style={styles.memberListText}>{text}</FmText>
      </View>
    </RectButton>
  );
}

const styles = StyleSheet.create({
  memberListItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
    minHeight: 60
  },
  memberListCheckBox: {
    marginRight: 15
  },
  memberListText: {
    flex: 1,
    fontSize: getCurrentUser().getMediumFontSize(),
    marginLeft: 15
  }
});
