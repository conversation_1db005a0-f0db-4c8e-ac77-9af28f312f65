/* eslint-disable react-native/no-unused-styles */

import { StyleSheet, View } from 'react-native';

import { FM_ROUNDED_CORNER_RADIUS } from '@/fishMeet/styles/fmStyles';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { FmText } from '@/fishMeet/components/FmText';
import React from 'react';
import { RectButton } from 'react-native-gesture-handler';
import { RedDot } from '@/components';
import { getCurrentUser } from '@/utils/user';

type FmChipProps = {
  page: number;
  pageIndex: number;
  title: string;
  onPress: () => void;
  width: number;
  showRedDot?: boolean;
};

export function FmChip({ page, pageIndex, title, onPress, width, showRedDot }: FmChipProps) {
  const isSelected = page === pageIndex;
  const styles = getFmChipStyles(width, isSelected);

  return (
    <RectButton style={styles.button} onPress={() => onPress()}>
      <View style={styles.chipContainer}>
        <FmText style={styles.title}>{title}</FmText>
      </View>
      {showRedDot ? <RedDot right={3} top={-3} /> : null}
    </RectButton>
  );
}

function getFmChipStyles(width: FmChipProps['width'], isSelected: boolean) {
  return StyleSheet.create({
    button: {
      backgroundColor: isSelected ? FmColors.accent : FmColors.lightAccent,
      borderRadius: FM_ROUNDED_CORNER_RADIUS,
      paddingVertical: 5,
      paddingHorizontal: 20,
      width
    },
    chipContainer: {
      alignItems: 'center'
    },
    title: {
      fontSize: getCurrentUser().getMediumFontSize(),
      fontWeight: 'bold',
      color: FmColors.darkGray
    }
  });
}
