// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<CreateAccountScreen /> UI renders correctly 1`] = `
<RCTScrollView
  keyboardShouldPersistTaps="handled"
  showsVerticalScrollIndicator={false}
>
  <View>
    <View
      style={
        {
          "alignItems": "center",
          "backgroundColor": "#e7eef2",
          "flex": 1,
          "paddingHorizontal": 15,
        }
      }
    >
      <View
        style={
          {
            "alignSelf": "stretch",
          }
        }
      >
        <Text
          style={
            {
              "color": "#fe9c75",
              "fontFamily": "AlbertSans-Bold",
              "fontSize": 24,
              "fontWeight": undefined,
              "lineHeight": 28,
            }
          }
        >
          Welcome to FishMeet!
Please log in or sign up.
        </Text>
        <Text
          style={
            {
              "color": "#424350",
              "fontFamily": "AlbertSans-Medium",
              "fontSize": 18,
              "fontWeight": undefined,
              "lineHeight": 22,
              "marginTop": 20,
            }
          }
        >
          If you already have an iDigest account, you can use the same information to log in.
        </Text>
        <View
          style={
            {
              "marginTop": 30,
            }
          }
        />
      </View>
      <View
        style={
          {
            "alignItems": "flex-start",
            "flexDirection": "row",
          }
        }
      >
        <View
          style={
            {
              "flex": 1,
              "paddingHorizontal": 0,
              "width": "100%",
            }
          }
          testID="RNE__Input__view-wrapper"
        >
          <Text
            style={
              {
                "alignSelf": "flex-start",
                "color": "#28262c",
                "fontFamily": "AlbertSans-Bold",
                "fontSize": 18,
                "fontWeight": undefined,
                "lineHeight": 22,
                "marginHorizontal": 15,
                "textTransform": "uppercase",
              }
            }
          >
            Display Name
          </Text>
          <View
            collapsable={false}
            style={
              {
                "alignItems": "center",
                "alignSelf": "center",
                "backgroundColor": "#f9fdfe",
                "borderBottomWidth": 1,
                "borderColor": "#42435080",
                "borderRadius": 24,
                "borderWidth": 1,
                "flexDirection": "row",
                "marginTop": 5,
                "paddingLeft": 15,
                "paddingRight": 15,
                "paddingVertical": 3,
                "transform": [
                  {
                    "translateX": 0,
                  },
                ],
              }
            }
          >
            <TextInput
              autoCapitalize="none"
              autoCorrect={false}
              defaultValue=""
              editable={true}
              onChangeText={[Function]}
              onSubmitEditing={[Function]}
              placeholder="For example: Lamb"
              placeholderTextColor="#42435080"
              returnKeyType="next"
              style={
                {
                  "color": "#424350",
                  "flex": 1,
                  "fontFamily": "AlbertSans-Medium",
                  "fontSize": 18,
                  "minHeight": 40,
                }
              }
              submitBehavior="submit"
              testID="RNE__Input__text-input"
              underlineColorAndroid="transparent"
            />
          </View>
          <Text
            style={
              {
                "color": "#ff190c",
                "fontSize": 12,
                "height": 0,
                "margin": 5,
              }
            }
          />
        </View>
      </View>
      <View
        style={
          {
            "marginTop": 20,
          }
        }
      />
      <View
        style={
          {
            "alignItems": "flex-start",
            "flexDirection": "row",
          }
        }
      >
        <View
          style={
            {
              "flex": 1,
              "paddingHorizontal": 0,
              "width": "100%",
            }
          }
          testID="RNE__Input__view-wrapper"
        >
          <Text
            style={
              {
                "alignSelf": "flex-start",
                "color": "#28262c",
                "fontFamily": "AlbertSans-Bold",
                "fontSize": 18,
                "fontWeight": undefined,
                "lineHeight": 22,
                "marginHorizontal": 15,
                "textTransform": "uppercase",
              }
            }
          >
            Email
          </Text>
          <View
            collapsable={false}
            style={
              {
                "alignItems": "center",
                "alignSelf": "center",
                "backgroundColor": "#f9fdfe",
                "borderBottomWidth": 1,
                "borderColor": "#42435080",
                "borderRadius": 24,
                "borderWidth": 1,
                "flexDirection": "row",
                "marginTop": 5,
                "paddingLeft": 15,
                "paddingRight": 15,
                "paddingVertical": 3,
                "transform": [
                  {
                    "translateX": 0,
                  },
                ],
              }
            }
          >
            <TextInput
              autoCapitalize="none"
              autoCorrect={false}
              defaultValue=""
              editable={true}
              onChangeText={[Function]}
              onSubmitEditing={[Function]}
              placeholder="Ex: <EMAIL>"
              placeholderTextColor="#42435080"
              returnKeyType="next"
              style={
                {
                  "color": "#424350",
                  "flex": 1,
                  "fontFamily": "AlbertSans-Medium",
                  "fontSize": 18,
                  "minHeight": 40,
                }
              }
              submitBehavior="submit"
              testID="RNE__Input__text-input"
              underlineColorAndroid="transparent"
            />
          </View>
          <Text
            style={
              {
                "color": "#ff190c",
                "fontSize": 12,
                "height": 0,
                "margin": 5,
              }
            }
          />
        </View>
      </View>
      <View
        style={
          {
            "marginTop": 20,
          }
        }
      />
      <View
        style={
          {
            "alignItems": "flex-start",
            "flexDirection": "row",
          }
        }
      >
        <View
          style={
            {
              "flex": 1,
              "paddingHorizontal": 0,
              "width": "100%",
            }
          }
          testID="RNE__Input__view-wrapper"
        >
          <Text
            style={
              {
                "alignSelf": "flex-start",
                "color": "#28262c",
                "fontFamily": "AlbertSans-Bold",
                "fontSize": 18,
                "fontWeight": undefined,
                "lineHeight": 22,
                "marginHorizontal": 15,
                "textTransform": "uppercase",
              }
            }
          >
            Password
          </Text>
          <View
            collapsable={false}
            style={
              {
                "alignItems": "center",
                "alignSelf": "center",
                "backgroundColor": "#f9fdfe",
                "borderBottomWidth": 1,
                "borderColor": "#42435080",
                "borderRadius": 24,
                "borderWidth": 1,
                "flexDirection": "row",
                "marginTop": 5,
                "paddingLeft": 15,
                "paddingRight": 15,
                "paddingVertical": 3,
                "transform": [
                  {
                    "translateX": 0,
                  },
                ],
              }
            }
          >
            <TextInput
              autoCapitalize="none"
              autoCorrect={false}
              defaultValue=""
              editable={true}
              onChangeText={[Function]}
              onSubmitEditing={[Function]}
              placeholder="Password"
              placeholderTextColor="#42435080"
              returnKeyType="go"
              secureTextEntry={true}
              style={
                {
                  "color": "#424350",
                  "flex": 1,
                  "fontFamily": "AlbertSans-Medium",
                  "fontSize": 18,
                  "minHeight": 40,
                }
              }
              submitBehavior="submit"
              testID="RNE__Input__text-input"
              underlineColorAndroid="transparent"
            />
          </View>
          <Text
            style={
              {
                "color": "#ff190c",
                "fontSize": 12,
                "height": 0,
                "margin": 5,
              }
            }
          />
        </View>
      </View>
      <View
        style={
          {
            "flexDirection": "row",
          }
        }
      >
        <Text />
        <View
          style={
            {
              "flex": 1,
              "justifyContent": "center",
            }
          }
        >
          <Text
            style={
              {
                "color": "#424350",
                "fontFamily": "AlbertSans-Medium",
                "fontSize": 18,
                "fontWeight": undefined,
                "lineHeight": 22,
              }
            }
          >
            <Text
              onPress={[Function]}
              style={
                {
                  "color": "#424350",
                  "fontFamily": "AlbertSans-Medium",
                  "fontSize": 18,
                  "fontWeight": undefined,
                  "lineHeight": 22,
                }
              }
            >
              I agree to the 
            </Text>
            <Text
              onPress={[Function]}
              style={
                {
                  "color": "#008bf9",
                  "fontFamily": "AlbertSans-Medium",
                  "fontSize": 18,
                  "fontWeight": undefined,
                  "lineHeight": 22,
                }
              }
            >
              Terms and Condition
            </Text>
             & 
            <Text
              onPress={[Function]}
              style={
                {
                  "color": "#008bf9",
                  "fontFamily": "AlbertSans-Medium",
                  "fontSize": 18,
                  "fontWeight": undefined,
                  "lineHeight": 22,
                }
              }
            >
              Privacy
            </Text>
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "marginTop": 20,
          }
        }
      >
        <RNGestureHandlerButton
          activeOpacity={0.105}
          collapsable={false}
          delayLongPress={600}
          handlerTag={1}
          handlerType="NativeViewGestureHandler"
          innerRef={null}
          onActiveStateChange={[Function]}
          onGestureEvent={[Function]}
          onGestureHandlerEvent={[Function]}
          onGestureHandlerStateChange={[Function]}
          onHandlerStateChange={[Function]}
          onPress={[Function]}
          style={
            [
              {
                "backgroundColor": "#fe9c75",
                "borderRadius": 24,
                "height": 48,
                "marginVertical": 10,
                "width": 500,
              },
              {
                "cursor": undefined,
              },
            ]
          }
          testID=""
          underlayColor="black"
        >
          <View
            collapsable={false}
            style={
              {
                "backgroundColor": "black",
                "borderBottomLeftRadius": undefined,
                "borderBottomRightRadius": undefined,
                "borderRadius": 24,
                "borderTopLeftRadius": undefined,
                "borderTopRightRadius": undefined,
                "bottom": 0,
                "left": 0,
                "opacity": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              }
            }
          />
          <View
            style={
              {
                "flex": 1,
                "justifyContent": "center",
              }
            }
          >
            <Text
              numberOfLines={1}
              style={
                {
                  "color": "#28262c",
                  "fontFamily": "AlbertSans-Bold",
                  "fontSize": 18,
                  "fontWeight": undefined,
                  "lineHeight": 24.5,
                  "textAlign": "center",
                  "textTransform": "uppercase",
                }
              }
            >
              Sign up
            </Text>
          </View>
        </RNGestureHandlerButton>
      </View>
    </View>
  </View>
</RCTScrollView>
`;
