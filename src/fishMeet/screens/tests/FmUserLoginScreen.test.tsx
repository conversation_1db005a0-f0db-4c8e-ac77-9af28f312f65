import { Navigation<PERSON>ontainer, useNavigation } from '@react-navigation/native';
import { RenderResult, render } from '@testing-library/react-native';

import React from 'react';
import { TYPES } from '@/ioc/types';
import UserLoginScreen from '@/screens/UserLoginScreen';
import { checkToShowGoogleSignin } from '@/utils/GoogleSignInHelper';
import { dataServices } from '@/ioc/interfaces';
import { iDigestContainer } from '@/ioc/inversify.config';
import { mockDS } from '@/dataStorage/dataServicesMock';

jest.mock('@/utils/deviceOrAppType', () => ({
  ...jest.requireActual('@/utils/deviceOrAppType'),
  isFishMeet: true
}));

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: jest.fn()
}));
jest.mock('@react-native-google-signin/google-signin', () => ({
  GoogleSignin: {
    configure: jest.fn()
  },
  GoogleSigninButton: Object.assign(() => 'GoogleSigninButton', {
    Size: {
      Wide: 'Wide'
    },
    Color: {
      Dark: 'Dark'
    }
  })
}));

beforeAll(async () => {
  // rebind the data service to the mock object
  iDigestContainer.snapshot();
  iDigestContainer.rebind<dataServices>(TYPES.dataServices).to(mockDS).inSingletonScope;
  globalThis.dsObject = iDigestContainer.get<dataServices>(TYPES.dataServices);
  (globalThis.dsObject.getCountryFromIP as jest.Mock).mockResolvedValue('us');
  await checkToShowGoogleSignin();
});

afterAll(async () => {
  // restore the real data service object
  iDigestContainer.restore();
  globalThis.dsObject = iDigestContainer.get<dataServices>(TYPES.dataServices);
});

describe('<UserLoginScreen />', () => {
  let tree: RenderResult | null = null;

  afterEach(() => {
    if (tree) {
      tree.unmount();
      tree = null;
    }
  });

  it('English UI renders correctly', () => {
    (useNavigation as jest.Mock).mockReturnValue({
      navigate: jest.fn(),
      addListener: jest.fn(),
      removeListener: jest.fn(),
      dangerouslyGetParent: jest.fn(),
      reset: jest.fn(),
      canGoBack: jest.fn(),
      setOptions: jest.fn()
    });

    tree = render(
      <NavigationContainer>
        <UserLoginScreen />
      </NavigationContainer>
    );
    console.log('UserLoginScreen render is done.');
    expect(tree.toJSON()).toMatchSnapshot();
  });
});
