import { AppContext } from '@/context/AppContext';
import CreateAccountScreen from '@/screens/CreateAccountScreen';
import React from 'react';
import { render } from '@testing-library/react-native';

jest.mock('@/utils/deviceOrAppType', () => ({
  ...jest.requireActual('@/utils/deviceOrAppType'),
  isFishMeet: true
}));

describe('<CreateAccountScreen />', () => {
  it('UI renders correctly', async () => {
    const mockNavigation = {
      navigate: jest.fn(),
      addListener: jest.fn().mockImplementation((_event, callback) => callback())
    };
    const appContextValue = {
      insets: { top: 0, bottom: 0, left: 0, right: 0 },
      userProfile: {
        displayName: 'Mock User'
      }
    };

    const tree = render(
      <AppContext.Provider value={appContextValue}>
        <CreateAccountScreen navigation={mockNavigation} />
      </AppContext.Provider>
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
