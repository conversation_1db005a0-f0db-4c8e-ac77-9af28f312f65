import React from 'react';
import { View } from 'react-native';
import SettingsList from '@/components/SettingsList';
import { getCurrentUser } from '@/utils/user';
import { getLoginInfoSettingScreenStyles } from '@/fishMeet/styles/screens/loginInfoSettingScreenStyles';
import { i18n2 } from '@/utils/i18n2';
import { Button } from '@/components';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function LoginInfoSettingScreen({ navigation }: any) {
  const fmStyles = getLoginInfoSettingScreenStyles();
  const currentUser = getCurrentUser();

  useSetNavigationOptions({
    title: i18n2.t('LoginInfoSettingScreen.Settings')
  });

  const fontSize: number = currentUser.getSmallFontSize();

  function renderPlaceholderList(fontSize: number) {
    return (
      <SettingsList isFishMeet={true} underlayColor={FmColors.background} borderColor={FmColors.mediumGray50}>
        <SettingsList.Item
          itemBoxStyle={fmStyles.itemBox}
          titleBoxStyle={fmStyles.titleBox}
          title='Lorem Ipsum'
          titleStyle={[fmStyles.itemTitle, { fontSize }]}
          icon={<View style={fmStyles.imagePlaceholder} />}
          rightSideContent={
            <View style={{ flex: 0, alignSelf: 'flex-start' }}>
              <Button
                title={i18n2.t('LoginInfoSettingScreen.Link')}
                onPress={() => {
                  // TBD
                }}
                style={fmStyles.linkButton}
                textStyle={fmStyles.linkButtonText}
              />
            </View>
          }
          hasNavArrow={false}
        />
        <SettingsList.Item
          itemBoxStyle={fmStyles.itemBox}
          titleBoxStyle={fmStyles.titleBox}
          title='Lorem Ipsum'
          titleStyle={[fmStyles.itemTitle, { fontSize }]}
          icon={<View style={fmStyles.imagePlaceholder} />}
          rightSideContent={
            <View style={{ flex: 0, alignSelf: 'flex-start' }}>
              <Button
                title={i18n2.t('LoginInfoSettingScreen.Link')}
                onPress={() => {
                  // TBD
                }}
                style={fmStyles.linkButton}
                textStyle={fmStyles.linkButtonText}
              />
            </View>
          }
          hasNavArrow={false}
        />
        <SettingsList.Item
          itemBoxStyle={fmStyles.itemBox}
          titleBoxStyle={fmStyles.titleBox}
          title='Lorem Ipsum'
          titleStyle={[fmStyles.itemTitle, { fontSize }]}
          icon={<View style={fmStyles.imagePlaceholder} />}
          rightSideContent={
            <View style={{ flex: 0, alignSelf: 'flex-start' }}>
              <Button
                title={i18n2.t('LoginInfoSettingScreen.Link')}
                onPress={() => {
                  // TBD
                }}
                style={fmStyles.linkButton}
                textStyle={fmStyles.linkButtonText}
              />
            </View>
          }
          hasNavArrow={false}
        />
        <SettingsList.Item
          itemBoxStyle={fmStyles.itemBox}
          titleBoxStyle={fmStyles.titleBox}
          title='Lorem Ipsum'
          titleStyle={[fmStyles.itemTitle, { fontSize }]}
          icon={<View style={fmStyles.imagePlaceholder} />}
          rightSideContent={
            <View style={{ flex: 0, alignSelf: 'flex-start' }}>
              <Button
                title={i18n2.t('LoginInfoSettingScreen.Link')}
                onPress={() => {
                  // TBD
                }}
                style={fmStyles.linkButton}
                textStyle={fmStyles.linkButtonText}
              />
            </View>
          }
          hasNavArrow={false}
        />
        <SettingsList.Item
          itemBoxStyle={fmStyles.itemBox}
          titleBoxStyle={fmStyles.titleBox}
          title='Lorem Ipsum'
          titleStyle={[fmStyles.itemTitle, { fontSize }]}
          icon={<View style={fmStyles.imagePlaceholder} />}
          rightSideContent={
            <View style={{ flex: 0, alignSelf: 'flex-start' }}>
              <Button
                title={i18n2.t('LoginInfoSettingScreen.Link')}
                onPress={() => {
                  // TBD
                }}
                style={fmStyles.linkButton}
                textStyle={fmStyles.linkButtonText}
              />
            </View>
          }
          hasNavArrow={false}
        />
      </SettingsList>
    );
  }

  return (
    <View style={fmStyles.container}>
      <View style={fmStyles.buttonContainer}>
        <Button
          style={fmStyles.button}
          testID='EmailPasswordButton'
          title={i18n2.t('LoginInfoSettingScreen.EmailAndPassword')}
          textStyle={fmStyles.buttonText}
          onPress={() => navigation.navigate('UpdatePassword', { isFishMeet: true })}
        />
      </View>
      <View style={fmStyles.groupContainer}>{renderPlaceholderList(fontSize)}</View>
    </View>
  );
}

export default LoginInfoSettingScreen;
