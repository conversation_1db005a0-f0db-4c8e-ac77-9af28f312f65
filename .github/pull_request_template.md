## Description

Please include a summary of the change or which issue is fixed.

## Type of change

- [ ] New feature (non-breaking change which adds functionality)
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)

## Checklist:

- [ ] I ran all the manual tests listed in manualBVT.md
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation where needed
- [ ] I conducted basic QA to assure all features are working
- [ ] My implementation match both desktop and mobile UX design
- [ ] I tested responsive for mobile and tablet resolutions
