name: Pull Request size label

on:
  pull_request:
    branches: ['master']

jobs:
  size-label:
    runs-on: ubuntu-latest
    steps:
      - name: size-label
        uses: 'pascalgn/size-label-action@v0.4.3'
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        with:
          sizes: >
            {
              "0": "XS",
              "20": "S",
              "50": "M",
              "250": "Too Large"
            }
