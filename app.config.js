const baseConfig = {
  owner: 'gracetechservices',
  platforms: ['ios', 'android'],
  runtimeVersion: '1.0.3',
  updates: {
    enabled: true,
    fallbackToCacheTimeout: 20000
  },
  ios: {
    supportsTablet: true,
    usesIcloudStorage: true,
    usesAppleSignIn: true,
    requireFullScreen: true,
    infoPlist: {
      ITSAppUsesNonExemptEncryption: false,
      LSApplicationQueriesSchemes: ['org.jitsi.meet', 'org.gtinternational.meet', 'zoomus', 'skype', 'msteams'],
      LSMinimumSystemVersion: '12.0.0',
      NSAppTransportSecurity: {
        NSAllowsArbitraryLoads: true,
        NSExceptionDomains: {
          '************': {
            NSIncludesSubdomains: true,
            NSTemporaryExceptionAllowsInsecureHTTPLoads: true
          },
          'eioqntachgyh.online': {
            NSIncludesSubdomains: true,
            NSTemporaryExceptionAllowsInsecureHTTPLoads: true
          }
        }
      },
      NSCalendarsUsageDescription: 'See your scheduled meetings in the app.',
      UIBackgroundModes: ['audio', 'fetch'],
      UIViewControllerBasedStatusBarAppearance: 'NO'
    }
  },
  android: {
    permissions: [
      'ACCESS_NETWORK_STATE',
      'ACCESS_WIFI_STATE',
      'BLUETOOTH',
      'CAMERA',
      'FOREGROUND_SERVICE',
      'FOREGROUND_SERVICE_MEDIA_PLAYBACK',
      'FOREGROUND_SERVICE_MEDIA_PROJECTION',
      'INTERNET',
      'MANAGE_OWN_CALLS',
      'MODIFY_AUDIO_SETTINGS',
      'READ_EXTERNAL_STORAGE',
      'RECEIVE_BOOT_COMPLETED',
      'RECORD_AUDIO',
      'SCHEDULE_EXACT_ALARM',
      'VIBRATE',
      'WAKE_LOCK',
      'WRITE_EXTERNAL_STORAGE',
      'com.majeur.launcher.permission.UPDATE_BADGE',
      'com.google.android.c2dm.permission.RECEIVE'
    ]
  },
  plugins: [
    './src/plugins/withDisableCleartextTraffic/withDisableCleartextTraffic.js',
    [
      'expo-av',
      {
        microphonePermission: 'This app uses the microphone to capture audio clip so that you can send it through chat.'
      }
    ],
    [
      'expo-build-properties',
      {
        android: {
          enableProguardInReleaseBuilds: false
        }
      }
    ],
    [
      'expo-camera',
      {
        cameraPermission:
          'This app uses the camera to capture images to set up your avatar and enable you to send images through chat.',
        microphonePermission:
          'This app uses the microphone to capture audio clip so that you can send it through chat.',
        recordAudioAndroid: true
      }
    ],
    [
      'expo-document-picker',
      {
        iCloudContainerEnvironment: 'Production'
      }
    ],
    [
      './src/plugins/withAndroidShareExtension/index',
      {
        androidIntentFilters: ['text/*', 'image/*', 'video/*', '*/*']
      }
    ],
    [
      'expo-config-plugin-ios-share-extension',
      {
        activationRules: {
          NSExtensionActivationSupportsText: true,
          NSExtensionActivationSupportsWebURLWithMaxCount: 1,
          NSExtensionActivationSupportsWebPageWithMaxCount: 1,
          NSExtensionActivationSupportsImageWithMaxCount: 1,
          NSExtensionActivationSupportsMovieWithMaxCount: 1,
          NSExtensionActivationSupportsFileWithMaxCount: 1
        }
      }
    ],
    'expo-localization',
    'expo-font',
    [
      '@config-plugins/detox',
      {
        subdomains: '*'
      }
    ],
    'expo-asset',
    '@config-plugins/react-native-webrtc',
    [
      'expo-screen-orientation',
      {
        initialOrientation: 'PORTRAIT_UP'
      }
    ],
    './src/plugins/withAndroidPiPSupport/withAndroidPiPSupport.js',
    './src/plugins/withAndroidPiPSupport/withAndroidPiPStateChange.js',
    './src/plugins/withIosCopyJitsiMeetSoundsScript/withIosCopyJitsiMeetSoundsScript.js',
    './src/plugins/withAndroidConfigureGradle/withAndroidConfigureGradle.js'
  ],
  newArchEnabled: false
};

module.exports = () => {
  if (process.env.APP === 'fishMeet') {
    return {
      ...baseConfig,
      name: '渔友会',
      slug: 'fishmeet',
      version: '1.0.1',
      icon: './src/fishMeet/assets/images/fishmeet_logo.png',
      updates: {
        ...baseConfig.updates,
        url: 'https://u.expo.dev/7f026bb1-92c9-48bd-9928-4f05f8430d63',
        assetPatternsToBeBundled: ['./src/**/*']
      },
      ios: {
        ...baseConfig.ios,
        icon: './src/fishMeet/assets/images/fishmeet_logo.png',
        buildNumber: '7',
        bundleIdentifier: 'org.gtinternational.meet',
        infoPlist: {
          ...baseConfig.ios.infoPlist,
          CFBundleName: '渔友会'
        },
        googleServicesFile: './src/fishMeet/GoogleService-Info.plist'
      },
      android: {
        ...baseConfig.android,
        googleServicesFile: './src/fishMeet/google-services.json',
        package: 'org.gtinternational.fishmeet',
        versionCode: 2
      },
      scheme: 'fishmeet',
      extra: {
        eas: {
          projectId: '7f026bb1-92c9-48bd-9928-4f05f8430d63'
        },
        webClientId: '418260164099-1s4hndct6bg9gpp7gqta7ft7fgkqd35t.apps.googleusercontent.com'
      },
      plugins: [
        ...baseConfig.plugins,
        [
          'expo-splash-screen',
          {
            backgroundColor: '#f9fdfe',
            image: './src/fishMeet/assets/images/fishmeet_logo.png',
            imageWidth: 200
          }
        ],
        [
          '@sentry/react-native/expo',
          {
            organization: 'gracetech-international',
            project: 'fishmeet',
            url: 'https://sentry.io/'
          }
        ]
      ]
    };
  } else {
    return {
      ...baseConfig,
      name: '小鸽子',
      slug: 'iDigest',
      version: '3.1.43',
      icon: './src/assets/images/icon_android.png',
      updates: {
        ...baseConfig.updates,
        url: 'https://u.expo.dev/9c52691d-0f96-4c61-8b86-74126c919a70',
        assetPatternsToBeBundled: ['./src/assets/**/*']
      },
      ios: {
        ...baseConfig.ios,
        icon: './src/assets/images/icon_ios.png',
        buildNumber: '1',
        bundleIdentifier: 'org.gtinternational.idigest',
        infoPlist: {
          ...baseConfig.ios.infoPlist,
          CFBundleName: '小鸽子'
        },
        googleServicesFile: './GoogleService-Info.plist'
      },
      android: {
        ...baseConfig.android,
        googleServicesFile: './google-services.json',
        package: 'org.gtinternational.idigest',
        versionCode: 95
      },
      scheme: 'idigest',
      extra: {
        eas: {
          projectId: '9c52691d-0f96-4c61-8b86-74126c919a70'
        },
        webClientId: '897020363845-1pq67fpmr8g572224kheoug4u85ij7ch.apps.googleusercontent.com'
      },
      plugins: [
        ...baseConfig.plugins,
        [
          'expo-splash-screen',
          {
            backgroundColor: '#ffffff',
            imageWidth: 200,
            enableFullScreenImage_legacy: true,
            android: {
              image: './src/assets/images/icon.png'
            },
            ios: {
              image: './src/assets/images/splash.png'
            }
          }
        ],
        [
          '@sentry/react-native/expo',
          {
            organization: 'gracetech-international',
            project: 'idigest',
            url: 'https://sentry.io/'
          }
        ]
      ]
    };
  }
};
