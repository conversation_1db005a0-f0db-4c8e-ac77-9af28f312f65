{"extends": "expo/tsconfig.base", "include": ["src/**/*", "scripts/**/*", "e2e/**/*", "__mocks__/**/*"], "compilerOptions": {"incremental": true, "baseUrl": "./", "paths": {"@/*": ["src/*"]}, "module": "ESNext", "experimentalDecorators": true, "emitDecoratorMetadata": true, "strict": true, "exactOptionalPropertyTypes": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true}}