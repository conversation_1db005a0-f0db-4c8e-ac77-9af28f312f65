# App Development

## Setup

### On computer

- Install Git https://git-scm.com/downloads
- Install NodeJS https://nodejs.org/en/ (version LTS)
- Install Yarn https://yarnpkg.com/getting-started/install
- Run `git clone https://github.com/gracetech-services/iDigestApp.git` to get source code in command-line window. Check this article if you want to avoid typing in the git access token for every git command. https://stackoverflow.com/questions/8588768/how-do-i-avoid-the-specification-of-the-username-and-password-at-every-git-push
- Run `yarn install`
- Run `npx expo register` to register your own Expo developer account
- Run `npx expo login` to login with your own Expo developer account
- Let JeffYin know your Expo account email. They need to give you access to the Expo project so that you can launch the app in Expo Go.
- With Google Signin support which is using native code, we have to use Expo dev build instead of Expo Go. Please following the instructions https://docs.expo.dev/guides/local-app-development/ to set up your Expo dev client build. If you update app.config.js, you might need to run `npx expo prebuild` again to generate the right config files.

### On phone

- Install "Expo Go" app on your phone (iOS or Android, check out https://expo.io/)
- Open "Expo Go" app on your phone, sign in using your newly created Expo developer account

### Start running

- On your dev machine, you can use Android/iOS emulator. To install Android emulator, check out https://docs.expo.dev/workflow/android-studio-emulator/. To get iOS emulator, please install XCode. Once Xcode is installed, go to Settings, then go to Locations, and then select one of the Command Line Tools.
- Run `yarn start`. If using Android emulator, run `yarn start --localhost` instead.
- On your phone, in "Expo Go" app, the debug app should show up in "Development servers", click on it to launch it.
- In the Expo CLI, hit "a" or "i" to load the app in Android or iOS emulator. It'll auto install Expo Go app in the emulator.
- To launch the debugger, hit "j" in the Expo CLI. Please note for Chromebook, you need to first run "sudo apt-get install chromium" to install a chromium browser into the linux env, and then the debugger could work.

## In-App Update

- Run `yarn run update`

Note: it only update for the specific app version (expo.version in app.config.js)

## Build & Release

- Update app.config.js with newer version and versionCode
- If native code is updated, make sure the version is changed to prevent incompatible updates
- Run `yarn run release`

Note:

- iOS requires new app version (Android doesn't)
- Android require new versionCode

## For China Android users:

- Run 'yarn chinaBuild'
- Download the apk from the link at expo. Link is supplied by the above build command
- Download the private key file
- chmod go-rwx {private key file}
- scp -i {private key file} {apk file} {remote user}@{remote server}:/home/<USER>/idigest-{versionCode}.apk
- ssh -i {private key file} {remote user}@{remote server}
  From the remote ssh
- sudo cp idigest-{versionCode}.apk /var/www/html/idigest/idigest-{versionCode}.apk
- cd /var/www/html/idigest/install, sudo nano config.php, set `$apk_version` to {versionCode}

## For fishMeet build & release

- yarn fstart, to get Expo Go mode of the fishMeet
- yarn fprebuild, and then yarn fandroid|fios, to get local android or ios build of fishMeet
- eas build -p ios|android --profile -fdevelopment|fpreview|fproduction, this gives you the EAS build of fishMeet
- APP=fishMeet eas submit -p ios|android, this is to submit the fishMeet build to store

## Use Typescript

Note: All new files should be written in Typescript as .ts or .tsx files.

## Test

### Snapshot test

Please see ./src/screens/tests/UserLoginScreen.test.tsx as an example. You can read https://docs.expo.dev/develop/unit-testing/ and https://jestjs.io/docs/snapshot-testing to get more infos about snapshot test.

### E2E test

Please see ./e2e/UserLoginScreen.test.js as an example. This is a more detailed explanation https://blog.expo.dev/testing-expo-apps-with-detox-and-react-native-testing-library-7fbdbb82ac87.
If you add testID to an element in a custom component, then the testID needs to be propagated to the underlying RN components. Please see ./src/components/button.js as an example.

To set up e2e test with detox:

- go to ./android/app/src/main/AndroidManifest.xml, change  
  \<meta-data android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH" android:value="ALWAYS"/>  
  to  
  \<meta-data android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH" android:value="NEVER"/>  
  This is to avoid android avd hang when trying to launch the app.
- run: npx detox init -r jest

Then for iOS:

- brew tap wix/brew
- brew install applesimutils
- cd ios
- pod install

For Android:
Create an AVD named "pixel_4" with API level 31. And the AVD needs to be launched manually first before running detox tests.
Android release version doesn't work for Detox yet.

To run the e2e test locally:

- npx detox build -c config. Config could be ios.sim.release or android.emu.release, etc.
- for debug config, you need to start the development server by doing: yarn start
- npx detox test -c config
  Please remember to build after your code change since detox only run with native code.

## Checkin Workflow

1. Create a new branch from master and use standard naming convention for branches:  
   **github-user-name/short-description-of-the-work**  
   Try to do this for every feature/fix to avoid creating very large pull requests which will be very time consuming for reviewers.
2. After you finish your code, run `yarn lint`, `yarn format`, and add corresponding tests.
3. run: yarn e2ei (for iOS) or yarn e2ea (for Android) to make sure the e2e tests pass.
4. Commit and push your code to your feature/fix branch and create pull request to merge this branch to master branch. PR title should start with a PR type specified in https://github.com/commitizen/conventional-commit-types/blob/v3.0.0/index.json.
5. Assign someone for code review. The goal here is to learn from each other, to make sure all standards are met, that code style is respected, and, of course, to make sure code does not have any bugs.
6. If there are any questions/suggestions/fixes/changes requested from the person which is conducting code review, that person will request changes on GitHub with clear comments and the process starts again. All comments need to be resolved before the reviewer can accept the pull request.
7. After successful code review, the branch will be merged into the master branch, and the feature/fix branch will be automatically deleted.

## localization

Please don't add more strings into i18n*\*.json file. We use i18n2*\*.json files. The pattern is i18n2.t('string key'). Please take a look at ./src/screens/CreateAccountScreen.js as an example.

## Code Coverage

To get code coverage, you can run: yarn testFinal, the coverage html will be in ./coverage/cov-report/index.html

# Move code from gracetech/iDigestApp to gracetech/Nua-app repo

- rsync -aPz --exclude='.git/' --exclude='CODEOWNERS' —-exclude='google-services.json' —-exclude='GoogleService-info.plist' –-delete iDigestApp/ nua-app
- cd nua-app
- Update README.md file’s git clone URL to nua-app
- Compare the change in app.config.js and do a manual merge
- yarn install
- npx expo prebuild

# Move code from gracetech/nua-app to bsftech/Nua-app repo

- rsync -aPz --exclude='.git/' –-exclude='CODEOWNERS' –-delete gracetech-nua-app-folder/ bsftech-nua-app-folder
- cd nua-app
- Update README.md file’s git clone URL to nua-app
- Compare the change in app.config.js and do a manual merge.
- yarn install
- npx expo prebuild
