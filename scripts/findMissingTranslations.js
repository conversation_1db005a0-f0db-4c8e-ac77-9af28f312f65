const fs = require('fs');
const path = require('path');
const process = require('process');

function readJSONFile(filePath) {
  const data = fs.readFileSync(filePath, 'utf8');
  return JSON.parse(data);
}

function loadData(directory) {
  const filePaths = {
    chs: path.join(directory, 'chs.json'),
    cht: path.join(directory, 'cht.json'),
    eng: path.join(directory, 'eng.json')
  };

  return {
    chs: readJSONFile(filePaths.chs),
    cht: readJSONFile(filePaths.cht),
    eng: readJSONFile(filePaths.eng)
  };
}

// generate keys in the format Category/Key
function getNestedKeys(obj, prefix = '') {
  let keys = [];
  for (const key in obj) {
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys = keys.concat(getNestedKeys(obj[key], `${prefix}${key}/`));
    } else {
      keys.push(`${prefix}${key}`);
    }
  }
  return keys;
}

// Create a set of keys from all lang JSON files
function getAllKeys(translations) {
  const { chs, cht, eng } = translations;
  return new Set([...getNestedKeys(chs), ...getNestedKeys(cht), ...getNestedKeys(eng)]);
}

// Create a dict that contains all translations for each key
function createDictionary(translations) {
  const allKeys = getAllKeys(translations);
  const { chs, cht, eng } = translations;
  const word = {};
  allKeys.forEach((key) => {
    word[key] = {
      chs: getValueByKeyPath(chs, key) || null,
      cht: getValueByKeyPath(cht, key) || null,
      eng: getValueByKeyPath(eng, key) || null
    };
  });
  return word;
}

// Helper function to get value by key path (Category/Key)
function getValueByKeyPath(obj, keyPath) {
  const keys = keyPath.split('/');
  let value = obj;
  for (const key of keys) {
    if (typeof value !== 'object' || value[key] === undefined) {
      return null;
    }
    value = value[key];
  }
  return typeof value === 'string' ? value : null;
}

// Generate a dict of missing translations
function findMissingTranslations(word) {
  const missingTranslations = {};
  Object.keys(word).forEach((key) => {
    const missingLocales = [];
    if (word[key].chs === null) {
      missingLocales.push('chs');
    }
    if (word[key].cht === null) {
      missingLocales.push('cht');
    }
    if (word[key].eng === null) {
      missingLocales.push('eng');
    }
    if (missingLocales.length > 0) {
      missingTranslations[key] = missingLocales;
    }
  });
  return missingTranslations;
}

function checkResult(missingTranslations) {
  if (Object.keys(missingTranslations).length === 0) {
    console.log('Ok');
  } else {
    console.log('Failed', missingTranslations);
    process.exit(1);
  }
}

function processDirectory(directory) {
  const translations = loadData(directory);
  const transDict = createDictionary(translations);
  const missingTranslations = findMissingTranslations(transDict);
  checkResult(missingTranslations);
}

function main() {
  const LOCALE_DIRECTORY = path.join(__dirname, '../src/locale');

  const directories = [
    path.join(LOCALE_DIRECTORY, 'common'),
    path.join(LOCALE_DIRECTORY, 'screens'),
    path.join(LOCALE_DIRECTORY, 'TopScreens')
  ];

  directories.forEach((directory) => {
    console.log(`Processing folder: ${path.basename(directory)}`);
    processDirectory(directory);
  });

  console.log('Processing translations: i18n2');
  const translations = {
    chs: readJSONFile(path.join(LOCALE_DIRECTORY, 'i18n2_chs.json')),
    cht: readJSONFile(path.join(LOCALE_DIRECTORY, 'i18n2_cht.json')),
    eng: readJSONFile(path.join(LOCALE_DIRECTORY, 'i18n2_eng.json'))
  };
  const dictionary = createDictionary(translations);
  const missingTranslations = findMissingTranslations(dictionary);
  checkResult(missingTranslations);
}

main();
