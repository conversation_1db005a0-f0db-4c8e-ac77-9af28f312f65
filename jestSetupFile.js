/* eslint-disable no-undef */

import * as Expo from 'expo';

jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock')
);

jest.mock('react-native-keyboard-aware-scroll-view', () => {
  const KeyboardAwareScrollView = require('react-native').ScrollView;
  return { KeyboardAwareScrollView };
});

jest.mock('expo-updates', () => ({
  checkForUpdateAsync: jest.fn(),
  fetchUpdateAsync: jest.fn(),
  reloadAsync: jest.fn()
}));

jest.mock('expo-file-system', () => ({
  getInfoAsync: jest.fn(() => Promise.resolve({ exists: true, isDirectory: false })),
  readAsStringAsync: jest.fn(() => Promise.resolve('file content')),
  documentDirectory: 'mocked/document/directory/',
  readDirectoryAsync: jest.fn(() => Promise.resolve(['file1.jpg', 'file2.jpg'])),
  downloadAsync: jest.fn(() => Promise.resolve({ uri: 'path/to/local/asset' }))
}));

jest.mock('expo-image-manipulator', () => ({
  manipulateAsync: jest.fn()
}));

/* jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn()
  })
})); */
jest.mock('@react-navigation/native', () => {
  return {
    ...jest.requireActual('@react-navigation/native'),
    useNavigation: () => ({
      navigate: jest.fn(),
      addListener: jest.fn(),
      removeListener: jest.fn(),
      dangerouslyGetParent: jest.fn(),
      reset: jest.fn(),
      canGoBack: jest.fn()
    })
  };
});

global.eventEmitter = new Expo.EventEmitter();

jest.mock('expo-clipboard', () => ({
  setStringAsync: jest.fn()
}));

jest.mock('@/hooks/useSetNavigationOptions', () => ({
  useSetNavigationOptions: jest.fn()
}));

require('@shopify/flash-list/jestSetup');

// Expo SDK 52: workaround for bug https://github.com/callstack/react-native-testing-library/issues/1712
jest.mock('expo-font');
