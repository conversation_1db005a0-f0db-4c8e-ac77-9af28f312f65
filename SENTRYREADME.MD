# Sentry is a monitoring and error-tracking tool that helps developers detect and resolve issues in their applications, improving performance. It provides:

1. **Error capture**: Records exception details and context for quick issue localization.
2. **Performance monitoring**: Analyzes application performance to identify bottlenecks.
3. **Real-time alerts**: Notifies developers immediately when issues occur.
4. **Session replay**: Offers video replays of user actions, making issues more visible.
5. **Version tracking**: Monitors issues and performance across different versions.
   Sentry saves debugging time and enhances application stability and user experience.

## How to view a crash stack in Sentry？

1. Open web page https://sentry.io/welcome/, and login to Sentry.
   You need to provide your email address and let the administrator invite you to join the team.
2. In the left menu, click Issues to open the list of issues, which shows all the captured errors and crashes in your application.
   We can see several statuses of issues, Prioritized, For Review, Regressed, Escalating, Archived
3. We can choose your project, We can choose All Envs or development, or production, We can also choose a time range.
4. Click the event name to go to the details page.
5. On the event details page, you’ll find the stack trace, showing function calls, file names, and line numbers.
   We can see user information, operating system information, App information, Device information, etc.

5.1:
In Event Highlights, we can see the basic information of the event, such as handled status,level, release version, environment
Mobile, device, App information, click "view all" to view specific detailed information.

5.2:
In Stack Trace, we can check the Error name, and we can select "Most Relevant" or "Full Stack Trace" to view the calling path of the code.
For example, the following code sentry captures this error.
It can locate the line where the error occurred

```javascript
          <SettingsList.Item
            title={'Test Sentry'}
            titleStyle={{ fontSize }}
            hasNavArrow={false}
            onPress={() => {
              throw new Error('Test, Sentry!');
            }}
          />
        </SettingsList>
```

5.3:
In the Breadcrumbs section, we can see the detailed triggering process of the exception.

5.4:
In the tags section and context section, we can view the required exception information in detail. For example, User information. App information, device information, SDK information, Operating System information, etc.

## How to view exception statistics table in Sentry？

1. Click "Projects" on the left menu, select your project, you can see: Crash Free Sessions, Crash Free Users, Number of Releases,Apdex,Number of Errors etc.
   Abnormal statistical results can be viewed through these fields and data charts.

2. We can also set up an alarm mechanism and adjust it according to parameters such as issues, number of errors, etc.

3. Advanced usage:
   Through the menu on the left, we can set Traces, Profiles, and Replays to help us analyze abnormal situations.
