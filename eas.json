{"cli": {"appVersionSource": "local"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "medium"}, "channel": "development"}, "fdevelopment": {"env": {"APP": "fishMeet"}, "developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "medium"}, "channel": "development"}, "preview": {"distribution": "internal", "ios": {"resourceClass": "medium"}, "android": {"resourceClass": "large"}, "channel": "preview"}, "fpreview": {"env": {"APP": "fishMeet"}, "distribution": "internal", "ios": {"resourceClass": "medium"}, "android": {"resourceClass": "large"}, "channel": "preview"}, "china": {"env": {"Country": "China"}, "ios": {"resourceClass": "medium"}, "android": {"buildType": "apk", "resourceClass": "large"}, "channel": "production"}, "production": {"ios": {"resourceClass": "medium"}, "android": {"buildType": "app-bundle", "resourceClass": "large"}, "channel": "production"}, "fproduction": {"env": {"APP": "fishMeet"}, "ios": {"resourceClass": "medium"}, "android": {"buildType": "app-bundle", "resourceClass": "large"}, "channel": "production"}}, "submit": {"production": {}, "fproduction": {"android": {"track": "internal", "releaseStatus": "draft"}}}}