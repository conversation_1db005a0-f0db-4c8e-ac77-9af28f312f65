diff --git a/node_modules/react-native-receive-sharing-intent/android/build.gradle b/node_modules/react-native-receive-sharing-intent/android/build.gradle
index 02cc8f2..2726157 100644
--- a/node_modules/react-native-receive-sharing-intent/android/build.gradle
+++ b/node_modules/react-native-receive-sharing-intent/android/build.gradle
@@ -8,7 +8,7 @@ buildscript {
   }

   dependencies {
-    classpath 'com.android.tools.build:gradle:3.2.1'
+    classpath 'com.android.tools.build:gradle:7.4.1'
     // noinspection DifferentKotlinGradleVersion
     classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
   }
@@ -26,6 +26,7 @@ def getExtOrIntegerDefault(name) {
 }

 android {
+  namespace "com.reactnativereceivesharingintent"
   compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')
   buildToolsVersion getExtOrDefault('buildToolsVersion')
   defaultConfig {
@@ -33,7 +34,6 @@ android {
     targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
     versionCode 1
     versionName "1.0"
-
   }

   buildTypes {
@@ -45,8 +45,8 @@ android {
     disable 'GradleCompatible'
   }
   compileOptions {
-    sourceCompatibility JavaVersion.VERSION_1_8
-    targetCompatibility JavaVersion.VERSION_1_8
+    sourceCompatibility JavaVersion.VERSION_17
+    targetCompatibility JavaVersion.VERSION_17
   }
 }

diff --git a/node_modules/react-native-receive-sharing-intent/android/gradle.properties b/node_modules/react-native-receive-sharing-intent/android/gradle.properties
index 9303aa4..2ff63dd 100644
--- a/node_modules/react-native-receive-sharing-intent/android/gradle.properties
+++ b/node_modules/react-native-receive-sharing-intent/android/gradle.properties
@@ -1,4 +1,5 @@
-ReceiveSharingIntent_kotlinVersion=1.3.50
-ReceiveSharingIntent_compileSdkVersion=29
-ReceiveSharingIntent_buildToolsVersion=29.0.2
-ReceiveSharingIntent_targetSdkVersion=29
+ReceiveSharingIntent_kotlinVersion=1.6.10
+ReceiveSharingIntent_compileSdkVersion=33
+ReceiveSharingIntent_buildToolsVersion=33.0.1
+ReceiveSharingIntent_targetSdkVersion=33
+android.useAndroidX=true
diff --git a/node_modules/react-native-receive-sharing-intent/android/src/main/AndroidManifest.xml b/node_modules/react-native-receive-sharing-intent/android/src/main/AndroidManifest.xml
index d19929a..0a0938a 100644
--- a/node_modules/react-native-receive-sharing-intent/android/src/main/AndroidManifest.xml
+++ b/node_modules/react-native-receive-sharing-intent/android/src/main/AndroidManifest.xml
@@ -1,4 +1,3 @@
-<manifest xmlns:android="http://schemas.android.com/apk/res/android"
-          package="com.reactnativereceivesharingintent">
+<manifest xmlns:android="http://schemas.android.com/apk/res/android">

 </manifest>
diff --git a/node_modules/react-native-receive-sharing-intent/android/src/main/java/com/reactnativereceivesharingintent/ReceiveSharingIntentModule.java b/node_modules/react-native-receive-sharing-intent/android/src/main/java/com/reactnativereceivesharingintent/ReceiveSharingIntentModule.java
index f752144..725918a 100644
--- a/node_modules/react-native-receive-sharing-intent/android/src/main/java/com/reactnativereceivesharingintent/ReceiveSharingIntentModule.java
+++ b/node_modules/react-native-receive-sharing-intent/android/src/main/java/com/reactnativereceivesharingintent/ReceiveSharingIntentModule.java
@@ -18,6 +18,7 @@ public class ReceiveSharingIntentModule extends ReactContextBaseJavaModule {

   private final ReactApplicationContext reactContext;
   private ReceiveSharingIntentHelper receiveSharingIntentHelper;
+  private Intent oldIntent;

   public ReceiveSharingIntentModule(ReactApplicationContext reactContext) {
     super(reactContext);
@@ -30,6 +31,7 @@ public class ReceiveSharingIntentModule extends ReactContextBaseJavaModule {
   protected void onNewIntent(Intent intent) {
     Activity mActivity = getCurrentActivity();
     if(mActivity == null) { return; }
+    oldIntent = mActivity.getIntent();
     mActivity.setIntent(intent);
   }

@@ -40,7 +42,9 @@ public class ReceiveSharingIntentModule extends ReactContextBaseJavaModule {
     if(mActivity == null) { return; }
     Intent intent = mActivity.getIntent();
     receiveSharingIntentHelper.sendFileNames(reactContext, intent, promise);
-    mActivity.setIntent(null);
+    if (oldIntent != null) {
+      mActivity.setIntent(oldIntent);
+    }
   }

   @ReactMethod
diff --git a/node_modules/react-native-receive-sharing-intent/src/ReceiveSharingIntent.ts b/node_modules/react-native-receive-sharing-intent/src/ReceiveSharingIntent.ts
index 735c191..91dab4b 100644
--- a/node_modules/react-native-receive-sharing-intent/src/ReceiveSharingIntent.ts
+++ b/node_modules/react-native-receive-sharing-intent/src/ReceiveSharingIntent.ts
@@ -33,7 +33,7 @@ class ReceiveSharingIntentModule implements IReceiveSharingIntent {
     }

     clearReceivedFiles(){
-        this.isClear = true;
+        ReceiveSharingIntent.clearFileNames();
     }


