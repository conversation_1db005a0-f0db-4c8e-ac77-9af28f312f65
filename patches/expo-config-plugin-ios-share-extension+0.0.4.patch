diff --git a/node_modules/expo-config-plugin-ios-share-extension/build/constants.d.ts b/node_modules/expo-config-plugin-ios-share-extension/build/constants.d.ts
index 5a6b294d..a019a810 100644
--- a/node_modules/expo-config-plugin-ios-share-extension/build/constants.d.ts
+++ b/node_modules/expo-config-plugin-ios-share-extension/build/constants.d.ts
@@ -3,5 +3,10 @@ export declare const shareExtensionInfoFileName: string;
 export declare const shareExtensionEntitlementsFileName: string;
 export declare const shareExtensionStoryBoardFileName = "MainInterface.storyboard";
 export declare const shareExtensionViewControllerFileName = "ShareViewController.swift";
+export declare type Parameters = {
+    activationRules?: {
+        [key: string]: number | boolean | string;
+    };
+};
 export declare const getAppGroups: (identifier: string) => string[];
 export declare const getShareExtensionBundledIdentifier: (appIdentifier: string) => string;
diff --git a/node_modules/expo-config-plugin-ios-share-extension/build/index.d.ts b/node_modules/expo-config-plugin-ios-share-extension/build/index.d.ts
index f4db5182..1097d4d4 100644
--- a/node_modules/expo-config-plugin-ios-share-extension/build/index.d.ts
+++ b/node_modules/expo-config-plugin-ios-share-extension/build/index.d.ts
@@ -1,3 +1,4 @@
 import { ConfigPlugin } from "@expo/config-plugins";
-declare const withShareMenu: ConfigPlugin;
+import { Parameters } from "./constants";
+declare const withShareMenu: ConfigPlugin<Parameters>;
 export default withShareMenu;
diff --git a/node_modules/expo-config-plugin-ios-share-extension/build/index.js b/node_modules/expo-config-plugin-ios-share-extension/build/index.js
index 85216022..24123f11 100644
--- a/node_modules/expo-config-plugin-ios-share-extension/build/index.js
+++ b/node_modules/expo-config-plugin-ios-share-extension/build/index.js
@@ -8,11 +8,11 @@ let pkg = {
     name: "expo-config-plugin-ios-share-extension",
     version: "UNVERSIONED",
 };
-const withShareMenu = (0, config_plugins_1.createRunOncePlugin)((config) => {
+const withShareMenu = (0, config_plugins_1.createRunOncePlugin)((config, params) => {
     return (0, config_plugins_1.withPlugins)(config, [
         withAppEntitlements_1.withAppEntitlements,
         withShareExtensionConfig_1.withShareExtensionConfig,
-        withShareExtensionXcodeTarget_1.withShareExtensionXcodeTarget,
+        () => (0, withShareExtensionXcodeTarget_1.withShareExtensionXcodeTarget)(config, params),
     ]);
 }, pkg.name, pkg.version);
 exports.default = withShareMenu;
diff --git a/node_modules/expo-config-plugin-ios-share-extension/build/withShareExtensionXcodeTarget.d.ts b/node_modules/expo-config-plugin-ios-share-extension/build/withShareExtensionXcodeTarget.d.ts
index d5e1e31e..c34879d5 100644
--- a/node_modules/expo-config-plugin-ios-share-extension/build/withShareExtensionXcodeTarget.d.ts
+++ b/node_modules/expo-config-plugin-ios-share-extension/build/withShareExtensionXcodeTarget.d.ts
@@ -1,2 +1,3 @@
 import { ConfigPlugin } from '@expo/config-plugins';
-export declare const withShareExtensionXcodeTarget: ConfigPlugin;
+import { Parameters } from './constants';
+export declare const withShareExtensionXcodeTarget: ConfigPlugin<Parameters>;
diff --git a/node_modules/expo-config-plugin-ios-share-extension/build/withShareExtensionXcodeTarget.js b/node_modules/expo-config-plugin-ios-share-extension/build/withShareExtensionXcodeTarget.js
index d5d454db..7cc07f7b 100644
--- a/node_modules/expo-config-plugin-ios-share-extension/build/withShareExtensionXcodeTarget.js
+++ b/node_modules/expo-config-plugin-ios-share-extension/build/withShareExtensionXcodeTarget.js
@@ -4,7 +4,7 @@ exports.withShareExtensionXcodeTarget = void 0;
 const config_plugins_1 = require("@expo/config-plugins");
 const constants_1 = require("./constants");
 const writeShareExtensionFiles_1 = require("./writeShareExtensionFiles");
-const withShareExtensionXcodeTarget = (config) => {
+const withShareExtensionXcodeTarget = (config, parameters) => {
     return (0, config_plugins_1.withXcodeProject)(config, async (config) => {
         var _a;
         const extensionName = constants_1.shareExtensionName;
@@ -18,7 +18,7 @@ const withShareExtensionXcodeTarget = (config) => {
         const entitlementsFilePath = (0, writeShareExtensionFiles_1.getShareExtensionEntitlementsFilePath)(platformProjectRoot);
         const viewControllerFilePath = (0, writeShareExtensionFiles_1.getShareExtensionViewControllerPath)(platformProjectRoot);
         const storyboardFilePath = (0, writeShareExtensionFiles_1.getShareExtensionStoryboardFilePath)(platformProjectRoot);
-        await (0, writeShareExtensionFiles_1.writeShareExtensionFiles)(platformProjectRoot, scheme, appIdentifier);
+        await (0, writeShareExtensionFiles_1.writeShareExtensionFiles)(platformProjectRoot, scheme, appIdentifier, parameters);
         const pbxProject = config.modResults;
         const target = pbxProject.addTarget(extensionName, 'app_extension', extensionName);
         // Add a new PBXSourcesBuildPhase for our ShareViewController
diff --git a/node_modules/expo-config-plugin-ios-share-extension/build/writeShareExtensionFiles.d.ts b/node_modules/expo-config-plugin-ios-share-extension/build/writeShareExtensionFiles.d.ts
index 6e70face..81164d8c 100644
--- a/node_modules/expo-config-plugin-ios-share-extension/build/writeShareExtensionFiles.d.ts
+++ b/node_modules/expo-config-plugin-ios-share-extension/build/writeShareExtensionFiles.d.ts
@@ -1,12 +1,13 @@
-export declare function writeShareExtensionFiles(platformProjectRoot: string, scheme: string, appIdentifier: string): Promise<void>;
+import { Parameters } from "./constants";
+export declare function writeShareExtensionFiles(platformProjectRoot: string, scheme: string, appIdentifier: string, parameters: Parameters): Promise<void>;
 export declare function getShareExtensionEntitlementsFilePath(platformProjectRoot: string): string;
 export declare function getShareExtensionEntitlements(appIdentifier: string): {
     "com.apple.security.application-groups": string[];
 };
 export declare function getShareExtensionEntitlementsContent(appIdentifier: string): string;
 export declare function getShareExtensionInfoFilePath(platformProjectRoot: string): string;
-export declare function getShareExtensionInfoContent(): string;
+export declare function getShareExtensionInfoContent(activationRules: Parameters["activationRules"]): string;
 export declare function getShareExtensionStoryboardFilePath(platformProjectRoot: string): string;
 export declare function getShareExtensionStoryBoardContent(): string;
 export declare function getShareExtensionViewControllerPath(platformProjectRoot: string): string;
-export declare function getShareExtensionViewControllerContent(scheme: string): string;
+export declare function getShareExtensionViewControllerContent(scheme: string, appIdentifier: string): string;
diff --git a/node_modules/expo-config-plugin-ios-share-extension/build/writeShareExtensionFiles.js b/node_modules/expo-config-plugin-ios-share-extension/build/writeShareExtensionFiles.js
index 5f0e1db8..9e869556 100644
--- a/node_modules/expo-config-plugin-ios-share-extension/build/writeShareExtensionFiles.js
+++ b/node_modules/expo-config-plugin-ios-share-extension/build/writeShareExtensionFiles.js
@@ -8,9 +8,9 @@ const node_path_1 = __importDefault(require("node:path"));
 const node_fs_1 = __importDefault(require("node:fs"));
 const plist_1 = __importDefault(require("@expo/plist"));
 const constants_1 = require("./constants");
-async function writeShareExtensionFiles(platformProjectRoot, scheme, appIdentifier) {
+async function writeShareExtensionFiles(platformProjectRoot, scheme, appIdentifier, parameters) {
     const infoPlistFilePath = getShareExtensionInfoFilePath(platformProjectRoot);
-    const infoPlistContent = getShareExtensionInfoContent();
+    const infoPlistContent = getShareExtensionInfoContent(parameters.activationRules);
     await node_fs_1.default.promises.mkdir(node_path_1.default.dirname(infoPlistFilePath), { recursive: true });
     await node_fs_1.default.promises.writeFile(infoPlistFilePath, infoPlistContent);
     const entitlementsFilePath = getShareExtensionEntitlementsFilePath(platformProjectRoot);
@@ -20,7 +20,7 @@ async function writeShareExtensionFiles(platformProjectRoot, scheme, appIdentifi
     const storyboardContent = getShareExtensionStoryBoardContent();
     await node_fs_1.default.promises.writeFile(storyboardFilePath, storyboardContent);
     const viewControllerFilePath = getShareExtensionViewControllerPath(platformProjectRoot);
-    const viewControllerContent = getShareExtensionViewControllerContent(scheme);
+    const viewControllerContent = getShareExtensionViewControllerContent(scheme, appIdentifier);
     await node_fs_1.default.promises.writeFile(viewControllerFilePath, viewControllerContent);
 }
 exports.writeShareExtensionFiles = writeShareExtensionFiles;
@@ -44,10 +44,10 @@ function getShareExtensionInfoFilePath(platformProjectRoot) {
     return node_path_1.default.join(platformProjectRoot, constants_1.shareExtensionName, constants_1.shareExtensionInfoFileName);
 }
 exports.getShareExtensionInfoFilePath = getShareExtensionInfoFilePath;
-function getShareExtensionInfoContent() {
+function getShareExtensionInfoContent(activationRules) {
     return plist_1.default.build({
         CFBundleName: "$(PRODUCT_NAME)",
-        CFBundleDisplayName: "Share Extension",
+        CFBundleDisplayName: "$(PRODUCT_NAME) Share Extension",
         CFBundleIdentifier: "$(PRODUCT_BUNDLE_IDENTIFIER)",
         CFBundleDevelopmentRegion: "$(DEVELOPMENT_LANGUAGE)",
         CFBundleExecutable: "$(EXECUTABLE_NAME)",
@@ -55,7 +55,7 @@ function getShareExtensionInfoContent() {
         CFBundlePackageType: "$(PRODUCT_BUNDLE_PACKAGE_TYPE)",
         NSExtension: {
             NSExtensionAttributes: {
-                NSExtensionActivationRule: {
+                NSExtensionActivationRule: activationRules || {
                     NSExtensionActivationSupportsWebURLWithMaxCount: 1,
                     NSExtensionActivationSupportsWebPageWithMaxCount: 1,
                 },
@@ -104,131 +104,359 @@ function getShareExtensionViewControllerPath(platformProjectRoot) {
     return node_path_1.default.join(platformProjectRoot, constants_1.shareExtensionName, constants_1.shareExtensionViewControllerFileName);
 }
 exports.getShareExtensionViewControllerPath = getShareExtensionViewControllerPath;
-function getShareExtensionViewControllerContent(scheme) {
-    console.debug("************ scheme", scheme);
-    return `import UIKit
-import Social
-import MobileCoreServices
+function getShareExtensionViewControllerContent(scheme, appIdentifier) {
+  console.debug("************ scheme", scheme, "appIdentifier", appIdentifier);
+  return `import UIKit
+  import Social
+  import MobileCoreServices
+  import Photos
 
-class ShareViewController: UIViewController {
-    // IMPORTANT: This should be your host app scheme
-    let hostAppURLScheme = "${scheme}"
-    let urlContentType = kUTTypeURL as String
-    let textContentType = kUTTypePlainText as String
-    
-    override func viewDidLoad() {
-        
-        var strUrl:String? = nil
-        DispatchQueue.global().async {
-            
-            
-            if let content = self.extensionContext!.inputItems[0] as? NSExtensionItem {
-                if let contents = content.attachments {
-                    for (_, attachment) in (contents).enumerated() {
-                        if attachment.hasItemConformingToTypeIdentifier(self.urlContentType) {
-                            let _strUrl = self.getStrUrlFromUrl(attachment: attachment)
-                            if(_strUrl != nil){
-                                strUrl = _strUrl
-                            }
-                        }
-                        
-                        if attachment.hasItemConformingToTypeIdentifier(self.textContentType) {
-                            if(strUrl != nil){
-                                continue;
-                            }
-                            let    _strUrl = self.getStrUrlFromText(attachment: attachment)
-                            if(_strUrl != nil){
-                                strUrl = _strUrl
-                            }
-                            
-                        }
-                    }
-                }
-            }
-            
-            if(strUrl == nil){
-                self.dismissWithError()
-                return;
-            }
-            self.redirectToHostApp(sharedURL: strUrl!)
-        }
-    }
-    
-    
-    private func getStrUrlFromUrl ( attachment: NSItemProvider) -> String? {
-        var result:String? = nil
-        
-        let semaphore = DispatchSemaphore(value: 0)
-        
-        attachment.loadItem(forTypeIdentifier: self.urlContentType, options: nil) { data, error in
-            
-            if error == nil, let item = data as? URL {
-                let _url = URL(string: item.absoluteString)
-                if(_url != nil){
-                    result = item.absoluteString
-                }
-            }
-            semaphore.signal()
-        }
-        
-        semaphore.wait()
-        
-        return result
-    }
-    
-    private func getStrUrlFromText ( attachment: NSItemProvider) -> String? {
-        var result:String? = nil
-        
-        let semaphore = DispatchSemaphore(value: 0)
-        
-        attachment.loadItem(forTypeIdentifier: self.textContentType, options: nil) { data, error in
-            
-            if error == nil, let item = data as? String {
-                
-                let types: NSTextCheckingResult.CheckingType = [.link]
-                let detector = try? NSDataDetector(types: types.rawValue)
-                
-                if  detector != nil && item.count > 0 && detector!.numberOfMatches(in: item, options: NSRegularExpression.MatchingOptions(rawValue: 0), range: NSMakeRange(0, item.count)) > 0 {
-                    result = item
-                }
-                
-            }
-            semaphore.signal()
+  class ShareViewController: SLComposeServiceViewController {
+   let hostAppBundleIdentifier = "${appIdentifier}"
+   let shareProtocol = "${scheme}"
+   let sharedKey = "${scheme}ShareKey"
+   var sharedMedia: [SharedMediaFile] = []
+   var sharedText: [String] = []
+   let imageContentType = kUTTypeImage as String
+   let videoContentType = kUTTypeMovie as String
+   let textContentType = kUTTypeText as String
+   let urlContentType = kUTTypeURL as String
+   let fileURLType = kUTTypeFileURL as String;
+  
+   override func isContentValid() -> Bool {
+     return true
+   }
+      
+   override func viewDidLoad() {
+         super.viewDidLoad();
+     }
+      
+   override func viewDidAppear(_ animated: Bool) {
+           super.viewDidAppear(animated)
+          
+     if let content = extensionContext!.inputItems[0] as? NSExtensionItem {
+       if let contents = content.attachments {
+         for (index, attachment) in (contents).enumerated() {
+           if attachment.hasItemConformingToTypeIdentifier(imageContentType) {
+             handleImages(content: content, attachment: attachment, index: index)
+           } else if attachment.hasItemConformingToTypeIdentifier(textContentType) {
+             handleText(content: content, attachment: attachment, index: index)
+           } else if attachment.hasItemConformingToTypeIdentifier(fileURLType) {
+             handleFiles(content: content, attachment: attachment, index: index)
+           } else if attachment.hasItemConformingToTypeIdentifier(urlContentType) {
+             handleUrl(content: content, attachment: attachment, index: index)
+           } else if attachment.hasItemConformingToTypeIdentifier(videoContentType) {
+             handleVideos(content: content, attachment: attachment, index: index)
+           }
+         }
+       }
+     }
+   }
+      
+   override func didSelectPost() {
+         print("didSelectPost");
+     }
+      
+   override func configurationItems() -> [Any]! {
+     // To add configuration options via table cells at the bottom of the sheet, return an array of SLComposeSheetConfigurationItem here.
+     return []
+   }
+  
+   private func handleText (content: NSExtensionItem, attachment: NSItemProvider, index: Int) {
+     attachment.loadItem(forTypeIdentifier: textContentType, options: nil) { [weak self] data, error in
+      
+      if let this = self {
+        if error != nil {
+          self?.dismissWithError()
+          return
         }
         
-        semaphore.wait()
-        
-        
-        return result
-    }    
-    
-    private func dismissWithError() {
-        self.dismiss(animated: true, completion: nil)
-        extensionContext!.completeRequest(returningItems: [], completionHandler: nil)
-    }
-    
-    private func redirectToHostApp(sharedURL: String) {
-        var urlComponents = URLComponents()
-        urlComponents.scheme = hostAppURLScheme
-        urlComponents.host = "share"
-        urlComponents.path = "/"
-        urlComponents.queryItems = [
-            URLQueryItem(name: "url", value: sharedURL),
-        ]
-        // urlComponents.url: \(scheme)://share/?url=\(sharedURL)
-        let url = urlComponents.url
-        var responder = self as UIResponder?
-        let selectorOpenURL = sel_registerName("openURL:")
-        
-        while (responder != nil) {
-            if (responder?.responds(to: selectorOpenURL))! {
-                responder?.perform(selectorOpenURL, with: url)
-            }
-            responder = responder!.next
+        if let url = data as? URL {
+          // Handle the case where the item is a URL
+          let fileExtension = url.pathExtension.lowercased()
+          if fileExtension == "txt" || fileExtension == "text" {
+            // Handle .txt file
+            self?.handleFiles(content: content, attachment: attachment, index: index)
+          } else {
+            // Handle other file types or text content
+            this.dismissWithError()
+          }
+        } else if let item = data as? String {
+          // Handle plain text content
+          this.sharedText.append(item)
+          
+          if index == (content.attachments?.count)! - 1 {
+            let userDefaults = UserDefaults(suiteName: "group.\\(this.hostAppBundleIdentifier)")
+            userDefaults?.set(this.sharedText, forKey: this.sharedKey)
+            userDefaults?.synchronize()
+            this.redirectToHostApp(type: .text)
+          }
+        } else {
+          this.dismissWithError()
         }
-        extensionContext!.completeRequest(returningItems: [], completionHandler: nil)
+      }
     }
-}
+   }
+              
+   private func handleUrl (content: NSExtensionItem, attachment: NSItemProvider, index: Int) {
+     attachment.loadItem(forTypeIdentifier: urlContentType, options: nil) { [weak self] data, error in
+              
+       if error == nil, let item = data as? URL, let this = self {
+      
+         this.sharedText.append(item.absoluteString)
+      
+         // If this is the last item, save imagesData in userDefaults and redirect to host app
+         if index == (content.attachments?.count)! - 1 {
+           let userDefaults = UserDefaults(suiteName: "group.\\(this.hostAppBundleIdentifier)")
+           userDefaults?.set(this.sharedText, forKey: this.sharedKey)
+           userDefaults?.synchronize()
+           this.redirectToHostApp(type: .text)
+         }
+      
+       } else {
+         self?.dismissWithError()
+       }
+     }
+   }
+   
+   private func handleImages (content: NSExtensionItem, attachment: NSItemProvider, index: Int) {
+     attachment.loadItem(forTypeIdentifier: imageContentType, options: nil) { [weak self] data, error in
+       
+       if error == nil, let url = data as? URL, let this = self {
+         //  this.redirectToHostApp(type: .media)
+         // Always copy
+         let fileExtension = this.getExtension(from: url, type: .image)
+         let newName = UUID().uuidString
+         let newPath = FileManager.default
+           .containerURL(forSecurityApplicationGroupIdentifier: "group.\\(this.hostAppBundleIdentifier)")!
+           .appendingPathComponent("\\(newName).\\(fileExtension)")
+         let copied = this.copyFile(at: url, to: newPath)
+         if(copied) {
+           this.sharedMedia.append(SharedMediaFile(path: newPath.absoluteString, thumbnail: nil, duration: nil, type: .image))
+         }
+         
+         // If this is the last item, save imagesData in userDefaults and redirect to host app
+         if index == (content.attachments?.count)! - 1 {
+           let userDefaults = UserDefaults(suiteName: "group.\\(this.hostAppBundleIdentifier)")
+           userDefaults?.set(this.toData(data: this.sharedMedia), forKey: this.sharedKey)
+           userDefaults?.synchronize()
+           this.redirectToHostApp(type: .media)
+         }
+         
+       } else {
+         self?.dismissWithError()
+       }
+     }
+   }
+   
+   private func handleVideos (content: NSExtensionItem, attachment: NSItemProvider, index: Int) {
+     attachment.loadItem(forTypeIdentifier: videoContentType, options:nil) { [weak self] data, error in
+       
+       if error == nil, let url = data as? URL, let this = self {
+         
+         // Always copy
+         let fileExtension = this.getExtension(from: url, type: .video)
+         let newName = UUID().uuidString
+         let newPath = FileManager.default
+           .containerURL(forSecurityApplicationGroupIdentifier: "group.\\(this.hostAppBundleIdentifier)")!
+           .appendingPathComponent("\\(newName).\\(fileExtension)")
+         let copied = this.copyFile(at: url, to: newPath)
+         if(copied) {
+           guard let sharedFile = this.getSharedMediaFile(forVideo: newPath) else {
+             return
+           }
+           this.sharedMedia.append(sharedFile)
+         }
+  
+         // If this is the last item, save imagesData in userDefaults and redirect to host app
+         if index == (content.attachments?.count)! - 1 {
+           let userDefaults = UserDefaults(suiteName: "group.\\(this.hostAppBundleIdentifier)")
+           userDefaults?.set(this.toData(data: this.sharedMedia), forKey: this.sharedKey)
+           userDefaults?.synchronize()
+           this.redirectToHostApp(type: .media)
+         }
+         
+       } else {
+         self?.dismissWithError()
+       }
+     }
+   }
+   
+   private func handleFiles (content: NSExtensionItem, attachment: NSItemProvider, index: Int) {
+     attachment.loadItem(forTypeIdentifier: fileURLType, options: nil) { [weak self] data, error in
+       
+       if error == nil, let url = data as? URL, let this = self {
+         
+         // Always copy
+         let newName = this.getFileName(from :url)
+         let newPath = FileManager.default
+           .containerURL(forSecurityApplicationGroupIdentifier: "group.\\(this.hostAppBundleIdentifier)")!
+           .appendingPathComponent("\\(newName)")
+         let copied = this.copyFile(at: url, to: newPath)
+         if (copied) {
+           this.sharedMedia.append(SharedMediaFile(path: newPath.absoluteString, thumbnail: nil, duration: nil, type: .file))
+         }
+         
+         if index == (content.attachments?.count)! - 1 {
+           let userDefaults = UserDefaults(suiteName: "group.\\(this.hostAppBundleIdentifier)")
+           userDefaults?.set(this.toData(data: this.sharedMedia), forKey: this.sharedKey)
+           userDefaults?.synchronize()
+           this.redirectToHostApp(type: .file)
+         }
+         
+       } else {
+         self?.dismissWithError()
+       }
+     }
+   }
+   
+   private func dismissWithError() {
+     print("[ERROR] Error loading data!")
+     let alert = UIAlertController(title: "Error", message: "Error loading data", preferredStyle: .alert)
+     
+     let action = UIAlertAction(title: "Error", style: .cancel) { _ in
+       self.dismiss(animated: true, completion: nil)
+     }
+     
+     alert.addAction(action)
+     present(alert, animated: true, completion: nil)
+     extensionContext!.completeRequest(returningItems: [], completionHandler: nil)
+   }
+   
+   private func redirectToHostApp(type: RedirectType) {
+     let url = URL(string: "\\(shareProtocol)://dataUrl=\\(sharedKey)#\\(type)")
+     var responder = self as UIResponder?
+     let selectorOpenURL = sel_registerName("openURL:")
+  
+     while (responder != nil) {
+       if (responder?.responds(to: selectorOpenURL))! {
+         let _ = responder?.perform(selectorOpenURL, with: url)
+       }
+       responder = responder!.next
+     }
+     extensionContext!.completeRequest(returningItems: [], completionHandler: nil)
+   }
+   
+   enum RedirectType {
+     case media
+     case text
+     case file
+   }
+   
+   func getExtension(from url: URL, type: SharedMediaType) -> String {
+     let parts = url.lastPathComponent.components(separatedBy: ".")
+     var ex: String? = nil
+     if (parts.count > 1) {
+       ex = parts.last
+     }
+     
+     if (ex == nil) {
+       switch type {
+       case .image:
+         ex = "PNG"
+       case .video:
+         ex = "MP4"
+       case .file:
+         ex = "TXT"
+       }
+     }
+     return ex ?? "Unknown"
+   }
+   
+   func getFileName(from url: URL) -> String {
+     var name = url.lastPathComponent
+     
+     if (name == "") {
+       name = UUID().uuidString + "." + getExtension(from: url, type: .file)
+     }
+     
+     return name
+   }
+   
+   func copyFile(at srcURL: URL, to dstURL: URL) -> Bool {
+     do {
+       if FileManager.default.fileExists(atPath: dstURL.path) {
+         try FileManager.default.removeItem(at: dstURL)
+       }
+       try FileManager.default.copyItem(at: srcURL, to: dstURL)
+     } catch (let error) {
+       print("Cannot copy item at \\(srcURL) to \\(dstURL): \\(error)")
+       return false
+     }
+     return true
+   }
+   
+   private func getSharedMediaFile(forVideo: URL) -> SharedMediaFile? {
+     let asset = AVAsset(url: forVideo)
+     let duration = (CMTimeGetSeconds(asset.duration) * 1000).rounded()
+     let thumbnailPath = getThumbnailPath(for: forVideo)
+     
+     if FileManager.default.fileExists(atPath: thumbnailPath.path) {
+       return SharedMediaFile(path: forVideo.absoluteString, thumbnail: thumbnailPath.absoluteString, duration: duration, type: .video)
+     }
+     
+     var saved = false
+     let assetImgGenerate = AVAssetImageGenerator(asset: asset)
+     assetImgGenerate.appliesPreferredTrackTransform = true
+     //        let scale = UIScreen.main.scale
+     assetImgGenerate.maximumSize =  CGSize(width: 360, height: 360)
+     do {
+       let img = try assetImgGenerate.copyCGImage(at: CMTimeMakeWithSeconds(600, preferredTimescale: Int32(1.0)), actualTime: nil)
+       try UIImage.pngData(UIImage(cgImage: img))()?.write(to: thumbnailPath)
+       saved = true
+     } catch {
+       saved = false
+     }
+     
+     return saved ? SharedMediaFile(path: forVideo.absoluteString, thumbnail: thumbnailPath.absoluteString, duration: duration, type: .video) : nil
+     
+   }
+   
+   private func getThumbnailPath(for url: URL) -> URL {
+     let fileName = Data(url.lastPathComponent.utf8).base64EncodedString().replacingOccurrences(of: "==", with: "")
+     let path = FileManager.default
+       .containerURL(forSecurityApplicationGroupIdentifier: "group.\\(hostAppBundleIdentifier)")!
+       .appendingPathComponent("\\(fileName).jpg")
+     return path
+   }
+   
+   class SharedMediaFile: Codable {
+     var path: String; // can be image, video or url path. It can also be text content
+     var thumbnail: String?; // video thumbnail
+     var duration: Double?; // video duration in milliseconds
+     var type: SharedMediaType;
+     
+     
+     init(path: String, thumbnail: String?, duration: Double?, type: SharedMediaType) {
+       self.path = path
+       self.thumbnail = thumbnail
+       self.duration = duration
+       self.type = type
+     }
+     
+     // Debug method to print out SharedMediaFile details in the console
+     func toString() {
+       print("[SharedMediaFile] path: \\(self.path)thumbnail: \\(self.thumbnail)duration: \\(self.duration)type: \\(self.type)")
+     }
+   }
+   
+   enum SharedMediaType: Int, Codable {
+     case image
+     case video
+     case file
+   }
+   
+   func toData(data: [SharedMediaFile]) -> Data {
+     let encodedData = try? JSONEncoder().encode(data)
+     return encodedData!
+   }
+  }
+  
+  extension Array {
+   subscript (safe index: UInt) -> Element? {
+     return Int(index) < count ? self[Int(index)] : nil
+   }
+  }
 `;
 }
 exports.getShareExtensionViewControllerContent = getShareExtensionViewControllerContent;
